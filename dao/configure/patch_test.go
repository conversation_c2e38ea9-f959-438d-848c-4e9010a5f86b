package configure

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/configure"
)

func aTestPatchDaoImpl_CreatePatch(t *testing.T) {
	patch := configure.Patch{}
	patchDao, _ := NewPatchDao(nil, context.TODO())
	id, err := patchDao.CreatePatch(&patch)
	if err != nil {
		t.<PERSON>rf("PatchDaoImpl CreatePatch fail,as[%s]", err.Error())
	} else {
		t.Log("PatchDaoImpl CreatePatch success, id=", id)
	}
}

func aTestPatchDaoImpl_DeletePatch(t *testing.T) {
	patchDao, _ := NewPatchDao(nil, context.TODO())
	affected, err := patchDao.DeletePatch(1)
	if err != nil {
		t.Errorf("PatchDaoImpl DeletePatch fail,as[%s]", err.Error())
	} else {
		t.Log("PatchDaoImpl DeletePatch success, affected=", affected)
	}
}

func aTestPatchDaoImpl_UpdatePatch(t *testing.T) {
	patch := configure.Patch{}
	patchDao, _ := NewPatchDao(nil, context.TODO())
	affected, err := patchDao.UpdatePatch(&patch)
	if err != nil {
		t.Errorf("PatchDaoImpl UpdatePatch fail,as[%s]", err.Error())
	} else {
		t.Log("PatchDaoImpl UpdatePatch success, affected=", affected)
	}
}

func aTestPatchDaoImpl_ReadPatchPage(t *testing.T) {
	patchDao, _ := NewPatchDao(nil, context.TODO())
	patchs, err := patchDao.ReadPatchPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("PatchDaoImpl ReadPatchPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(patchs)
		t.Log("PatchDaoImpl ReadPatchPage success, data=", string(bytes))
	}
}

func aTestPatchDaoImpl_FindAll(t *testing.T) {
	patchDao, _ := NewPatchDao(nil, context.TODO())
	patchs, err := patchDao.FindAll()
	if err != nil {
		t.Errorf("PatchDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(patchs)
		t.Log("PatchDaoImpl FindAll success, data=", string(bytes))
	}
}

func aTestPatchDaoImpl_ReadPatch(t *testing.T) {
	patchDao, _ := NewPatchDao(nil, context.TODO())
	patch, err := patchDao.ReadPatch(1)
	if err != nil {
		t.Errorf("PatchDaoImpl ReadPatch fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(patch)
		t.Log("PatchDaoImpl ReadPatch success, data=", string(bytes))
	}
}
