package configure

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/configure"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

// Data Access Object for configure.Stack
type StackDaoImpl struct {
	database.DaoImpl
}

// new a data access object for stack
func NewStackDao(session *xorm.Session, ctx context.Context) (*StackDaoImpl, error) {
	stackDao := StackDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(configure.Stack)
	if err := stackDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	stackDao.NewSlice = func() interface{} {
		slice := make([]configure.Stack, 0)
		return &slice
	}

	return &stackDao, nil
}

func (d *StackDaoImpl) CreateStack(stack *configure.Stack) (int64, error) {
	affected, err := d.Create(stack)
	if err != nil {
		log.Errorf(d.Ctx, "StackDaoImpl CreateStack [%v] fail,as:%s", stack, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *StackDaoImpl) DeleteStack(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "StackDaoImpl DeleteStack [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *StackDaoImpl) UpdateStack(stack *configure.Stack, fields ...string) (int64, error) {
	if stack == nil {
		return 0, fmt.Errorf("invalid paramter, stack=nil")
	}

	affected, err := d.Update(stack.ID, stack, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "StackDaoImpl UpdateStack [%v] fail,as:%s", stack.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *StackDaoImpl) FindAll() ([]configure.Stack, error) {
	var stacks []configure.Stack
	err := d.SortListBy(&stacks, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "StackDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return stacks, nil
}

func (d *StackDaoImpl) ReadStack(stackId uint64) (*configure.Stack, error) {
	var stack configure.Stack
	exist, err := d.FindById(stackId, &stack)
	if err != nil {
		log.Errorf(d.Ctx, "StackDaoImpl Read [%d] fail,as:%s", stackId, err.Error())
		return nil, err
	} else if exist {
		return &stack, nil
	} else {
		log.Debugf(d.Ctx, "StackDaoImpl the stack id=[%d] not exist", stackId)
		return nil, fmt.Errorf("the stack [%d] not exist", stackId)
	}
}

func (d *StackDaoImpl) ReadStackPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "StackDaoImpl ReadStackPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *StackDaoImpl) ReadRecordPageByFilter(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "StackDaoImpl FindStackPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *StackDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]configure.StackInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "StackDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *StackDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]configure.StackInfo, error) {
	records := make([]configure.StackInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "StackDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *StackDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*configure.StackInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(configure.StackInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "StackDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "StackDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *StackDaoImpl) ReadByFilter(filter information.IFilter) ([]configure.Stack, error) {
	records := make([]configure.Stack, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "StackDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *StackDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *StackDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *StackDaoImpl) Entity() information.IRecord {
	return new(configure.Stack)
}

func (d *StackDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(configure.StackFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
