package configure

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/configure"
)

func aTestStackDaoImpl_CreateStack(t *testing.T) {
	stack := configure.Stack{}
	stackDao, _ := NewStackDao(nil, context.TODO())
	id, err := stackDao.CreateStack(&stack)
	if err != nil {
		t.<PERSON>("StackDaoImpl CreateStack fail,as[%s]", err.Error())
	} else {
		t.Log("StackDaoImpl CreateStack success, id=", id)
	}
}

func aTestStackDaoImpl_DeleteStack(t *testing.T) {
	stackDao, _ := NewStackDao(nil, context.TODO())
	affected, err := stackDao.DeleteStack(1)
	if err != nil {
		t.Errorf("StackDaoImpl DeleteStack fail,as[%s]", err.<PERSON>r())
	} else {
		t.Log("StackDaoImpl DeleteStack success, affected=", affected)
	}
}

func aTestStackDaoImpl_UpdateStack(t *testing.T) {
	stack := configure.Stack{}
	stackDao, _ := NewStackDao(nil, context.TODO())
	affected, err := stackDao.UpdateStack(&stack)
	if err != nil {
		t.Errorf("StackDaoImpl UpdateStack fail,as[%s]", err.Error())
	} else {
		t.Log("StackDaoImpl UpdateStack success, affected=", affected)
	}
}

func aTestStackDaoImpl_ReadStackPage(t *testing.T) {
	stackDao, _ := NewStackDao(nil, context.TODO())
	stacks, err := stackDao.ReadStackPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("StackDaoImpl ReadStackPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(stacks)
		t.Log("StackDaoImpl ReadStackPage success, data=", string(bytes))
	}
}

func aTestStackDaoImpl_FindAll(t *testing.T) {
	stackDao, _ := NewStackDao(nil, context.TODO())
	stacks, err := stackDao.FindAll()
	if err != nil {
		t.Errorf("StackDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(stacks)
		t.Log("StackDaoImpl FindAll success, data=", string(bytes))
	}
}

func aTestStackDaoImpl_ReadStack(t *testing.T) {
	stackDao, _ := NewStackDao(nil, context.TODO())
	stack, err := stackDao.ReadStack(0)
	if err != nil {
		t.Errorf("StackDaoImpl ReadStack fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(stack)
		t.Log("StackDaoImpl ReadStack success, data=", string(bytes))
	}
}
