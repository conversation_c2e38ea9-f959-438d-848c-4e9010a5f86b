package configure

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/configure"
)

func aTestPatchLogDaoImpl_CreatePatchLog(t *testing.T) {
	patchLog := configure.PatchLog{}
	patchLogDao, _ := NewPatchLogDao(nil, context.TODO())
	id, err := patchLogDao.CreatePatchLog(&patchLog)
	if err != nil {
		t.<PERSON><PERSON>("PatchLogDaoImpl CreatePatchLog fail,as[%s]", err.Error())
	} else {
		t.Log("PatchLogDaoImpl CreatePatchLog success, id=", id)
	}
}

func aTestPatchLogDaoImpl_DeletePatchLog(t *testing.T) {
	patchLogDao, _ := NewPatchLogDao(nil, context.TODO())
	affected, err := patchLogDao.DeletePatchLog(1)
	if err != nil {
		t.<PERSON>("PatchLogDaoImpl DeletePatchLog fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("PatchLogDaoImpl DeletePatchLog success, affected=", affected)
	}
}

func aTestPatchLogDaoImpl_UpdatePatchLog(t *testing.T) {
	patchLog := configure.PatchLog{}
	patchLogDao, _ := NewPatchLogDao(nil, context.TODO())
	affected, err := patchLogDao.UpdatePatchLog(&patchLog)
	if err != nil {
		t.Errorf("PatchLogDaoImpl UpdatePatchLog fail,as[%s]", err.Error())
	} else {
		t.Log("PatchLogDaoImpl UpdatePatchLog success, affected=", affected)
	}
}

func aTestPatchLogDaoImpl_ReadPatchLogPage(t *testing.T) {
	patchLogDao, _ := NewPatchLogDao(nil, context.TODO())
	patchLogs, err := patchLogDao.ReadPatchLogPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("PatchLogDaoImpl ReadPatchLogPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(patchLogs)
		t.Log("PatchLogDaoImpl ReadPatchLogPage success, data=", string(bytes))
	}
}

func aTestPatchLogDaoImpl_FindAll(t *testing.T) {
	patchLogDao, _ := NewPatchLogDao(nil, context.TODO())
	patchLogs, err := patchLogDao.FindAll()
	if err != nil {
		t.Errorf("PatchLogDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(patchLogs)
		t.Log("PatchLogDaoImpl FindAll success, data=", string(bytes))
	}
}

func aTestPatchLogDaoImpl_ReadPatchLog(t *testing.T) {
	patchLogDao, _ := NewPatchLogDao(nil, context.TODO())
	patchLog, err := patchLogDao.ReadPatchLog("")
	if err != nil {
		t.Errorf("PatchLogDaoImpl ReadPatchLog fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(patchLog)
		t.Log("PatchLogDaoImpl ReadPatchLog success, data=", string(bytes))
	}
}
