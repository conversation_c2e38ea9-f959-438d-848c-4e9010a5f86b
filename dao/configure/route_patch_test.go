package configure

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/configure"
)

func aTestRoutePatchDaoImpl_CreateRoutePatch(t *testing.T) {
	routePatch := configure.RoutePatch{}
	routePatchDao, _ := NewRoutePatchDao(nil, context.TODO())
	id, err := routePatchDao.CreateRoutePatch(&routePatch)
	if err != nil {
		t.Errorf("RoutePatchDaoImpl CreateRoutePatch fail,as[%s]", err.Error())
	} else {
		t.Log("RoutePatchDaoImpl CreateRoutePatch success, id=", id)
	}
}

func aTestRoutePatchDaoImpl_DeleteRoutePatch(t *testing.T) {
	routePatchDao, _ := NewRoutePatchDao(nil, context.TODO())
	affected, err := routePatchDao.DeleteRoutePatch(1)
	if err != nil {
		t.Errorf("RoutePatchDaoImpl DeleteRoutePatch fail,as[%s]", err.Error())
	} else {
		t.Log("RoutePatchDaoImpl DeleteRoutePatch success, affected=", affected)
	}
}

func aTestRoutePatchDaoImpl_UpdateRoutePatch(t *testing.T) {
	routePatch := configure.RoutePatch{}
	routePatchDao, _ := NewRoutePatchDao(nil, context.TODO())
	affected, err := routePatchDao.UpdateRoutePatch(&routePatch)
	if err != nil {
		t.Errorf("RoutePatchDaoImpl UpdateRoutePatch fail,as[%s]", err.Error())
	} else {
		t.Log("RoutePatchDaoImpl UpdateRoutePatch success, affected=", affected)
	}
}

func aTestRoutePatchDaoImpl_ReadRoutePatchPage(t *testing.T) {
	routePatchDao, _ := NewRoutePatchDao(nil, context.TODO())
	routePatchs, err := routePatchDao.ReadRoutePatchPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("RoutePatchDaoImpl ReadRoutePatchPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(routePatchs)
		t.Log("RoutePatchDaoImpl ReadRoutePatchPage success, data=", string(bytes))
	}
}

func aTestRoutePatchDaoImpl_FindAll(t *testing.T) {
	routePatchDao, _ := NewRoutePatchDao(nil, context.TODO())
	routePatchs, err := routePatchDao.FindAll()
	if err != nil {
		t.Errorf("RoutePatchDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(routePatchs)
		t.Log("RoutePatchDaoImpl FindAll success, data=", string(bytes))
	}
}

func aTestRoutePatchDaoImpl_ReadRoutePatch(t *testing.T) {
	routePatchDao, _ := NewRoutePatchDao(nil, context.TODO())
	routePatch, err := routePatchDao.ReadRoutePatch(1)
	if err != nil {
		t.Errorf("RoutePatchDaoImpl ReadRoutePatch fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(routePatch)
		t.Log("RoutePatchDaoImpl ReadRoutePatch success, data=", string(bytes))
	}
}
