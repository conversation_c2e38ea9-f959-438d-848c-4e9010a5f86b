package configure

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/configure"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for configure.Field
type FieldDaoImpl struct {
	database.DaoImpl
}

// new a data access object for field
func NewFieldDao(session *xorm.Session, ctx context.Context) (*FieldDaoImpl, error) {
	fieldDao := FieldDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(configure.Field)
	if err := fieldDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	fieldDao.NewSlice = func() interface{} {
		slice := make([]configure.Field, 0)
		return &slice
	}

	return &fieldDao, nil
}

func (d *FieldDaoImpl) CreateField(field *configure.Field) (int64, error) {
	affected, err := d.Create(field)
	if err != nil {
		log.Errorf(d.Ctx, "FieldDaoImpl CreateField [%v] fail,as:%s", field, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *FieldDaoImpl) DeleteField(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "FieldDaoImpl DeleteField [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *FieldDaoImpl) UpdateField(field *configure.Field, fields ...string) (int64, error) {
	if field == nil {
		return 0, fmt.Errorf("invalid paramter, field=nil")
	}

	affected, err := d.Update(field.ID, field, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "FieldDaoImpl UpdateField [%v] fail,as:%s", field.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *FieldDaoImpl) FindAll() ([]configure.Field, error) {
	var fields []configure.Field
	err := d.SortListBy(&fields, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "FieldDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return fields, nil
}

func (d *FieldDaoImpl) ReadField(fieldId uint64) (*configure.Field, error) {
	var field configure.Field
	exist, err := d.FindById(fieldId, &field)
	if err != nil {
		log.Errorf(d.Ctx, "FieldDaoImpl Read [%s] fail,as:%s", fieldId, err.Error())
		return nil, err
	} else if exist {
		return &field, nil
	} else {
		log.Debugf(d.Ctx, "FieldDaoImpl the field id=[%d] not exist", fieldId)
		return nil, fmt.Errorf("the field [%d] not exist", fieldId)
	}
}

func (d *FieldDaoImpl) ReadFieldPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "FieldDaoImpl ReadFieldPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *FieldDaoImpl) ReadFieldInfo(fieldId uint64) (*configure.FieldInfo, error) {
	var field configure.FieldInfo
	filter := configure.FieldFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(fieldId, &field, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "FieldDaoImpl ReadFieldInfo [%d] fail,as:%s", fieldId, err.Error())
		return nil, err
	} else if exist {
		return &field, nil
	} else {
		log.Debugf(d.Ctx, "FieldDaoImpl the field id=[%d] not exist", fieldId)
		return nil, fmt.Errorf("the field [%d] not exist", fieldId)
	}
}

func (d *FieldDaoImpl) FindFieldPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "FieldDaoImpl FindFieldPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *FieldDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]configure.FieldInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "FieldDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *FieldDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]configure.FieldInfo, error) {
	records := make([]configure.FieldInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "FieldDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *FieldDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*configure.FieldInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(configure.FieldInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "FieldDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "FieldDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *FieldDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindFieldPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *FieldDaoImpl) ReadByFilter(filter information.IFilter) ([]configure.Field, error) {
	records := make([]configure.Field, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "FieldDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *FieldDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *FieldDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *FieldDaoImpl) Entity() information.IRecord {
	return new(configure.Field)
}

func (d *FieldDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(configure.FieldFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
