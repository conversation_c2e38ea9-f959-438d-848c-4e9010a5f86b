package configure

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/configure"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

// Data Access Object for configure.Patch
type PatchDaoImpl struct {
	database.DaoImpl
}

// new a data access object for patch
func NewPatchDao(session *xorm.Session, ctx context.Context) (*PatchDaoImpl, error) {
	patchDao := PatchDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(configure.Patch)
	if err := patchDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	patchDao.NewSlice = func() interface{} {
		slice := make([]configure.Patch, 0)
		return &slice
	}

	return &patchDao, nil
}

func (d *PatchDaoImpl) CreatePatch(patch *configure.Patch) (int64, error) {
	affected, err := d.Create(patch)
	if err != nil {
		log.Errorf(d.Ctx, "PatchDaoImpl CreatePatch [%v] fail,as:%s", patch, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PatchDaoImpl) DeletePatch(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "PatchDaoImpl DeletePatch [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PatchDaoImpl) UpdatePatch(patch *configure.Patch, patchs ...string) (int64, error) {
	if patch == nil {
		return 0, fmt.Errorf("invalid paramter, patch=nil")
	}

	affected, err := d.Update(patch.ID, patch, patchs...)
	if err != nil {
		log.Errorf(d.Ctx, "PatchDaoImpl UpdatePatch [%v] fail,as:%s", patch.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PatchDaoImpl) FindAll() ([]configure.Patch, error) {
	var patchs []configure.Patch
	err := d.SortListBy(&patchs, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "PatchDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return patchs, nil
}

func (d *PatchDaoImpl) ReadPatch(patchId uint64) (*configure.Patch, error) {
	var patch configure.Patch
	exist, err := d.FindById(patchId, &patch)
	if err != nil {
		log.Errorf(d.Ctx, "PatchDaoImpl Read [%d] fail,as:%s", patchId, err.Error())
		return nil, err
	} else if exist {
		return &patch, nil
	} else {
		log.Debugf(d.Ctx, "PatchDaoImpl the patch id=[%d] not exist", patchId)
		return nil, fmt.Errorf("the patch [%d] not exist", patchId)
	}
}

func (d *PatchDaoImpl) ReadPatchPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "PatchDaoImpl ReadPatchPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *PatchDaoImpl) ReadByPlanID(planID uint64, status, type_, code string) ([]configure.Patch, error) {
	records := make([]configure.Patch, 0)

	var err error
	if status == "" {
		err = d.SortListBy(&records, "id ASC", "plan_id=?", planID)
	} else if code == "" {
		err = d.SortListBy(&records, "id ASC", "plan_id=? AND status=?", planID, status)
	} else {
		err = d.SortListBy(&records, "id ASC", "plan_id=? AND status=? AND service_code=? AND service_type=?", planID, status, code, type_)
	}

	if log.IfError(err, "PatchDaoImpl ReadByFilterPlanID") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *PatchDaoImpl) ReadLastByFilter(filter *configure.PatchFilter) (*configure.Patch, error) {
	records := make([]configure.Patch, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortFirstBy(&records, sort, query, args...)
	if log.IfError(err, "PatchDaoImpl SortFirstBy") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *PatchDaoImpl) ReadFirstByFilter(filter *configure.PatchFilter) (*configure.Patch, error) {
	records := make([]configure.Patch, 0)
	query, args := filter.ToSql()
	sort := "id asc"
	err := d.SortFirstBy(&records, sort, query, args...)
	if log.IfError(err, "PatchDaoImpl SortFirstBy") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *PatchDaoImpl) ReadPatchInfo(patchId uint64) (*configure.PatchInfo, error) {
	var patch configure.PatchInfo
	filter := configure.PatchFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(patchId, &patch, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "PatchDaoImpl ReadPatchInfo [%d] fail,as:%s", patchId, err.Error())
		return nil, err
	} else if exist {
		return &patch, nil
	} else {
		log.Debugf(d.Ctx, "PatchDaoImpl the patch id=[%d] not exist", patchId)
		return nil, fmt.Errorf("the patch [%d] not exist", patchId)
	}
}

func (d *PatchDaoImpl) FindPatchPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "PatchDaoImpl FindPatchPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *PatchDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]configure.PatchInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "PatchDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *PatchDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]configure.PatchInfo, error) {
	records := make([]configure.PatchInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "PatchDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *PatchDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*configure.PatchInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(configure.PatchInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "PatchDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "PatchDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *PatchDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindPatchPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *PatchDaoImpl) ReadByFilter(filter information.IFilter) ([]configure.Patch, error) {
	records := make([]configure.Patch, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "PatchDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *PatchDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *PatchDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *PatchDaoImpl) Entity() information.IRecord {
	return new(configure.Patch)
}

func (d *PatchDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(configure.PatchFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
