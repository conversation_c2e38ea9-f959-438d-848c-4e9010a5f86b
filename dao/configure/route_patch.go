package configure

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/configure"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

// Data Access Object for configure.RoutePatch
type RoutePatchDaoImpl struct {
	database.DaoImpl
}

// new a data access object for routePatch
func NewRoutePatchDao(session *xorm.Session, ctx context.Context) (*RoutePatchDaoImpl, error) {
	routePatchDao := RoutePatchDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(configure.RoutePatch)
	if err := routePatchDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	routePatchDao.NewSlice = func() interface{} {
		slice := make([]configure.RoutePatch, 0)
		return &slice
	}

	return &routePatchDao, nil
}

func (d *RoutePatchDaoImpl) CreateRoutePatch(routePatch *configure.RoutePatch) (int64, error) {
	affected, err := d.Create(routePatch)
	if err != nil {
		log.Errorf(d.Ctx, "RoutePatchDaoImpl CreateRoutePatch [%v] fail,as:%s", routePatch, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RoutePatchDaoImpl) DeleteRoutePatch(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "RoutePatchDaoImpl DeleteRoutePatch [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RoutePatchDaoImpl) UpdateRoutePatch(routePatch *configure.RoutePatch, routePatchs ...string) (int64, error) {
	if routePatch == nil {
		return 0, fmt.Errorf("invalid paramter, routePatch=nil")
	}

	affected, err := d.Update(routePatch.ID, routePatch, routePatchs...)
	if err != nil {
		log.Errorf(d.Ctx, "RoutePatchDaoImpl UpdateRoutePatch [%v] fail,as:%s", routePatch.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RoutePatchDaoImpl) FindAll() ([]configure.RoutePatch, error) {
	var routePatchs []configure.RoutePatch
	err := d.SortListBy(&routePatchs, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "RoutePatchDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return routePatchs, nil
}

func (d *RoutePatchDaoImpl) ReadRoutePatch(routePatchId uint64) (*configure.RoutePatch, error) {
	var routePatch configure.RoutePatch
	exist, err := d.FindById(routePatchId, &routePatch)
	if err != nil {
		log.Errorf(d.Ctx, "RoutePatchDaoImpl Read [%d] fail,as:%s", routePatchId, err.Error())
		return nil, err
	} else if exist {
		return &routePatch, nil
	} else {
		log.Debugf(d.Ctx, "RoutePatchDaoImpl the routePatch id=[%d] not exist", routePatchId)
		return nil, fmt.Errorf("the routePatch [%d] not exist", routePatchId)
	}
}

func (d *RoutePatchDaoImpl) ReadRoutePatchPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "RoutePatchDaoImpl ReadRoutePatchPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *RoutePatchDaoImpl) ReadByPlanID(planID uint64, status, type_, code string) ([]configure.RoutePatch, error) {
	records := make([]configure.RoutePatch, 0)

	var err error
	if status == "" {
		err = d.SortListBy(&records, "id ASC", "plan_id=?", planID)
	} else if code == "" {
		err = d.SortListBy(&records, "id ASC", "plan_id=? AND status=?", planID, status)
	} else {
		err = d.SortListBy(&records, "id ASC", "plan_id=? AND status=? AND service_code=? AND service_type=?", planID, status, code, type_)
	}

	if log.IfError(err, "RoutePatchDaoImpl ReadByFilterPlanID") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *RoutePatchDaoImpl) ReadLastByFilter(filter *configure.RoutePatchFilter) (*configure.RoutePatch, error) {
	records := make([]configure.RoutePatch, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortFirstBy(&records, sort, query, args...)
	if log.IfError(err, "RoutePatchDaoImpl SortFirstBy") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *RoutePatchDaoImpl) ReadFirstByFilter(filter *configure.RoutePatchFilter) (*configure.RoutePatch, error) {
	records := make([]configure.RoutePatch, 0)
	query, args := filter.ToSql()
	sort := "id asc"
	err := d.SortFirstBy(&records, sort, query, args...)
	if log.IfError(err, "RoutePatchDaoImpl SortFirstBy") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *RoutePatchDaoImpl) ReadRoutePatchInfo(routePatchId uint64) (*configure.RoutePatchInfo, error) {
	var routePatch configure.RoutePatchInfo
	filter := configure.RoutePatchFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(routePatchId, &routePatch, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "RoutePatchDaoImpl ReadRoutePatchInfo [%d] fail,as:%s", routePatchId, err.Error())
		return nil, err
	} else if exist {
		return &routePatch, nil
	} else {
		log.Debugf(d.Ctx, "RoutePatchDaoImpl the routePatch id=[%d] not exist", routePatchId)
		return nil, fmt.Errorf("the routePatch [%d] not exist", routePatchId)
	}
}

func (d *RoutePatchDaoImpl) FindRoutePatchPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "RoutePatchDaoImpl FindRoutePatchPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *RoutePatchDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]configure.RoutePatchInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "RoutePatchDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *RoutePatchDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]configure.RoutePatchInfo, error) {
	records := make([]configure.RoutePatchInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "RoutePatchDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *RoutePatchDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*configure.RoutePatchInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(configure.RoutePatchInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "RoutePatchDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "RoutePatchDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *RoutePatchDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindRoutePatchPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *RoutePatchDaoImpl) ReadByFilter(filter information.IFilter) ([]configure.RoutePatch, error) {
	records := make([]configure.RoutePatch, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "RoutePatchDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *RoutePatchDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *RoutePatchDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *RoutePatchDaoImpl) Entity() information.IRecord {
	return new(configure.RoutePatch)
}

func (d *RoutePatchDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(configure.RoutePatchFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
