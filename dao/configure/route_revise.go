package configure

import (
	"context"
	"fmt"
	"strings"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/configure"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for gateway.RouteRevise
type RouteReviseDaoImpl struct {
	db.DaoImpl
}

// new a data access object for routeRevise
func NewRouteReviseDao(session *xorm.Session, ctx context.Context) (*RouteReviseDaoImpl, error) {
	routeReviseDao := RouteReviseDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(configure.RouteRevise)
	if err := routeReviseDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	routeReviseDao.NewSlice = func() interface{} {
		slice := make([]configure.RouteRevise, 0)
		return &slice
	}

	return &routeReviseDao, nil
}

func (d *RouteReviseDaoImpl) CreateRouteRevise(routeRevise *configure.RouteRevise) (int64, error) {
	affected, err := d.Create(routeRevise)
	if err != nil {
		log.Errorf(d.Ctx, "RouteReviseDaoImpl CreateRouteRevise [%v] fail,as:%s", routeRevise, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RouteReviseDaoImpl) DeleteRouteRevise(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "RouteReviseDaoImpl DeleteRouteRevise [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RouteReviseDaoImpl) UpdateRouteRevise(routeRevise *configure.RouteRevise, routeRevises ...string) (int64, error) {
	if routeRevise == nil {
		return 0, fmt.Errorf("invalid paramter, routeRevise=nil")
	}

	affected, err := d.Update(routeRevise.ID, routeRevise, routeRevises...)
	if err != nil {
		log.Errorf(d.Ctx, "RouteReviseDaoImpl UpdateRouteRevise [%v] fail,as:%s", routeRevise.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RouteReviseDaoImpl) FindAll() ([]configure.RouteRevise, error) {
	var routeRevises []configure.RouteRevise
	err := d.SortListBy(&routeRevises, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "RouteReviseDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return routeRevises, nil
}

func (d *RouteReviseDaoImpl) ReadRouteRevise(routeReviseId uint64) (*configure.RouteRevise, error) {
	var routeRevise configure.RouteRevise
	exist, err := d.FindById(routeReviseId, &routeRevise)
	if err != nil {
		log.Errorf(d.Ctx, "RouteReviseDaoImpl Read [%d] fail,as:%s", routeReviseId, err.Error())
		return nil, err
	} else if exist {
		return &routeRevise, nil
	} else {
		log.Debugf(d.Ctx, "RouteReviseDaoImpl the routeRevise id=[%d] not exist", routeReviseId)
		return nil, fmt.Errorf("the routeRevise [%d] not exist", routeReviseId)
	}
}

func (d *RouteReviseDaoImpl) ReadRouteRevisePage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "RouteReviseDaoImpl ReadRouteRevisePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *RouteReviseDaoImpl) ReadRouteReviseInfo(routeReviseId uint64) (*configure.RouteReviseInfo, error) {
	var routeRevise configure.RouteReviseInfo
	filter := configure.RouteReviseFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(routeReviseId, &routeRevise, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "RouteReviseDaoImpl ReadRouteReviseInfo [%d] fail,as:%s", routeReviseId, err.Error())
		return nil, err
	} else if exist {
		return &routeRevise, nil
	} else {
		log.Debugf(d.Ctx, "RouteReviseDaoImpl the routeRevise id=[%d] not exist", routeReviseId)
		return nil, fmt.Errorf("the routeRevise [%d] not exist", routeReviseId)
	}
}

func (d *RouteReviseDaoImpl) ReadLastByFilter(filter *configure.RouteReviseFilter) (*configure.RouteRevise, error) {
	records := make([]configure.RouteRevise, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortFirstBy(&records, sort, query, args...)
	if log.IfError(err, "RouteReviseDaoImpl SortFirstBy") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *RouteReviseDaoImpl) FindRouteRevisePage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "RouteReviseDaoImpl FindRouteRevisePage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *RouteReviseDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]configure.RouteReviseInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "RouteReviseDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *RouteReviseDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]configure.RouteReviseInfo, error) {
	records := make([]configure.RouteReviseInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "RouteReviseDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *RouteReviseDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*configure.RouteReviseInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(configure.RouteReviseInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "RouteReviseDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "RouteReviseDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *RouteReviseDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindRouteRevisePage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *RouteReviseDaoImpl) ReadByFilter(filter information.IFilter) ([]configure.RouteRevise, error) {
	records := make([]configure.RouteRevise, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "RouteReviseDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *RouteReviseDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *RouteReviseDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *RouteReviseDaoImpl) Entity() information.IRecord {
	return new(configure.RouteRevise)
}

func (d *RouteReviseDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(configure.RouteReviseFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
