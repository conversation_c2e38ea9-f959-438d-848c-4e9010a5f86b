package configure

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/configure"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for configure.Revise
type ReviseDaoImpl struct {
	database.DaoImpl
}

// new a data access object for revise
func NewReviseDao(session *xorm.Session, ctx context.Context) (*ReviseDaoImpl, error) {
	reviseDao := ReviseDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(configure.Revise)
	if err := reviseDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	reviseDao.NewSlice = func() interface{} {
		slice := make([]configure.Revise, 0)
		return &slice
	}

	return &reviseDao, nil
}

func (d *ReviseDaoImpl) CreateRevise(revise *configure.Revise) (int64, error) {
	affected, err := d.Create(revise)
	if err != nil {
		log.Errorf(d.Ctx, "ReviseDaoImpl CreateRevise [%v] fail,as:%s", revise, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ReviseDaoImpl) DeleteRevise(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "ReviseDaoImpl DeleteRevise [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ReviseDaoImpl) UpdateRevise(revise *configure.Revise, revises ...string) (int64, error) {
	if revise == nil {
		return 0, fmt.Errorf("invalid paramter, revise=nil")
	}

	affected, err := d.Update(revise.ID, revise, revises...)
	if err != nil {
		log.Errorf(d.Ctx, "ReviseDaoImpl UpdateRevise [%v] fail,as:%s", revise.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ReviseDaoImpl) FindAll() ([]configure.Revise, error) {
	var revises []configure.Revise
	err := d.SortListBy(&revises, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "ReviseDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return revises, nil
}

func (d *ReviseDaoImpl) ReadRevise(reviseId string) (*configure.Revise, error) {
	var revise configure.Revise
	exist, err := d.FindById(reviseId, &revise)
	if err != nil {
		log.Errorf(d.Ctx, "ReviseDaoImpl Read [%s] fail,as:%s", reviseId, err.Error())
		return nil, err
	} else if exist {
		return &revise, nil
	} else {
		log.Debugf(d.Ctx, "ReviseDaoImpl the revise id=[%s] not exist", reviseId)
		return nil, fmt.Errorf("the revise [%s] not exist", reviseId)
	}
}

func (d *ReviseDaoImpl) ReadRevisePage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "ReviseDaoImpl ReadRevisePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *ReviseDaoImpl) ReadReviseInfo(reviseId uint64) (*configure.ReviseInfo, error) {
	var revise configure.ReviseInfo
	filter := configure.ReviseFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(reviseId, &revise, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "ReviseDaoImpl ReadReviseInfo [%d] fail,as:%s", reviseId, err.Error())
		return nil, err
	} else if exist {
		return &revise, nil
	} else {
		log.Debugf(d.Ctx, "ReviseDaoImpl the revise id=[%d] not exist", reviseId)
		return nil, fmt.Errorf("the revise [%d] not exist", reviseId)
	}
}

func (d *ReviseDaoImpl) FindRevisePage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "ReviseDaoImpl FindRevisePage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *ReviseDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]configure.ReviseInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "ReviseDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *ReviseDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]configure.ReviseInfo, error) {
	records := make([]configure.ReviseInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "ReviseDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *ReviseDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*configure.ReviseInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(configure.ReviseInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "ReviseDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "ReviseDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *ReviseDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindRevisePage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *ReviseDaoImpl) ReadByFilter(filter information.IFilter) ([]configure.Revise, error) {
	records := make([]configure.Revise, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "ReviseDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *ReviseDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *ReviseDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *ReviseDaoImpl) Entity() information.IRecord {
	return new(configure.Revise)
}

func (d *ReviseDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(configure.ReviseFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
