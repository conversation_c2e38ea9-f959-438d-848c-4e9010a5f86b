package configure

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	. "git.platform.io/environment/environment/dao/test"
	"git.platform.io/environment/environment/model/configure"
)

func aTestColumnDaoImpl_CreateColumn(t *testing.T) {
	column := configure.Column{}
	columnDao, _ := NewColumnDao(nil, context.TODO())
	id, err := columnDao.CreateColumn(&column)
	if err != nil {
		t.<PERSON><PERSON>("ColumnDaoImpl CreateColumn fail,as[%s]", err.Error())
	} else {
		t.Log("ColumnDaoImpl CreateColumn success, id=", id)
	}
}

func aTestColumnDaoImpl_DeleteColumn(t *testing.T) {
	columnDao, _ := NewColumnDao(nil, context.TODO())
	affected, err := columnDao.DeleteColumn("1")
	if err != nil {
		t.<PERSON>rrorf("ColumnDaoImpl DeleteColumn fail,as[%s]", err.Error())
	} else {
		t.Log("ColumnDaoImpl DeleteColumn success, affected=", affected)
	}
}

func aTestColumnDaoImpl_UpdateColumn(t *testing.T) {
	column := configure.Column{}
	columnDao, _ := NewColumnDao(nil, context.TODO())
	affected, err := columnDao.UpdateColumn(&column)
	if err != nil {
		t.Errorf("ColumnDaoImpl UpdateColumn fail,as[%s]", err.Error())
	} else {
		t.Log("ColumnDaoImpl UpdateColumn success, affected=", affected)
	}
}

func aTestColumnDaoImpl_ReadColumnPage(t *testing.T) {
	columnDao, _ := NewColumnDao(nil, context.TODO())
	columns, err := columnDao.ReadColumnPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("ColumnDaoImpl ReadColumnPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(columns)
		t.Log("ColumnDaoImpl ReadColumnPage success, data=", string(bytes))
	}
}

func aTestColumnDaoImpl_FindAll(t *testing.T) {
	columnDao, _ := NewColumnDao(nil, context.TODO())
	columns, err := columnDao.FindAll()
	if err != nil {
		t.Errorf("ColumnDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(columns)
		t.Log("ColumnDaoImpl FindAll success, data=", string(bytes))
	}
}

func aTestColumnDaoImpl_ReadColumn(t *testing.T) {
	columnDao, _ := NewColumnDao(nil, context.TODO())
	column, err := columnDao.ReadColumn("")
	if err != nil {
		t.Errorf("ColumnDaoImpl ReadColumn fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(column)
		t.Log("ColumnDaoImpl ReadColumn success, data=", string(bytes))
	}
}

func aTestMain(m *testing.M) {
	Setup()
	code := m.Run()
	Teardown()
	os.Exit(code)
}
