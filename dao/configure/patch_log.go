package configure

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/configure"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for configure.PatchLog
type PatchLogDaoImpl struct {
	database.DaoImpl
}

// new a data access object for patchLog
func NewPatchLogDao(session *xorm.Session, ctx context.Context) (*PatchLogDaoImpl, error) {
	patchLogDao := PatchLogDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(configure.PatchLog)
	if err := patchLogDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	patchLogDao.NewSlice = func() interface{} {
		slice := make([]configure.PatchLog, 0)
		return &slice
	}

	return &patchLogDao, nil
}

func (d *PatchLogDaoImpl) CreatePatchLog(patchLog *configure.PatchLog) (int64, error) {
	affected, err := d.Create(patchLog)
	if err != nil {
		log.Errorf(d.Ctx, "PatchLogDaoImpl CreatePatchLog [%v] fail,as:%s", patchLog, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PatchLogDaoImpl) DeletePatchLog(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "PatchLogDaoImpl DeletePatchLog [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PatchLogDaoImpl) UpdatePatchLog(patchLog *configure.PatchLog, patchLogs ...string) (int64, error) {
	if patchLog == nil {
		return 0, fmt.Errorf("invalid paramter, patchLog=nil")
	}

	affected, err := d.Update(patchLog.ID, patchLog, patchLogs...)
	if err != nil {
		log.Errorf(d.Ctx, "PatchLogDaoImpl UpdatePatchLog [%v] fail,as:%s", patchLog.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PatchLogDaoImpl) FindAll() ([]configure.PatchLog, error) {
	var patchLogs []configure.PatchLog
	err := d.SortListBy(&patchLogs, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "PatchLogDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return patchLogs, nil
}

func (d *PatchLogDaoImpl) ReadPatchLog(patchLogId string) (*configure.PatchLog, error) {
	var patchLog configure.PatchLog
	exist, err := d.FindById(patchLogId, &patchLog)
	if err != nil {
		log.Errorf(d.Ctx, "PatchLogDaoImpl Read [%s] fail,as:%s", patchLogId, err.Error())
		return nil, err
	} else if exist {
		return &patchLog, nil
	} else {
		log.Debugf(d.Ctx, "PatchLogDaoImpl the patchLog id=[%s] not exist", patchLogId)
		return nil, fmt.Errorf("the patchLog [%s] not exist", patchLogId)
	}
}

func (d *PatchLogDaoImpl) ReadPatchLogPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "PatchLogDaoImpl ReadPatchLogPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *PatchLogDaoImpl) ReadPatchLogInfo(patchLogId uint64) (*configure.PatchLogInfo, error) {
	var patchLog configure.PatchLogInfo
	filter := configure.PatchLogFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(patchLogId, &patchLog, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "PatchLogDaoImpl ReadPatchLogInfo [%d] fail,as:%s", patchLogId, err.Error())
		return nil, err
	} else if exist {
		return &patchLog, nil
	} else {
		log.Debugf(d.Ctx, "PatchLogDaoImpl the patchLog id=[%d] not exist", patchLogId)
		return nil, fmt.Errorf("the patchLog [%d] not exist", patchLogId)
	}
}

func (d *PatchLogDaoImpl) FindPatchLogPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "PatchLogDaoImpl FindPatchLogPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *PatchLogDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]configure.PatchLogInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "PatchLogDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *PatchLogDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]configure.PatchLogInfo, error) {
	records := make([]configure.PatchLogInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "PatchLogDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *PatchLogDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*configure.PatchLogInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(configure.PatchLogInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "PatchLogDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "PatchLogDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *PatchLogDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindPatchLogPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *PatchLogDaoImpl) ReadByFilter(filter information.IFilter) ([]configure.PatchLog, error) {
	records := make([]configure.PatchLog, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "PatchLogDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *PatchLogDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *PatchLogDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *PatchLogDaoImpl) Entity() information.IRecord {
	return new(configure.PatchLog)
}

func (d *PatchLogDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(configure.PatchLogFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
