package configure

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/configure"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for configure.Section
type SectionDaoImpl struct {
	database.DaoImpl
}

// new a data access object for section
func NewSectionDao(session *xorm.Session, ctx context.Context) (*SectionDaoImpl, error) {
	sectionDao := SectionDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(configure.Section)
	if err := sectionDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	sectionDao.NewSlice = func() interface{} {
		slice := make([]configure.Section, 0)
		return &slice
	}

	return &sectionDao, nil
}

func (d *SectionDaoImpl) CreateSection(section *configure.Section) (int64, error) {
	affected, err := d.Create(section)
	if err != nil {
		log.Errorf(d.Ctx, "SectionDaoImpl CreateSection [%v] fail,as:%s", section, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *SectionDaoImpl) DeleteSection(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "SectionDaoImpl DeleteSection [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *SectionDaoImpl) UpdateSection(section *configure.Section, fields ...string) (int64, error) {
	if section == nil {
		return 0, fmt.Errorf("invalid paramter, section=nil")
	}

	affected, err := d.Update(section.ID, section, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "SectionDaoImpl UpdateSection [%v] fail,as:%s", section.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *SectionDaoImpl) FindAll() ([]configure.Section, error) {
	var sections []configure.Section
	err := d.SortListBy(&sections, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "SectionDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return sections, nil
}

func (d *SectionDaoImpl) ReadSection(sectionId uint64) (*configure.Section, error) {
	var section configure.Section
	exist, err := d.FindById(sectionId, &section)
	if err != nil {
		log.Errorf(d.Ctx, "SectionDaoImpl Read [%d] fail,as:%s", sectionId, err.Error())
		return nil, err
	} else if exist {
		return &section, nil
	} else {
		log.Debugf(d.Ctx, "SectionDaoImpl the section id=[%d] not exist", sectionId)
		return nil, fmt.Errorf("the section [%d] not exist", sectionId)
	}
}

func (d *SectionDaoImpl) ReadSectionByCode(sectionCode string) (*configure.Section, error) {
	var section configure.Section
	exist, err := d.Find(&section, "section_code=?", sectionCode)
	if err != nil {
		log.Errorf(d.Ctx, "SectionDaoImpl Read [%s] fail,as:%s", sectionCode, err.Error())
		return nil, err
	} else if exist {
		return &section, nil
	} else {
		log.Debugf(d.Ctx, "SectionDaoImpl the section code=[%d] not exist", sectionCode)
		return nil, fmt.Errorf("the section code=[%s] not exist", sectionCode)
	}
}

func (d *SectionDaoImpl) ReadSectionPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "SectionDaoImpl ReadSectionPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *SectionDaoImpl) ReadSectionInfo(sectionId uint64) (*configure.SectionInfo, error) {
	var section configure.SectionInfo
	filter := configure.SectionFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(sectionId, &section, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "SectionDaoImpl ReadSectionInfo [%d] fail,as:%s", sectionId, err.Error())
		return nil, err
	} else if exist {
		return &section, nil
	} else {
		log.Debugf(d.Ctx, "SectionDaoImpl the section id=[%d] not exist", sectionId)
		return nil, fmt.Errorf("the section [%d] not exist", sectionId)
	}
}

func (d *SectionDaoImpl) FindSectionPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "SectionDaoImpl FindSectionPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *SectionDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]configure.SectionInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "SectionDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *SectionDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]configure.SectionInfo, error) {
	records := make([]configure.SectionInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "SectionDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *SectionDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*configure.SectionInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(configure.SectionInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "SectionDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "SectionDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *SectionDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindSectionPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *SectionDaoImpl) ReadByFilter(filter information.IFilter) ([]configure.Section, error) {
	records := make([]configure.Section, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "SectionDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *SectionDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *SectionDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *SectionDaoImpl) Entity() information.IRecord {
	return new(configure.Section)
}

func (d *SectionDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(configure.SectionFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
