package configure

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/configure"
)

func aTestFieldDaoImpl_CreateField(t *testing.T) {
	field := configure.Field{}
	fieldDao, _ := NewFieldDao(nil, context.TODO())
	id, err := fieldDao.CreateField(&field)
	if err != nil {
		t.<PERSON>rf("FieldDaoImpl CreateField fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("FieldDaoImpl CreateField success, id=", id)
	}
}

func aTestFieldDaoImpl_DeleteField(t *testing.T) {
	fieldDao, _ := NewFieldDao(nil, context.TODO())
	affected, err := fieldDao.DeleteField(1)
	if err != nil {
		t.<PERSON>rrorf("FieldDaoImpl DeleteField fail,as[%s]", err.Error())
	} else {
		t.Log("FieldDaoImpl DeleteField success, affected=", affected)
	}
}

func aTestFieldDaoImpl_UpdateField(t *testing.T) {
	field := configure.Field{}
	fieldDao, _ := NewFieldDao(nil, context.TODO())
	affected, err := fieldDao.UpdateField(&field)
	if err != nil {
		t.Errorf("FieldDaoImpl UpdateField fail,as[%s]", err.Error())
	} else {
		t.Log("FieldDaoImpl UpdateField success, affected=", affected)
	}
}

func aTestFieldDaoImpl_ReadFieldPage(t *testing.T) {
	fieldDao, _ := NewFieldDao(nil, context.TODO())
	fields, err := fieldDao.ReadFieldPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("FieldDaoImpl ReadFieldPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(fields)
		t.Log("FieldDaoImpl ReadFieldPage success, data=", string(bytes))
	}
}

func aTestFieldDaoImpl_FindAll(t *testing.T) {
	fieldDao, _ := NewFieldDao(nil, context.TODO())
	fields, err := fieldDao.FindAll()
	if err != nil {
		t.Errorf("FieldDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(fields)
		t.Log("FieldDaoImpl FindAll success, data=", string(bytes))
	}
}

func aTestFieldDaoImpl_ReadField(t *testing.T) {
	fieldDao, _ := NewFieldDao(nil, context.TODO())
	field, err := fieldDao.ReadField(1)
	if err != nil {
		t.Errorf("FieldDaoImpl ReadField fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(field)
		t.Log("FieldDaoImpl ReadField success, data=", string(bytes))
	}
}
