package configure

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"git.platform.io/environment/environment/model/configure"

	. "git.platform.io/environment/environment/dao/test"
)

func TestRouteReviseDaoImpl_CreateRouteRevise(t *testing.T) {
	routeRevise := configure.RouteRevise{}
	routeReviseDao, _ := NewRouteReviseDao(nil, context.TODO())
	id, err := routeReviseDao.CreateRouteRevise(&routeRevise)
	if err != nil {
		t.Errorf("RouteReviseDaoImpl CreateRouteRevise fail,as[%s]", err.Error())
	} else {
		t.Log("RouteReviseDaoImpl CreateRouteRevise success, id=", id)
	}
}

func TestRouteReviseDaoImpl_UpdateRouteRevise(t *testing.T) {
	routeRevise := configure.RouteRevise{}
	routeReviseDao, _ := NewRouteReviseDao(nil, context.TODO())
	affected, err := routeReviseDao.UpdateRouteRevise(&routeRevise)
	if err != nil {
		t.Errorf("RouteReviseDaoImpl UpdateRouteRevise fail,as[%s]", err.Error())
	} else {
		t.Log("RouteReviseDaoImpl UpdateRouteRevise success, affected=", affected)
	}
}

func TestRouteReviseDaoImpl_ReadRouteRevisePage(t *testing.T) {
	routeReviseDao, _ := NewRouteReviseDao(nil, context.TODO())
	routeRevises, err := routeReviseDao.ReadRouteRevisePage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("RouteReviseDaoImpl ReadRouteRevisePage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(routeRevises)
		t.Log("RouteReviseDaoImpl ReadRouteRevisePage success, data=", string(bytes))
	}
}

func TestRouteReviseDaoImpl_FindAll(t *testing.T) {
	routeReviseDao, _ := NewRouteReviseDao(nil, context.TODO())
	routeRevises, err := routeReviseDao.FindAll()
	if err != nil {
		t.Errorf("RouteReviseDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(routeRevises)
		t.Log("RouteReviseDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestRouteReviseDaoImpl_ReadRouteRevise(t *testing.T) {
	routeReviseDao, _ := NewRouteReviseDao(nil, context.TODO())
	routeRevise, err := routeReviseDao.ReadRouteRevise(1)
	if err != nil {
		t.Errorf("RouteReviseDaoImpl ReadRouteRevise fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(routeRevise)
		t.Log("RouteReviseDaoImpl ReadRouteRevise success, data=", string(bytes))
	}
}

func TestRouteReviseDaoImpl_DeleteRouteRevise(t *testing.T) {
	routeReviseDao, _ := NewRouteReviseDao(nil, context.TODO())
	affected, err := routeReviseDao.DeleteRouteRevise(1)
	if err != nil {
		t.Errorf("RouteReviseDaoImpl DeleteRouteRevise fail,as[%s]", err.Error())
	} else {
		t.Log("RouteReviseDaoImpl DeleteRouteRevise success, affected=", affected)
	}
}

func TestMain(m *testing.M) {
	Setup()
	code := m.Run()
	Teardown()
	os.Exit(code)
}
