package configure

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/configure"
)

func aTestReviseDaoImpl_CreateRevise(t *testing.T) {
	revise := configure.Revise{}
	reviseDao, _ := NewReviseDao(nil, context.TODO())
	id, err := reviseDao.CreateRevise(&revise)
	if err != nil {
		t.<PERSON>rf("ReviseDaoImpl CreateRevise fail,as[%s]", err.Error())
	} else {
		t.Log("ReviseDaoImpl CreateRevise success, id=", id)
	}
}

func aTestReviseDaoImpl_DeleteRevise(t *testing.T) {
	reviseDao, _ := NewReviseDao(nil, context.TODO())
	affected, err := reviseDao.DeleteRevise(1)
	if err != nil {
		t.Errorf("ReviseDaoImpl DeleteRevise fail,as[%s]", err.Error())
	} else {
		t.Log("ReviseDaoImpl DeleteRevise success, affected=", affected)
	}
}

func aTestReviseDaoImpl_UpdateRevise(t *testing.T) {
	revise := configure.Revise{}
	reviseDao, _ := NewReviseDao(nil, context.TODO())
	affected, err := reviseDao.UpdateRevise(&revise)
	if err != nil {
		t.Errorf("ReviseDaoImpl UpdateRevise fail,as[%s]", err.Error())
	} else {
		t.Log("ReviseDaoImpl UpdateRevise success, affected=", affected)
	}
}

func aTestReviseDaoImpl_ReadRevisePage(t *testing.T) {
	reviseDao, _ := NewReviseDao(nil, context.TODO())
	revises, err := reviseDao.ReadRevisePage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("ReviseDaoImpl ReadRevisePage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(revises)
		t.Log("ReviseDaoImpl ReadRevisePage success, data=", string(bytes))
	}
}

func aTestReviseDaoImpl_FindAll(t *testing.T) {
	reviseDao, _ := NewReviseDao(nil, context.TODO())
	revises, err := reviseDao.FindAll()
	if err != nil {
		t.Errorf("ReviseDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(revises)
		t.Log("ReviseDaoImpl FindAll success, data=", string(bytes))
	}
}

func aTestReviseDaoImpl_ReadRevise(t *testing.T) {
	reviseDao, _ := NewReviseDao(nil, context.TODO())
	revise, err := reviseDao.ReadRevise("")
	if err != nil {
		t.Errorf("ReviseDaoImpl ReadRevise fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(revise)
		t.Log("ReviseDaoImpl ReadRevise success, data=", string(bytes))
	}
}
