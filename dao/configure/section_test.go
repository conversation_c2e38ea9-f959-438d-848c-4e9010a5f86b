package configure

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/configure"
)

func aTestSectionDaoImpl_CreateSection(t *testing.T) {
	section := configure.Section{}
	sectionDao, _ := NewSectionDao(nil, context.TODO())
	id, err := sectionDao.CreateSection(&section)
	if err != nil {
		t.<PERSON>rf("SectionDaoImpl CreateSection fail,as[%s]", err.Error())
	} else {
		t.Log("SectionDaoImpl CreateSection success, id=", id)
	}
}

func aTestSectionDaoImpl_DeleteSection(t *testing.T) {
	sectionDao, _ := NewSectionDao(nil, context.TODO())
	affected, err := sectionDao.DeleteSection(1)
	if err != nil {
		t.Errorf("SectionDaoImpl DeleteSection fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("SectionDaoImpl DeleteSection success, affected=", affected)
	}
}

func aTestSectionDaoImpl_UpdateSection(t *testing.T) {
	section := configure.Section{}
	sectionDao, _ := NewSectionDao(nil, context.TODO())
	affected, err := sectionDao.UpdateSection(&section)
	if err != nil {
		t.Errorf("SectionDaoImpl UpdateSection fail,as[%s]", err.Error())
	} else {
		t.Log("SectionDaoImpl UpdateSection success, affected=", affected)
	}
}

func aTestSectionDaoImpl_ReadSectionPage(t *testing.T) {
	sectionDao, _ := NewSectionDao(nil, context.TODO())
	sections, err := sectionDao.ReadSectionPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("SectionDaoImpl ReadSectionPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(sections)
		t.Log("SectionDaoImpl ReadSectionPage success, data=", string(bytes))
	}
}

func aTestSectionDaoImpl_FindAll(t *testing.T) {
	sectionDao, _ := NewSectionDao(nil, context.TODO())
	sections, err := sectionDao.FindAll()
	if err != nil {
		t.Errorf("SectionDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(sections)
		t.Log("SectionDaoImpl FindAll success, data=", string(bytes))
	}
}

func aTestSectionDaoImpl_ReadSection(t *testing.T) {
	sectionDao, _ := NewSectionDao(nil, context.TODO())
	section, err := sectionDao.ReadSection(1)
	if err != nil {
		t.Errorf("SectionDaoImpl ReadSection fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(section)
		t.Log("SectionDaoImpl ReadSection success, data=", string(bytes))
	}
}

func aTestGetSectionList(t *testing.T) {
	filter := configure.SectionFilter{
		StackID:    0,
		DomainList: []string{configure.SectionDomainAll, configure.SectionDomainService, "dxc"},
		// TargetCode: "dxc",
	}

	sectionDao, _ := NewSectionDao(nil, context.TODO())
	sections, err := sectionDao.ReadByFilter(&filter)
	if err != nil {
		t.Errorf("SectionDaoImpl ReadByFilter fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(sections)
		t.Log("SectionDaoImpl ReadByFilter success, data=", string(bytes))
	}
}
