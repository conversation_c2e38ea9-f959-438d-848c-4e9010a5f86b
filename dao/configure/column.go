package configure

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/configure"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

// Data Access Object for configure.Column
type ColumnDaoImpl struct {
	database.DaoImpl
}

// new a data access object for column
func NewColumnDao(session *xorm.Session, ctx context.Context) (*ColumnDaoImpl, error) {
	columnDao := ColumnDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(configure.Column)
	if err := columnDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	columnDao.NewSlice = func() interface{} {
		slice := make([]configure.Column, 0)
		return &slice
	}

	return &columnDao, nil
}

func (d *ColumnDaoImpl) CreateColumn(column *configure.Column) (int64, error) {
	affected, err := d.Create(column)
	if err != nil {
		log.Errorf(d.Ctx, "ColumnDaoImpl CreateColumn [%v] fail,as:%s", column, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ColumnDaoImpl) DeleteColumn(id string) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "ColumnDaoImpl DeleteColumn [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ColumnDaoImpl) UpdateColumn(column *configure.Column, fields ...string) (int64, error) {
	if column == nil {
		return 0, fmt.Errorf("invalid paramter, column=nil")
	}

	affected, err := d.Update(column.ID, column, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "ColumnDaoImpl UpdateColumn [%v] fail,as:%s", column.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ColumnDaoImpl) FindAll() ([]configure.Column, error) {
	var columns []configure.Column
	err := d.SortListBy(&columns, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "ColumnDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return columns, nil
}

func (d *ColumnDaoImpl) ReadColumn(columnId string) (*configure.Column, error) {
	var column configure.Column
	exist, err := d.FindById(columnId, &column)
	if err != nil {
		log.Errorf(d.Ctx, "ColumnDaoImpl Read [%s] fail,as:%s", columnId, err.Error())
		return nil, err
	} else if exist {
		return &column, nil
	} else {
		log.Debugf(d.Ctx, "ColumnDaoImpl the column id=[%s] not exist", columnId)
		return nil, fmt.Errorf("the column [%s] not exist", columnId)
	}
}

func (d *ColumnDaoImpl) ReadColumnPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "ColumnDaoImpl ReadColumnPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

//func (d *ColumnDaoImpl) ReadColumnInfo(columnId string) (*configure.ColumnInfo, error) {
//	var column configure.ColumnInfo
//	filter := configure.ColumnFilter{}
//	conditions := filter.GetJoinCondition()
//	exist, err := d.JoinFindById(columnId, &column, conditions)
//	if err != nil {
//		log.Errorf("ColumnDaoImpl ReadColumnInfo [%s] fail,as:%s", columnId, err.Error())
//		return nil, err
//	} else if exist {
//		return &column, nil
//	} else {
//		log.Debugf("ColumnDaoImpl the column id=[%s] not exist", columnId)
//		return nil, fmt.Errorf("the column [%s] not exist", columnId)
//	}
//}

func (d *ColumnDaoImpl) ReadRecordPageByFilter(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "ColumnDaoImpl FindColumnPage") {
		return nil, err
	} else {
		return page, err
	}
}

//func (d *ColumnDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
//	conditions := filter.GetJoinCondition()
//	query, args := filter.ToAliasSql()
//
//	records := make([]configure.ColumnInfo, 0)
//	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
//	if log.IfError(err, "ColumnDaoImpl ReadInfoPageByFilter") {
//		return nil, err
//	} else {
//		return page, err
//	}
//}
//
//func (d *ColumnDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]configure.ColumnInfo, error) {
//	records := make([]configure.ColumnInfo, 0)
//	err := d.JoinQueryByFilter(&records, filter)
//	if log.IfError(err, "ColumnDaoImpl ReadInfoListByFilter") {
//		return nil, err
//	} else {
//		return records, err
//	}
//}
//
//func (d *ColumnDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*configure.ColumnInfo, error) {
//	conditions := filter.GetJoinCondition()
//	query, args := filter.ToAliasSql()
//
//	record := new(configure.ColumnInfo)
//	exist, err := d.JoinFind(record, conditions, query, args...)
//	if err != nil {
//		log.Errorf("ColumnDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
//		return nil, err
//	} else if exist {
//		return record, nil
//	} else {
//		log.Debugf("ColumnDaoImpl the record [%++v] not exist", filter)
//		return nil, fmt.Errorf("the record [%++v] not exist", filter)
//	}
//}

func (d *ColumnDaoImpl) ReadByFilter(filter information.IFilter) ([]configure.Column, error) {
	records := make([]configure.Column, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "ColumnDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *ColumnDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *ColumnDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *ColumnDaoImpl) Entity() information.IRecord {
	return new(configure.Column)
}

func (d *ColumnDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(configure.ColumnFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	return filter, err
}
