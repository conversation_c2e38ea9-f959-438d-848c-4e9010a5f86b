package alertrule

import (
	"context"
	"fmt"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/alertrule"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

type AlertRuleTemplateDaoImpl struct {
	database.DaoImpl
}

func NewAlertRuleTemplateDao(session *xorm.Session, ctx context.Context) *AlertRuleTemplateDaoImpl {
	dao := AlertRuleTemplateDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(alertrule.AlertRuleTemplate)
	dao.Init(m.TableName(), m, session)
	dao.NewSlice = func() interface{} {
		var slice []alertrule.AlertRuleTemplate
		return &slice
	}

	return &dao
}

func (d *AlertRuleTemplateDaoImpl) CreateAlertRuleTemplate(record *alertrule.AlertRuleTemplate) (int64, error) {
	affected, err := d.Create(record)
	if err != nil {
		log.Errorf(d.Ctx, "AlertRuleDaoImpl CreateAlertRule [%v] fail,as:%s", record, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AlertRuleTemplateDaoImpl) UpdateAlertRuleTemplate(record *alertrule.AlertRuleTemplate) (int64, error) {
	if record == nil {
		return 0, fmt.Errorf("invalid paramter, record=nil")
	}

	affected, err := d.Update(record.ID, record)
	if err != nil {
		log.Errorf(d.Ctx, "AlertRuleDaoImpl UpdateAlertRule [%v] fail,as:%s", record.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AlertRuleTemplateDaoImpl) ReadAlertRuleTemplate(id uint64) (*alertrule.AlertRuleTemplate, error) {
	var record alertrule.AlertRuleTemplate
	exist, err := d.FindById(id, &record)
	if err != nil {
		log.Errorf(d.Ctx, "AlertRuleDaoImpl Read [%d] fail,as:%s", id, err.Error())
		return nil, err
	} else if exist {
		return &record, nil
	} else {
		log.Debugf(d.Ctx, "AlertRuleDaoImpl the record id=[%d] not exist", id)
		return nil, fmt.Errorf("the record [%d] not exist", id)
	}
}

func (d *AlertRuleTemplateDaoImpl) ReadByFilter(filter *alertrule.AlertRuleTemplateFilter) ([]alertrule.AlertRuleTemplate, error) {
	query, args := filter.ToSql()
	records := make([]alertrule.AlertRuleTemplate, 0)
	err := d.ListBy(&records, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "AlertRuleDaoImpl ReadByFilter [%v] fail,as:%s", filter, err.Error())
		return nil, err
	} else {
		return records, nil
	}
}

func (d *AlertRuleTemplateDaoImpl) CheckExist(id string) (bool, error) {
	return d.Exist("id=?", id)
}
