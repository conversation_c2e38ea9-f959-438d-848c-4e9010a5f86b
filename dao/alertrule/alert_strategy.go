package alertrule

import (
	"context"
	"fmt"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/alertrule"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

type AlertStrategyDaoImpl struct {
	database.DaoImpl
}

func NewAlertStrategyDao(session *xorm.Session, ctx context.Context) *AlertStrategyDaoImpl {
	dao := AlertStrategyDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(alertrule.AlertStrategy)
	dao.Init(m.TableName(), m, session)
	dao.NewSlice = func() interface{} {
		var slice []alertrule.AlertStrategy
		return &slice
	}

	return &dao
}

func (d *AlertStrategyDaoImpl) CreateAlertStrategy(record *alertrule.AlertStrategy) (int64, error) {
	affected, err := d.Create(record)
	if err != nil {
		log.Errorf(d.Ctx, "AlertStrategyDaoImpl CreateAlertStrategy [%v] fail,as:%s", record, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AlertStrategyDaoImpl) UpdateAlertStrategy(record *alertrule.AlertStrategy) (int64, error) {
	if record == nil {
		return 0, fmt.Errorf("invalid paramter, record=nil")
	}

	affected, err := d.Update(record.ID, record)
	if err != nil {
		log.Errorf(d.Ctx, "AlertStrategyDaoImpl UpdateAlertStrategy [%v] fail,as:%s", record.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AlertStrategyDaoImpl) ReadAlertStrategy(id uint64) (*alertrule.AlertStrategy, error) {
	var record alertrule.AlertStrategy
	exist, err := d.FindById(id, &record)
	if err != nil {
		log.Errorf(d.Ctx, "AlertStrategyDaoImpl Read [%d] fail,as:%s", id, err.Error())
		return nil, err
	} else if exist {
		return &record, nil
	} else {
		log.Debugf(d.Ctx, "AlertStrategyDaoImpl the record id=[%d] not exist", id)
		return nil, fmt.Errorf("the record [%d] not exist", id)
	}
}

func (d *AlertStrategyDaoImpl) ReadByFilter(filter *alertrule.AlertStrategyFilter) ([]alertrule.AlertStrategy, error) {
	query, args := filter.ToSql()
	records := make([]alertrule.AlertStrategy, 0)
	err := d.ListBy(&records, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "AlertStrategyDaoImpl ReadByFilter [%v] fail,as:%s", filter, err.Error())
		return nil, err
	} else {
		return records, nil
	}
}

func (d *AlertStrategyDaoImpl) CheckExist(name string) (bool, error) {
	return d.Exist("name=?", name)
}
