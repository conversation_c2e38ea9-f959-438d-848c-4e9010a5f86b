package alertrule

import (
	"context"
	"fmt"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/alertrule"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

type AlertRuleDaoImpl struct {
	database.DaoImpl
}

func NewAlertRuleDao(session *xorm.Session, ctx context.Context) *AlertRuleDaoImpl {
	dao := AlertRuleDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(alertrule.AlertRule)
	dao.Init(m.TableName(), m, session)
	dao.NewSlice = func() interface{} {
		var slice []alertrule.AlertRule
		return &slice
	}

	return &dao
}

func (d *AlertRuleDaoImpl) CreateAlertRule(record *alertrule.AlertRule) (int64, error) {
	affected, err := d.Create(record)
	if err != nil {
		log.Errorf(d.Ctx, "AlertRuleDaoImpl CreateAlertRule [%v] fail,as:%s", record, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AlertRuleDaoImpl) UpdateAlertRule(record *alertrule.AlertRule) (int64, error) {
	if record == nil {
		return 0, fmt.Errorf("invalid paramter, record=nil")
	}

	affected, err := d.Update(record.ID, record)
	if err != nil {
		log.Errorf(d.Ctx, "AlertRuleDaoImpl UpdateAlertRule [%v] fail,as:%s", record.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AlertRuleDaoImpl) ReadAlertRule(id uint64) (*alertrule.AlertRule, error) {
	var record alertrule.AlertRule
	exist, err := d.FindById(id, &record)
	if err != nil {
		log.Errorf(d.Ctx, "AlertRuleDaoImpl Read [%d] fail,as:%s", id, err.Error())
		return nil, err
	} else if exist {
		return &record, nil
	} else {
		log.Debugf(d.Ctx, "AlertRuleDaoImpl the record id=[%d] not exist", id)
		return nil, fmt.Errorf("the record [%d] not exist", id)
	}
}

func (d *AlertRuleDaoImpl) ReadByFilter(filter *alertrule.AlertRuleFilter) ([]alertrule.AlertRule, error) {
	query, args := filter.ToSql()
	records := make([]alertrule.AlertRule, 0)
	err := d.ListBy(&records, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "AlertRuleDaoImpl ReadByFilter [%v] fail,as:%s", filter, err.Error())
		return nil, err
	} else {
		return records, nil
	}
}

func (d *AlertRuleDaoImpl) CheckExist(id string) (bool, error) {
	return d.Exist("id=?", id)
}
