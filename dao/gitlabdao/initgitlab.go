package gitlabdao

import (
	commonconfig "git.multiverse.io/eventkit/kit/handler/config"
	"github.com/xanzy/go-gitlab"
)

var (
	GitlabClient       *gitlab.Client
	GitlabCUDClient    *gitlab.Client
	Environmentconfigs *commonconfig.ServiceConfigs
)

// func GetGitLabClient(gitlabConfig ci.GitLabConfig) (*gitlab.Client, *model.EnvironmentError) {
// 	gitlabConfig.Url = strings.TrimSpace(gitlabConfig.Url)
// 	if len(gitlabConfig.Url) < 4 {
// 		return nil, model.Error(constants.ERROR_GITLAB_CONNECT_FAILED, "gitlab initialization failed, please check gitlab.url")
// 	}
// 	gitlabUrl := ""
// 	if gitlabConfig.Url[0:4] == "http" {
// 		gitlabUrl = fmt.Sprintf("%s/api/v4", gitlabConfig.Url)
// 	} else {
// 		gitlabUrl = fmt.Sprintf("http://%s/api/v4", gitlabConfig.Url)
// 	}
// 	gitlabClient, err := gitlab.NewClient(
// 		gitlabConfig.Token,
// 		gitlab.WithBaseURL(gitlabUrl),
// 	)
// 	if err != nil {
// 		return nil, model.Error(constants.ERROR_GITLAB_CONNECT_FAILED, "connect pgsql db failed: "+err.Error())
// 	}
// 	v, _, err := gitlabClient.Version.GetVersion()
// 	if err != nil {
// 		return nil, model.Error(constants.ERROR_GITLAB_CONNECT_FAILED, err.Error())
// 	}
// 	log.Debugsf("gitlab version is %s", v.String())

// 	return gitlabClient, nil
// }

// func Init(cfg *commonconfig.ServiceConfigs) *errors.Error {
// 	if cfg == nil {
// 		return errors.Wrap(
// 			fmt.Sprintf("%d", constants.ERROR_INTERNAL_INIT_CONFIG_FAILED),
// 			"Init congif is failed", 0)
// 	}
// 	Environmentconfigs = cfg
// 	return initGitlab(ci.GitLabConfig{
// 		Url:     Environmentconfigs.GetString("gitlab.url"),
// 		User:    "",
// 		Passwd:  "",
// 		Token:   Environmentconfigs.GetString("gitlab.private_token"),
// 		Debug:   false,
// 		Timeout: Environmentconfigs.GetInt("gitlab.timeout"),
// 		Group:   Environmentconfigs.GetString("gitlab.group"),
// 	})
// }

// func initGitlab(gitlabConfig ci.GitLabConfig) *errors.Error {
// 	gc, err := GetGitLabClient(gitlabConfig)
// 	if err != nil {
// 		return errors.Wrap(err.ErrorMsg, err.ErrorCode, 0)
// 	}
// 	GitlabClient = gc
// 	gc2, err := GetGitLabClient(gitlabConfig)
// 	if err != nil {
// 		return errors.Wrap(err.ErrorMsg, err.ErrorCode, 0)
// 	}
// 	GitlabCUDClient = gc2
// 	return nil
// }
