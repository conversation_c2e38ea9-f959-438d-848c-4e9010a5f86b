package gitlabdao

import (
	"github.com/xanzy/go-gitlab"
)

type UpdateFileInfo struct {
	Name    string
	Action  gitlab.FileActionValue
	Content []byte
}
type CommisMsg struct {
	Operator string `json:"operator,omitempty"`
	User     string `json:"user,omitempty"`
	Version  string `json:"version,omitempty"`
	File     string `json:"file,omitempty"`
	Branch   string `json:"branch,omitempty"`
	Reason   string `json:"reason,omitempty"`
}

// func CreateProject(branch, desc, pname, remote_url string, pnsid int, initreadme bool) (*gitlab.Project, *model.EnvironmentError) {
// 	client := GitlabCUDClient
// 	if projectList, err := ListProjectInGroup(client, pnsid, 1, 10000); err != nil {
// 		return nil, err
// 	} else {
// 		for _, project := range projectList {
// 			if project.Name == pname {
// 				log.Infosf("Project %s already exists,skip!", pname)
// 				return project, nil
// 			}
// 		}
// 	}

// 	project, _, err := GitlabClient.Projects.CreateProject(&gitlab.CreateProjectOptions{
// 		AllowMergeOnSkippedPipeline:              nil,
// 		ApprovalsBeforeMerge:                     nil,
// 		AutoCancelPendingPipelines:               nil,
// 		AutoDevopsDeployStrategy:                 nil,
// 		AutoDevopsEnabled:                        nil,
// 		AutocloseReferencedIssues:                nil,
// 		BuildCoverageRegex:                       nil,
// 		BuildGitStrategy:                         nil,
// 		BuildTimeout:                             nil,
// 		BuildsAccessLevel:                        nil,
// 		CIConfigPath:                             nil,
// 		ContainerExpirationPolicyAttributes:      nil,
// 		DefaultBranch:                            &branch,
// 		Description:                              &desc,
// 		EmailsDisabled:                           nil,
// 		ExternalAuthorizationClassificationLabel: nil,
// 		ForkingAccessLevel:                       nil,
// 		GroupWithProjectTemplatesID:              nil,
// 		// ImportURL:                                &remote_url,
// 		ImportURL:                nil,
// 		InitializeWithReadme:     &initreadme,
// 		IssuesAccessLevel:        nil,
// 		LFSEnabled:               nil,
// 		MergeMethod:              nil,
// 		MergeRequestsAccessLevel: nil,
// 		Mirror:                   nil,
// 		MirrorTriggerBuilds:      nil,
// 		Name:                     &pname,
// 		NamespaceID:              &pnsid,
// 		OnlyAllowMergeIfAllDiscussionsAreResolved: nil,
// 		OnlyAllowMergeIfPipelineSucceeds:          nil,
// 		OperationsAccessLevel:                     nil,
// 		PackagesEnabled:                           nil,
// 		PagesAccessLevel:                          nil,
// 		Path:                                      &pname,
// 		PrintingMergeRequestLinkEnabled:           nil,
// 		PublicBuilds:                              nil,
// 		RemoveSourceBranchAfterMerge:              nil,
// 		RepositoryAccessLevel:                     nil,
// 		RequestAccessEnabled:                      nil,
// 		ResolveOutdatedDiffDiscussions:            nil,
// 		SharedRunnersEnabled:                      nil,
// 		SnippetsAccessLevel:                       nil,
// 		SuggestionCommitMessage:                   nil,
// 		TemplateName:                              nil,
// 		TemplateProjectID:                         nil,
// 		UseCustomTemplate:                         nil,
// 		Visibility:                                nil,
// 		WikiAccessLevel:                           nil,
// 		CIForwardDeploymentEnabled:                nil,
// 		ContainerRegistryEnabled:                  nil,
// 		IssuesEnabled:                             nil,
// 		IssuesTemplate:                            nil,
// 		JobsEnabled:                               nil,
// 		MergeRequestsEnabled:                      nil,
// 		MergeRequestsTemplate:                     nil,
// 		ServiceDeskEnabled:                        nil,
// 		SnippetsEnabled:                           nil,
// 		TagList:                                   nil,
// 		WikiEnabled:                               nil,
// 	})
// 	if err != nil {
// 		return project, model.Error(constants.ERROR_GITLAB_CREATEPORJECT_FAILED, err)
// 	}

// 	log.Infosf("init env service project: %++v", project)
// 	return project, nil
// }

// func CreateMultiProjects(pname []*string, branch, desc, remote_url string, pnsid int, initreadme bool) ([]*gitlab.Project, *model.EnvironmentError) {
// 	var projects []*gitlab.Project

// 	for i := 0; i < len(pname); i++ {

// 		project, err := CreateProject(branch, desc, *pname[i], remote_url, pnsid, initreadme)
// 		if err != nil {
// 			return projects, model.Error(constants.ERROR_GITLAB_CREATEPORJECT_FAILED, err)
// 		}

// 		projects = append(projects, project)
// 	}

// 	return projects, nil
// }

// func CreateProjectFile(client *gitlab.Client, projectid int, branch, user, fileName string, fileContent []byte) (*gitlab.FileInfo, *model.EnvironmentError) {
// 	initversion := fmt.Sprintf("%s_%s.%s",
// 		branch,
// 		user,
// 		time.Now().Format("20060102150405"),
// 	)
// 	cm := CommisMsg{
// 		Operator: "CreateFile",
// 		User:     user,
// 		Version:  initversion,
// 		File:     fileName,
// 		Branch:   branch,
// 	}
// 	b, _ := json.Marshal(cm)
// 	file, _, err := client.RepositoryFiles.CreateFile(
// 		projectid,
// 		fileName,
// 		&gitlab.CreateFileOptions{
// 			Branch:        gitlab.String(branch),
// 			StartBranch:   nil,
// 			Encoding:      nil,
// 			AuthorEmail:   nil,
// 			AuthorName:    gitlab.String(user),
// 			Content:       gitlab.String(string(fileContent)),
// 			CommitMessage: gitlab.String(string(b)),
// 		})
// 	if err != nil {
// 		log.Errorsf("write files to db failed as:[%s]", err)
// 		return nil, model.Error(constants.ERROR_GITLAB_CREATEFILE_FAILED, err.Error())
// 	}
// 	return file, nil
// }

// func GetFileRawContent(client *gitlab.Client, projectid int, filename, branch string) ([]byte, *model.EnvironmentError) {
// 	content, _, err := client.RepositoryFiles.GetRawFile(
// 		projectid,
// 		filename,
// 		&gitlab.GetRawFileOptions{Ref: gitlab.String(branch)},
// 	)
// 	if err != nil {
// 		log.Errorsf("get raw files failed %s", err)
// 		return nil, model.Error(constants.ERROR_GITLAB_GETFILE_FAILED,
// 			err.Error())
// 	}
// 	return content, nil
// }

// func UpdateProjectFiles(client *gitlab.Client, projectid int, src map[string]UpdateFileInfo, branch, user string) (*gitlab.Commit, *model.EnvironmentError) {
// 	version := fmt.Sprintf("%s_%s.%s",
// 		branch,
// 		user,
// 		time.Now().Format("20060102150405"),
// 	)
// 	var actions []*gitlab.CommitActionOptions
// 	var files []string
// 	for _, af := range src {
// 		a := &gitlab.CommitActionOptions{
// 			Action:          gitlab.FileAction(af.Action),
// 			FilePath:        gitlab.String(af.Name),
// 			PreviousPath:    nil,
// 			Content:         gitlab.String(string(af.Content)),
// 			Encoding:        nil,
// 			LastCommitID:    nil,
// 			ExecuteFilemode: nil,
// 		}
// 		actions = append(actions, a)
// 		files = append(files, af.Name)
// 	}
// 	cm := CommisMsg{
// 		Operator: "UpdateFiles",
// 		User:     user,
// 		Version:  version,
// 		File:     strings.Join(files, ","),
// 		Branch:   branch,
// 	}
// 	b, _ := json.Marshal(cm)
// 	commitMsg := string(b)
// 	c, _, err := client.Commits.CreateCommit(
// 		projectid,
// 		&gitlab.CreateCommitOptions{
// 			Branch:        gitlab.String(branch),
// 			CommitMessage: gitlab.String(commitMsg),
// 			StartBranch:   nil,
// 			StartSHA:      nil,
// 			StartProject:  nil,
// 			Actions:       actions,
// 			AuthorEmail:   nil,
// 			AuthorName:    nil,
// 			Stats:         nil,
// 			Force:         nil,
// 		},
// 	)
// 	if err != nil {
// 		return nil, model.Error(constants.ERROR_GITLAB_UPDATEFILE_FAILED, err.Error())
// 	}

// 	return c, nil
// }

// func UpdateProjectFile(client *gitlab.Client, projectid int, branch, user, fileName string, fileContent []byte) (*gitlab.FileInfo, *model.EnvironmentError) {
// 	initversion := fmt.Sprintf("%s_%s.%s",
// 		branch,
// 		user,
// 		time.Now().Format("20060102150405"),
// 	)
// 	cm := CommisMsg{
// 		Operator: "UpdateFile",
// 		User:     user,
// 		Version:  initversion,
// 		File:     fileName,
// 		Branch:   branch,
// 	}
// 	b, _ := json.Marshal(cm)
// 	file, _, err := client.RepositoryFiles.UpdateFile(
// 		projectid,
// 		fileName,
// 		&gitlab.UpdateFileOptions{
// 			Branch:        gitlab.String(branch),
// 			StartBranch:   nil,
// 			Encoding:      nil,
// 			AuthorEmail:   nil,
// 			AuthorName:    gitlab.String(user),
// 			Content:       gitlab.String(string(fileContent)),
// 			CommitMessage: gitlab.String(string(b)),
// 			LastCommitID:  nil,
// 		})
// 	if err != nil {
// 		return nil, model.Error(constants.ERROR_GITLAB_CREATEFILE_FAILED, err.Error())
// 	}
// 	return file, nil
// }

// func DeleteProjectFile(client *gitlab.Client, projectid int, branch, user, fileName string) (*gitlab.Response, *model.EnvironmentError) {
// 	initversion := fmt.Sprintf("%s_%s.%s",
// 		branch,
// 		user,
// 		time.Now().Format("20060102150405"),
// 	)
// 	cm := CommisMsg{
// 		Operator: "UpdateFile",
// 		User:     user,
// 		Version:  initversion,
// 		File:     fileName,
// 		Branch:   branch,
// 	}
// 	b, _ := json.Marshal(cm)
// 	file, err := client.RepositoryFiles.DeleteFile(
// 		projectid,
// 		fileName,
// 		&gitlab.DeleteFileOptions{
// 			Branch:        gitlab.String(branch),
// 			StartBranch:   nil,
// 			AuthorEmail:   nil,
// 			AuthorName:    gitlab.String(user),
// 			CommitMessage: gitlab.String(string(b)),
// 			LastCommitID:  nil,
// 		})
// 	if err != nil {
// 		return nil, model.Error(constants.ERROR_GITLAB_CREATEFILE_FAILED, err.Error())
// 	}
// 	return file, nil
// }

// func GetProjectFileIsExists(client *gitlab.Client, projectid int, fileName, branch string) (*gitlab.File, *model.EnvironmentError) {
// 	re := &gitlab.GetFileOptions{
// 		Ref: gitlab.String(branch),
// 	}
// 	f, _, err := client.RepositoryFiles.GetFile(projectid, fileName, re, nil)
// 	if err != nil {
// 		log.Errorsf("get files is exists failed: %s", err)
// 		return nil, model.Error(constants.ERROR_GITLAB_GETMETAFILESINFO_FAILED, err.Error())
// 	}

// 	return f, nil
// }

// func ListProjectInGroup(client *gitlab.Client, groupid, page, pagesize int) ([]*gitlab.Project, *model.EnvironmentError) {
// 	projects, _, err := client.Groups.ListGroupProjects(groupid, &gitlab.ListGroupProjectsOptions{
// 		ListOptions: gitlab.ListOptions{
// 			Page:    page,
// 			PerPage: pagesize,
// 		},
// 	})
// 	if err != nil {
// 		log.Errorsf("list project in group failed %s", err.Error())
// 		return nil, model.Error(constants.ERROR_GITLAB_GETPROJECTID_FAILED, err.Error())
// 	}
// 	return projects, nil
// }

// func GetFileMetaData(client *gitlab.Client, projectid int, filename, branch string) (*gitlab.File, bool, *model.EnvironmentError) {
// 	f, resp, err := client.RepositoryFiles.GetFileMetaData(
// 		projectid,
// 		filename,
// 		&gitlab.GetFileMetaDataOptions{Ref: gitlab.String(branch)},
// 	)
// 	if err != nil {
// 		if resp.StatusCode == http.StatusNotFound {
// 			return f, false, nil
// 		}
// 		return f, false, model.Error(constants.ERROR_GITLAB_ISEXISTSFILE_FAILED,
// 			err.Error())
// 	}
// 	return f, true, nil
// }
