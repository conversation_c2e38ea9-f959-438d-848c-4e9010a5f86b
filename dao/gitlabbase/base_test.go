/**
 * @Author: lichehuo
 * @Mail: <EMAIL>
 * @Enterprise: lichehuo
 * @Description:
 * @File:  base_test
 * @Version: v2023/3/817:32
 * @Date: 2023/3/8 17:32
 */

package gitlabbase

var (
	GITLAB_URL = "************"
	TOKEN      = "_6W_KftLFBN8E5UxxHJ6"
)

// func aTest_queryGroups(t *testing.T) {
// 	clt := GetGitLabClient(GitLabConfig{
// 		Url:     GITLAB_URL,
// 		User:    "",
// 		Passwd:  "",
// 		Token:   TOKEN,
// 		Debug:   false,
// 		Timeout: 0,
// 	})
// 	gs, err := ListGroup(clt, 0, 0, "workspaceInt/WKS_wks06061715/Workspace")
// 	if err != nil {
// 		panic(err)
// 	}
// 	for _, g := range gs {
// 		fmt.Println(g.FullPath)
// 	}
// }

type GitLabConfig struct {
	Url     string
	User    string
	Passwd  string
	Token   string
	Debug   bool
	Timeout int
	Group   string
	GroupID int
}

// func GetGitLabClient(gitlabConfig GitLabConfig) *gitlab.Client {
// 	gitlabUrl := fmt.Sprintf("http://%s/api/v4", gitlabConfig.Url)
// 	gitlabClient, err := gitlab.NewClient(
// 		gitlabConfig.Token,
// 		gitlab.WithBaseURL(gitlabUrl),
// 	)
// 	v, _, err := gitlabClient.Version.GetVersion()
// 	if err != nil {
// 		return nil
// 	}
// 	log.Debugsf("gitlab version is %s", v.String())
// 	return gitlabClient
// }
