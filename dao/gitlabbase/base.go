package gitlabbase

// func CreateGitlabSubGroup(c *gitlab.Client, genvname, desc string, id int) (int, *model.EnvironmentError) {
// 	// var b bool
// 	var err error
// 	grouppars := &gitlab.CreateGroupOptions{
// 		Name:        &genvname,
// 		Path:        &genvname,
// 		Description: &desc,
// 		ParentID:    &id,
// 	}

// 	log.Infosf("crate gitlab subgroup %s ,parentid is :[%d]", genvname, id)
// 	g, _, err := c.Groups.CreateGroup(grouppars)
// 	if err != nil {
// 		if strings.Contains(err.Error(), "has already been taken") {
// 			log.Infosf("%s subgroup alread exists, skip!", genvname)
// 			if subid, err := GitlabSubGroupByName(genvname, id); err != nil {
// 				return g.ID, err
// 			} else {
// 				return subid, nil
// 			}
// 		}
// 		return 0, model.Error(constants.ErrorGitlab, err)
// 	}
// 	return g.ID, nil
// }
// func GitlabSubGroupIsExist(sgname string, id int) (bool, int, *model.EnvironmentError) {

// 	grouppars := &gitlab.ListSubgroupsOptions{
// 		ListOptions: gitlab.ListOptions{
// 			Page:    1,
// 			PerPage: 1000,
// 		},
// 	}

// 	g, _, e := gitlabdao.GitlabClient.Groups.ListSubgroups(id, grouppars)
// 	if e != nil {
// 		return false, 0, model.Error(constants.ErrorGitlab, e)
// 	}
// 	for _, sg := range g {
// 		if sg.Name == sgname {
// 			log.Infosf("get exist gitlab subgroup %s,parentid is :[%d]", sgname, id)
// 			return true, sg.ID, nil
// 		}
// 	}
// 	return false, 0, nil
// }

// func GitlabSubGroupByName(sgname string, id int) (int, *model.EnvironmentError) {

// 	grouppars := &gitlab.ListSubgroupsOptions{
// 		ListOptions: gitlab.ListOptions{
// 			Page:    1,
// 			PerPage: 1000,
// 		},
// 	}
// 	g, _, e := gitlabdao.GitlabClient.Groups.ListSubgroups(id, grouppars)
// 	if e != nil {
// 		return 0, model.Error(constants.ErrorGitlab, e)
// 	}
// 	for _, sg := range g {
// 		if sg.Name == sgname {
// 			return sg.ID, nil
// 		}
// 	}
// 	return 0, nil
// }

// func DeleteSubGroup(subGroupId int) error {
// 	// log.Infosf("delete gitlab subgroup id is :%d", subGroupId)
// 	resp, e := gitlabdao.GitlabClient.Groups.DeleteGroup(subGroupId)
// 	if e != nil {
// 		return e
// 	}
// 	if resp.StatusCode != 202 && resp.StatusCode != 200 {
// 		return fmt.Errorf("delete gitlab subgroup by id %d failed", subGroupId)
// 	}
// 	return nil
// }
// func GetGroupDetail(subGroupId int) (*gitlab.Group, error) {
// 	group := &gitlab.GetGroupOptions{}
// 	log.Infosf("get gitlab subgroup detail by id is :%d", subGroupId)
// 	g, resp, e := gitlabdao.GitlabClient.Groups.GetGroup(subGroupId, group)
// 	if e != nil {
// 		return nil, e
// 	}
// 	if resp.StatusCode != 200 {
// 		return nil, fmt.Errorf("get gitlab subgroup detail by id %d failed.", subGroupId)
// 	}
// 	return g, nil
// }
// func CreateGitlabProject(env string, sgid int) (int, *model.EnvironmentError) {

// 	p, err := gitlabdao.CreateProject("master", "init env service project.", env, "", sgid, true)
// 	if err != nil {
// 		return 0, err
// 	}
// 	return p.ID, nil
// }

// func CreateGitlabMultiProjects(env []*string, sgid int) ([]*gitlab.Project, *model.EnvironmentError) {

// 	p, err := gitlabdao.CreateMultiProjects(env, "master", "init env service projects.", "", sgid, true)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return p, nil
// }

// func GetProjectInGroupID(servicetype string, sgid, page, size int) (int, *model.EnvironmentError) {
// 	c := gitlabdao.GitlabCUDClient
// 	p, err := gitlabdao.ListProjectInGroup(c, sgid, page, size)
// 	if err != nil {
// 		// log.Infosf("get projects failed %s\n", err.ErrorMsg)
// 		return constants.ERROR_GITLAB_GETPROJECTID_FAILED, err
// 	}
// 	for _, v := range p {
// 		if v.Name == servicetype {
// 			return v.ID, nil
// 		}
// 	}
// 	return 0, nil
// }

// func GetProjectInGroupIDWithCreate(project string, sgid, page, size int, groupFlag bool) (int, *model.EnvironmentError) {
// 	c := gitlabdao.GitlabCUDClient
// 	p, err := gitlabdao.ListProjectInGroup(c, sgid, page, size)
// 	if err != nil {
// 		// log.Infosf("get projects failed %s\n", err.ErrorMsg)
// 		return constants.ERROR_GITLAB_GETPROJECTID_FAILED, err
// 	}
// 	for _, v := range p {
// 		if v.Name == project {
// 			return v.ID, nil
// 		}
// 	}
// 	// create
// 	if groupFlag {
// 		return CreateGitlabSubGroup(c, project, project, sgid)
// 	} else {
// 		return CreateGitlabProject(project, sgid)
// 	}
// }

// func WirteAndUpdateGfile(client *gitlab.Client, id int, file string, scfg []byte) *app.Fault {
// 	//Modify client to mock a real gitlab server address.
// 	client = gitlabdao.GitlabCUDClient
// 	//Get service project file,if not exist will be create.
// 	_, b, err := gitlabdao.GetFileMetaData(client, id, file, "master")
// 	if err != nil {
// 		return app.NewFaultf(errcode.GetFileMetadataFail, "get service project file metadata fail,as[%s]", err.ErrorMsg)
// 	}
// 	if b {
// 		_, err := gitlabdao.UpdateProjectFile(client, id, "master", "", file, scfg)
// 		if err != nil {
// 			return app.NewFaultf(errcode.UpdateFileFail, "update service project file fail,as[%s]", err.ErrorMsg)
// 		}
// 	} else {
// 		_, err := gitlabdao.CreateProjectFile(client, id, "master", "", file, scfg)
// 		if err != nil {
// 			return app.NewFaultf(errcode.WriteFileFail, "write service project file fail,as[%s]", err.ErrorMsg)
// 		}
// 	}
// 	return nil
// }

// func ListGroup(client *gitlab.Client, page, pagesize int, searchGroupName string) ([]*gitlab.Group, *model.EnvironmentError) {
// 	if client == nil {
// 		return nil, model.Error(constants.ErrorGitlab, fmt.Sprintf("gitlab client is empty."))
// 	}

// 	search := gitlab.String("")
// 	targetSearchList := strings.Split(searchGroupName, "/")
// 	if len(targetSearchList) < 3 {
// 		search = nil
// 	} else {
// 		search = gitlab.String(targetSearchList[1])
// 	}

// 	gs, resp, err := client.Groups.ListGroups(
// 		&gitlab.ListGroupsOptions{
// 			ListOptions: gitlab.ListOptions{
// 				Page:    page,
// 				PerPage: pagesize,
// 			},
// 			AllAvailable:         nil,
// 			MinAccessLevel:       nil,
// 			OrderBy:              nil,
// 			Owned:                nil,
// 			Search:               search,
// 			SkipGroups:           nil,
// 			Sort:                 nil,
// 			Statistics:           nil,
// 			TopLevelOnly:         nil,
// 			WithCustomAttributes: nil,
// 		})
// 	if err != nil {
// 		if resp.StatusCode == http.StatusNotFound {
// 			return nil, nil
// 		}
// 		return nil, model.Error(constants.ErrorGitlab)
// 	}
// 	currentPage := resp.CurrentPage
// 	nextPage := resp.NextPage
// 	totalPage := resp.TotalPages
// 	groups := make([]*gitlab.Group, 0)
// 	if currentPage == totalPage {
// 		return gs, nil
// 	} else {
// 		groups = append(groups, gs...)
// 	}

// 	for i := nextPage; i <= totalPage; i++ {
// 		gs2, resp2, err2 := client.Groups.ListGroups(
// 			&gitlab.ListGroupsOptions{
// 				ListOptions: gitlab.ListOptions{
// 					Page:    i,
// 					PerPage: pagesize,
// 				},
// 				AllAvailable:         nil,
// 				MinAccessLevel:       nil,
// 				OrderBy:              nil,
// 				Owned:                nil,
// 				Search:               search,
// 				SkipGroups:           nil,
// 				Sort:                 nil,
// 				Statistics:           nil,
// 				TopLevelOnly:         nil,
// 				WithCustomAttributes: nil,
// 			})
// 		if err2 != nil {
// 			if resp2.StatusCode == http.StatusNotFound {
// 				return nil, nil
// 			}
// 			//log.Debugsf("list subgroup is failed:%s", err2.Error())
// 			return nil, model.Error(constants.ErrorGitlab)
// 		}
// 		groups = append(groups, gs2...)
// 	}
// 	return groups, nil
// }

// func GetSubGroupID(subGroupPath string) (int, *model.EnvironmentError) {
// 	//fmt.Printf("subGroupPath : %s\n", subGroupPath)
// 	if strings.HasPrefix(subGroupPath, "/") {
// 		subGroupPath = strings.TrimPrefix(subGroupPath, "/")
// 	}

// 	//orggitpath := strings.Trim(subGroupPath, "/")
// 	client := gitlabdao.GitlabCUDClient
// 	groups, err := ListGroup(client, 0, 0, subGroupPath)

// 	if err != nil {
// 		return 0, err
// 	}
// 	if len(groups) == 0 {
// 		return 0, model.Error(constants.ErrorGitlab)
// 	}
// 	//fmt.Printf("group length : %d\n", len(groups))
// 	for _, group := range groups {
// 		//gName := group.FullName
// 		//gpath := group.Path
// 		fullPath := group.FullPath
// 		//fmt.Printf("group name : %s\n", gName)
// 		//fmt.Printf("patch group name : %s\n", strings.Trim(subGroupPath, "/"))
// 		//fmt.Printf("gpath name : %s\n", gpath)
// 		//fmt.Printf("fullPath name : %s\n", fullPath)
// 		if strings.Trim(fullPath, "/") == strings.Trim(subGroupPath, "/") {
// 			return group.ID, nil
// 		}
// 	}
// 	return 0, nil
// }

// func CommitOneFile(pid int, action *gitlab.CommitActionOptions, msg, branch string) (*gitlab.Commit, *model.EnvironmentError) {
// 	var actions []*gitlab.CommitActionOptions
// 	actions = append(actions, action)
// 	return CommitFiles(pid, actions, msg, branch)
// }

// func CommitFiles(pid int, actions []*gitlab.CommitActionOptions, msg, branch string) (*gitlab.Commit, *model.EnvironmentError) {

// 	commits := &gitlab.CreateCommitOptions{
// 		Branch:        gitlab.String(branch),
// 		CommitMessage: gitlab.String(msg),
// 		Actions:       actions,
// 		Force:         gitlab.Bool(true),
// 	}
// 	c, _, err := gitlabdao.GitlabCUDClient.Commits.CreateCommit(
// 		pid,
// 		commits,
// 	)
// 	if err != nil {
// 		log.Infosf("commit template file failed :%s", err.Error())
// 		return nil, model.Error(constants.ErrorGitlab, err.Error())
// 	}
// 	return c, nil
// }

// func ListTags(pid int) ([]*gitlab.Tag, error) {
// 	client := gitlabdao.GitlabCUDClient
// 	tags, _, err := client.Tags.ListTags(pid, &gitlab.ListTagsOptions{
// 		ListOptions: gitlab.ListOptions{},
// 		OrderBy:     nil,
// 		Search:      nil,
// 		Sort:        nil,
// 	})
// 	return tags, err
// }

// func ListNewestTag(pid int) (*gitlab.Tag, error) {
// 	client := gitlabdao.GitlabCUDClient
// 	tags, _, err := client.Tags.ListTags(pid, &gitlab.ListTagsOptions{
// 		ListOptions: gitlab.ListOptions{
// 			Page:    0,
// 			PerPage: 1,
// 		},
// 		OrderBy: gitlab.String("updated"),
// 		Search:  gitlab.String("v"),
// 		Sort:    gitlab.String("desc"),
// 	})
// 	if err != nil {
// 		return nil, err
// 	}
// 	if len(tags) > 0 {
// 		return tags[0], nil
// 	}
// 	return nil, nil
// }

// func GetTagByName(pid int, tagName string) ([]*gitlab.Tag, error) {
// 	client := gitlabdao.GitlabCUDClient
// 	tags, _, err := client.Tags.ListTags(pid, &gitlab.ListTagsOptions{
// 		ListOptions: gitlab.ListOptions{},
// 		OrderBy:     nil,
// 		Search:      gitlab.String(tagName),
// 		Sort:        nil,
// 	})
// 	return tags, err
// }

// func CreateTag(pid int, tagName, branch, user string) (*gitlab.Tag, error) {
// 	client := gitlabdao.GitlabCUDClient
// 	msg := fmt.Sprintf("new tag by [%s]", user)
// 	tag, _, err := client.Tags.CreateTag(pid, &gitlab.CreateTagOptions{
// 		TagName:            gitlab.String(tagName),
// 		Ref:                gitlab.String(branch),
// 		Message:            gitlab.String(msg),
// 		ReleaseDescription: nil,
// 	})
// 	return tag, err
// }

// func ListProjectFiles(branch, path string, page, pagesize, projectid int) ([]*gitlab.TreeNode, error) {
// 	client := gitlabdao.GitlabCUDClient
// 	filePath := gitlab.String(path)
// 	if path == "" {
// 		filePath = nil
// 	}
// 	var treenodes []*gitlab.TreeNode
// 	ts, resp, err := client.Repositories.ListTree(projectid, &gitlab.ListTreeOptions{
// 		ListOptions: gitlab.ListOptions{
// 			Page:    page,
// 			PerPage: pagesize,
// 		},
// 		Path:      filePath,
// 		Ref:       gitlab.String(branch),
// 		Recursive: nil,
// 	})
// 	if err != nil {
// 		if resp.StatusCode == http.StatusNotFound {
// 			return nil, nil
// 		}
// 		//log.Debugsf("list tree is failed:%s", err.Error())
// 		return nil, err
// 	}

// 	treenodes = append(treenodes, ts...)
// 	currentpage := resp.CurrentPage
// 	nextpage := resp.NextPage
// 	totalpage := resp.TotalPages

// 	if currentpage == totalpage {
// 		if filePath == nil {
// 			treenodes = ListAllTrees(branch, treenodes, nil, projectid)
// 		}
// 		return treenodes, nil
// 	}
// 	for i := nextpage; i <= totalpage; i++ {
// 		ts, resp, err = client.Repositories.ListTree(projectid, &gitlab.ListTreeOptions{
// 			ListOptions: gitlab.ListOptions{
// 				Page:    i,
// 				PerPage: pagesize,
// 			},
// 			Path:      filePath,
// 			Ref:       gitlab.String(branch),
// 			Recursive: nil,
// 		})
// 		if err != nil {
// 			if resp.StatusCode == http.StatusNotFound {
// 				return nil, nil
// 			}
// 			//log.Debugsf("list tree is failed:%s", err.Error())
// 			return nil, err
// 		}
// 		treenodes = append(treenodes, ts...)
// 	}
// 	if filePath == nil {
// 		treenodes = ListAllTrees(branch, treenodes, nil, projectid)
// 	}
// 	return treenodes, nil
// }

// func ListAllTrees(branch string, treeNodes, nextNodes []*gitlab.TreeNode, projId int) []*gitlab.TreeNode {
// 	nodes := treeNodes
// 	if nextNodes != nil {
// 		nodes = nextNodes
// 	}
// 	for _, t := range nodes {
// 		treeType := t.Type
// 		log.Infosf("treeType:%s", treeType)
// 		log.Infosf("treeName:%s", t.Name)
// 		if treeType == "tree" {
// 			ts, _ := ListProjectFiles(branch, t.Path, 0, 0, projId)
// 			treeNodes = append(treeNodes, ts...)
// 			treeNodes = ListAllTrees(branch, treeNodes, ts, projId)
// 		}
// 	}
// 	return treeNodes
// }

type FileData struct {
	Filepath string `json:"filepath"`
	Content  []byte `json:"content"`
	Error    error  `json:"error"`
}

// func FetchFileFromGit(pid int, branch string, treeNode *gitlab.TreeNode, source chan *FileData) {
// 	//filename := treeNode.Name
// 	nodeType := treeNode.Type
// 	filePath := treeNode.Path

// 	if nodeType == "tree" {
// 		source <- nil
// 		return
// 	}
// 	//log.Infosf("fileName[%s]", filename)
// 	log.Infosf("filePath[%s]", filePath)
// 	content, err := GetFileRawContent(gitlabdao.GitlabCUDClient, pid, filePath, branch)
// 	if err != nil {
// 		log.Debugsf("get content is failed:%s", err.Error())
// 	}

// 	source <- &FileData{
// 		Filepath: filePath,
// 		Content:  content,
// 		Error:    err,
// 	}
// }

// func GetAllContent(pid int, branch string, filenames []*gitlab.TreeNode) (map[string][]byte, error) {
// 	start := time.Now()

// 	cnt := len(filenames)
// 	if cnt == 0 {
// 		return nil, nil
// 	}

// 	source := make(chan *FileData)

// 	for _, treeNode := range filenames {
// 		go FetchFileFromGit(pid, branch, treeNode, source)
// 	}

// 	allContent := make(map[string][]byte)
// 	errors := make([]error, 0)
// 	for i := 0; i < cnt; i++ {
// 		fileData, ok := <-source
// 		if ok {
// 			if fileData == nil {
// 				continue
// 			}
// 			if fileData.Error != nil {
// 				errors = append(errors, fileData.Error)
// 			} else {
// 				allContent[fileData.Filepath] = fileData.Content
// 			}
// 		} else {
// 			log.Debugsf("receive file content job finished")
// 			break
// 		}
// 	}

// 	close(source)

// 	log.Debugs("get all file cost time==", time.Since(start))
// 	if len(errors) > 0 {
// 		log.Debugsf("errors=%v", errors)
// 		return nil, errors[0]
// 	}
// 	return allContent, nil
// }

// func GetFileRawContent(client *gitlab.Client, projectid int, filePath, branch string) ([]byte, error) {
// 	content, resp, err := client.RepositoryFiles.GetRawFile(
// 		projectid,
// 		filePath,
// 		&gitlab.GetRawFileOptions{Ref: gitlab.String(branch)},
// 	)
// 	if err != nil {
// 		//log.Debugsf(err.Error())
// 		if resp.StatusCode == http.StatusNotFound {
// 			return nil, nil
// 		}
// 		return nil, err
// 	}
// 	return content, nil
// }
