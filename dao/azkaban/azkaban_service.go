package azkaban

import (
	"context"
	"fmt"
	"time"

	"git.platform.io/resource/information"

	model "git.platform.io/environment/environment/model/azkaban"

	"git.platform.io/environment/environment/database"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

// Data Access Object for ip
type AzkabanServiceDaoImpl struct {
	database.DaoImpl
}

func NewAzkabanServiceDao(session *xorm.Session, ctx context.Context) (*AzkabanServiceDaoImpl, error) {
	dao := AzkabanServiceDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(model.AzkabanService)
	if err := dao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	dao.NewSlice = func() interface{} {
		var slice []model.AzkabanService
		return &slice
	}

	return &dao, nil
}

func (d *AzkabanServiceDaoImpl) NewAzkabanService(name, description, status, instanceNum, version, _type, cpuRequestLimitC, memoryRequestLimitC, nodeIP, port string, k8sClusterId uint64, args ...string) error {
	user := "system"
	if len(args) > 0 {
		user = args[0]
	}

	now := time.Now()
	newAzkabanService := &model.AzkabanService{
		Name:                name,
		Description:         description,
		Status:              status,
		InstanceNum:         instanceNum,
		Version:             version,
		Type:                _type,
		Port:                port,
		K8SClusterID:        k8sClusterId,
		CPURequestLimitC:    cpuRequestLimitC,
		MemoryRequestLimitC: memoryRequestLimitC,
		NodeIP:              nodeIP,
		CreatedAt:           now,
		CreatorId:           user,
		UpdatedAt:           now,
		UpdaterId:           user,
	}
	_, err := d.Create(newAzkabanService)
	if err != nil {
		return err
	} else {
		return nil
	}
}

func (d *AzkabanServiceDaoImpl) CreateAzkabanService(AzkabanService *model.AzkabanService) (int64, error) {
	affected, err := d.Create(AzkabanService)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanServiceDaoImpl CreateAzkabanService [%v] fail,as:%s", AzkabanService, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AzkabanServiceDaoImpl) removeDuplicates(lst *[]interface{}) {
	if len(*lst) == 0 {
		return
	}
	// clone
	mp := make(map[interface{}]int, 0)
	for pos, e := range *lst {
		mp[e] = pos
	}
	*lst = (*lst)[0:0]
	for k, v := range mp {
		(*lst) = append((*lst), k)
		fmt.Println(v)
	}
	return
}

func (d *AzkabanServiceDaoImpl) ModifyAzkabanServices(creates []model.AzkabanService, removes []model.AzkabanService) (string, error) {
	// start session
	alert := ""
	var err error
	session := d.NewSession()
	if session == nil {
		err = fmt.Errorf("AzkabanServiceDaoImpl Create Xorm Session fail, get empty return")
		log.Error(d.Ctx, err.Error())
		return alert, err
	}
	defer func() {
		if err != nil {
			err = session.Rollback()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		} else {
			err = session.Commit()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		}
	}()

	// get create or update records
	ins := make([]interface{}, 0)
	var args []interface{}
	where := "1 = 1"
	for _, relation := range creates {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and cluster_name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
			if len(relation.InstanceNum) > 0 {
				where += " and instance_num = ?"
				args = append(args, relation.InstanceNum)
			}
			if len(relation.Version) > 0 {
				where += " and version = ?"
				args = append(args, relation.Version)
			}
			if len(relation.Type) > 0 {
				where += " and `type` = ?"
				args = append(args, relation.Type)
			}
			if len(relation.CPURequestLimitC) > 0 {
				where += " and cpu_request_limit_c = ?"
				args = append(args, relation.CPURequestLimitC)
			}
			if len(relation.MemoryRequestLimitC) > 0 {
				where += " and memory_request_limit_c = ?"
				args = append(args, relation.MemoryRequestLimitC)
			}
			if len(relation.NodeIP) > 0 {
				where += " and node_ip = ?"
				args = append(args, relation.NodeIP)
			}
		}
		var b bool
		b, err = session.NoAutoCondition(true).Where(where, args...).Get(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
		if b { // update
			_, err = session.NoAutoCondition(true).Cols("cluster_name", "description", "status", "updated_at", "updater_id").Where(where, args...).Update(relation)
			if err != nil {
				log.Error(d.Ctx, err.Error())
				return alert, err
			}
			continue
		}
		if relation.Id > 0 {
			// ignore
			continue
		}

		ins = append(ins, relation)
	}
	if len(ins) > 0 {
		_, err = session.Insert(ins...)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}
	// remove
	for _, relation := range removes {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and cluster_name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
			if len(relation.InstanceNum) > 0 {
				where += " and instance_num = ?"
				args = append(args, relation.InstanceNum)
			}
			if len(relation.Version) > 0 {
				where += " and version = ?"
				args = append(args, relation.Version)
			}
			if len(relation.Type) > 0 {
				where += " and `type` = ?"
				args = append(args, relation.Type)
			}
			if len(relation.CPURequestLimitC) > 0 {
				where += " and cpu_request_limit_c = ?"
				args = append(args, relation.CPURequestLimitC)
			}
			if len(relation.MemoryRequestLimitC) > 0 {
				where += " and memory_request_limit_c = ?"
				args = append(args, relation.MemoryRequestLimitC)
			}
			if len(relation.NodeIP) > 0 {
				where += " and node_ip = ?"
				args = append(args, relation.NodeIP)
			}
		}
		_, err := session.NoAutoCondition(true).Where(where, args...).Delete(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}

	return alert, err
}

func (d *AzkabanServiceDaoImpl) CreateAzkabanServiceByMap(AzkabanService *map[string]interface{}) (int64, error) {
	affected, err := d.CreateWithMap(AzkabanService)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanServiceDaoImpl CreateAzkabanServiceByMap [%v] fail,as:%s", AzkabanService, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AzkabanServiceDaoImpl) DeleteAzkabanService(AzkabanService *model.AzkabanService) (int64, error) {
	affected, err := d.DeleteById(AzkabanService.Id)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanServiceDaoImpl DeleteAzkabanService [%v] fail,as:%s", AzkabanService.Id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AzkabanServiceDaoImpl) UpdateAzkabanService(id uint64, bean *model.AzkabanService, fields ...string) (int64, error) {
	if bean == nil {
		return 0, fmt.Errorf("invalid paramter, AzkabanService=nil")
	}
	if id == 0 {
		return 0, fmt.Errorf("invalid paramter for zero id")
	}
	affected, err := d.Update(id, bean, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanServiceDaoImpl UpdateAzkabanService [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AzkabanServiceDaoImpl) FindAll() (*[]model.AzkabanService, error) {
	AzkabanServices := make([]model.AzkabanService, 0)
	err := d.ListAll(&AzkabanServices)
	if err != nil {
		log.Error(d.Ctx, "AzkabanServiceDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return &AzkabanServices, nil
}

func (d *AzkabanServiceDaoImpl) ReadAzkabanService(AzkabanServiceId uint64) (*model.AzkabanService, error) {
	var AzkabanService model.AzkabanService
	exist, err := d.FindById(AzkabanServiceId, &AzkabanService)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanServiceDaoImpl ReadAzkabanService [%d] fail,as:%s", AzkabanServiceId, err.Error())
		return nil, err
	} else if exist {
		return &AzkabanService, nil
	} else {
		log.Debugf(d.Ctx, "AzkabanServiceDaoImpl the AzkabanService id=[%d] not exist", AzkabanServiceId)
		return nil, fmt.Errorf("the AzkabanService [%d] not exist", AzkabanServiceId)
	}
}

func (d *AzkabanServiceDaoImpl) ReadAzkabanServicePage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanServiceDaoImpl ReadAzkabanServicePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *AzkabanServiceDaoImpl) ReadAzkabanServices(query interface{}, args ...interface{}) (*[]model.AzkabanService, error) {
	result := make([]model.AzkabanService, 0)
	err := d.ListBy(&result, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanServiceDaoImpl ReadAzkabanServicePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return &result, nil
	}
}

func (d *AzkabanServiceDaoImpl) ReadAzkabanServiceByK8SClusterId(k8sClusterId uint64) ([]model.AzkabanService, error) {
	AzkabanServices := make([]model.AzkabanService, 0)
	err := d.ListBy(&AzkabanServices, "k8s_cluster_id = ?", k8sClusterId)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanServiceDaoImpl ReadAzkabanServiceByK8SClusterId k8sClusterId=[%d] fail,as:%s", k8sClusterId, err.Error())
		return nil, err
	}
	return AzkabanServices, nil
}

func (d *AzkabanServiceDaoImpl) ReadAzkabanServiceByPlanId(planId uint64) ([]model.AzkabanService, error) {
	AzkabanServices := make([]model.AzkabanService, 0)
	err := d.ListBy(&AzkabanServices, "plan_id = ?", planId)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanServiceDaoImpl ReadAzkabanServiceByPlanId planId=[%d] fail,as:%s", planId, err.Error())
		return nil, err
	}
	return AzkabanServices, nil
}
