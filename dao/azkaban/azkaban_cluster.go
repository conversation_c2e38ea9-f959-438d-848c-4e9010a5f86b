package azkaban

import (
	"context"
	"fmt"
	"time"

	"git.platform.io/resource/information"

	model "git.platform.io/environment/environment/model/azkaban"

	"git.platform.io/environment/environment/database"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

// Data Access Object for ip
type AzkabanClusterDaoImpl struct {
	database.DaoImpl
}

func NewAzkabanClusterDao(session *xorm.Session, ctx context.Context) (*AzkabanClusterDaoImpl, error) {
	dao := AzkabanClusterDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(model.AzkabanCluster)
	if err := dao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	dao.NewSlice = func() interface{} {
		var slice []model.AzkabanCluster
		return &slice
	}

	return &dao, nil
}

func (d *AzkabanClusterDaoImpl) NewAzkabanCluster(name, description, status string, args ...string) error {
	user := "system"
	if len(args) > 0 {
		user = args[0]
	}

	now := time.Now()
	newAzkabanCluster := &model.AzkabanCluster{
		Name:        name,
		Description: description,
		Status:      status,
		CreatedAt:   now,
		CreatorId:   user,
		UpdatedAt:   now,
		UpdaterId:   user,
	}
	_, err := d.Create(newAzkabanCluster)
	if err != nil {
		return err
	} else {
		return nil
	}
}

func (d *AzkabanClusterDaoImpl) removeDuplicates(lst *[]interface{}) {
	if len(*lst) == 0 {
		return
	}
	// clone
	mp := make(map[interface{}]int, 0)
	for pos, e := range *lst {
		mp[e] = pos
	}
	*lst = (*lst)[0:0]
	for k, v := range mp {
		(*lst) = append((*lst), k)
		fmt.Println(v)
	}
	return
}

func (d *AzkabanClusterDaoImpl) ModifyAzkabanClusters(creates []model.AzkabanCluster, removes []model.AzkabanCluster) (string, error) {
	// start session
	alert := ""
	var err error
	session := d.NewSession()
	if session == nil {
		err = fmt.Errorf("AzkabanClusterDaoImpl Create Xorm Session fail, get empty return")
		log.Error(d.Ctx, err.Error())
		return alert, err
	}
	defer func() {
		if err != nil {
			err = session.Rollback()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		} else {
			err = session.Commit()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		}
	}()

	// get create or update records
	ins := make([]interface{}, 0)
	var args []interface{}
	where := "1 = 1"
	for _, relation := range creates {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
		}
		var b bool
		b, err = session.NoAutoCondition(true).Where(where, args...).Get(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
		if b { // update
			_, err = session.NoAutoCondition(true).Cols("name", "description", "status", "updated_at", "updater_id").Where(where, args...).Update(relation)
			if err != nil {
				log.Error(d.Ctx, err.Error())
				return alert, err
			}
			continue
		}
		if relation.Id > 0 {
			// ignore
			continue
		}

		ins = append(ins, relation)
	}
	if len(ins) > 0 {
		_, err = session.Insert(ins...)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}
	// remove
	for _, relation := range removes {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
		}
		_, err := session.NoAutoCondition(true).Where(where, args...).Delete(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}

	return alert, err
}

func (d *AzkabanClusterDaoImpl) CreateAzkabanClusterByMap(AzkabanCluster *map[string]interface{}) (int64, error) {
	affected, err := d.CreateWithMap(AzkabanCluster)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanClusterDaoImpl CreateAzkabanClusterByMap [%v] fail,as:%s", AzkabanCluster, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AzkabanClusterDaoImpl) DeleteAzkabanCluster(AzkabanCluster *model.AzkabanCluster) (int64, error) {
	affected, err := d.DeleteById(AzkabanCluster.Id)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanClusterDaoImpl DeleteAzkabanCluster [%v] fail,as:%s", AzkabanCluster.Id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AzkabanClusterDaoImpl) UpdateAzkabanCluster(id uint64, bean *model.AzkabanCluster, fields ...string) (int64, error) {
	if bean == nil {
		return 0, fmt.Errorf("invalid paramter, AzkabanCluster=nil")
	}
	if id == 0 {
		return 0, fmt.Errorf("invalid paramter for zero id")
	}
	affected, err := d.Update(id, bean, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanClusterDaoImpl UpdateAzkabanCluster [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AzkabanClusterDaoImpl) FindAll() (*[]model.AzkabanCluster, error) {
	AzkabanClusters := make([]model.AzkabanCluster, 0)
	err := d.ListAll(&AzkabanClusters)
	if err != nil {
		log.Error(d.Ctx, "AzkabanClusterDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return &AzkabanClusters, nil
}

func (d *AzkabanClusterDaoImpl) ReadAzkabanClusterWithName(name string) (*model.AzkabanCluster, error) {
	var AzkabanCluster model.AzkabanCluster
	exist, err := d.Find(&AzkabanCluster, "name = ?", name)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanClusterDaoImpl ReadAzkabanCluster [%s] fail,as:%s", name, err.Error())
		return nil, err
	} else if exist {
		return &AzkabanCluster, nil
	} else {
		log.Debugf(d.Ctx, "AzkabanClusterDaoImpl the AzkabanCluster id=[%s] not exist", name)
		return nil, fmt.Errorf("the AzkabanCluster [%s] not exist", name)
	}
}

func (d *AzkabanClusterDaoImpl) ReadAzkabanCluster(AzkabanClusterId uint64) (*model.AzkabanCluster, error) {
	var AzkabanCluster model.AzkabanCluster
	exist, err := d.FindById(AzkabanClusterId, &AzkabanCluster)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanClusterDaoImpl ReadAzkabanCluster [%d] fail,as:%s", AzkabanClusterId, err.Error())
		return nil, err
	} else if exist {
		return &AzkabanCluster, nil
	} else {
		log.Debugf(d.Ctx, "AzkabanClusterDaoImpl the AzkabanCluster id=[%d] not exist", AzkabanClusterId)
		return nil, fmt.Errorf("the AzkabanCluster [%d] not exist", AzkabanClusterId)
	}
}

func (d *AzkabanClusterDaoImpl) ReadAzkabanClusterPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanClusterDaoImpl ReadAzkabanClusterPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *AzkabanClusterDaoImpl) ReadAzkabanClusters(query interface{}, args ...interface{}) (*[]model.AzkabanCluster, error) {
	result := make([]model.AzkabanCluster, 0)
	err := d.ListBy(&result, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "AzkabanClusterDaoImpl ReadAzkabanClusterPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return &result, nil
	}
}

func (d *AzkabanClusterDaoImpl) CheckAzkabanClusterWithName(name string) (bool, error) {
	var GlsCluster model.AzkabanCluster
	return d.Find(&GlsCluster, "name = ?", name)
}

func (d *AzkabanClusterDaoImpl) ReadByFilter(filter *model.AzkabanClusterFilter) ([]model.AzkabanCluster, error) {
	records := make([]model.AzkabanCluster, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "AzkabanClusterDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}
