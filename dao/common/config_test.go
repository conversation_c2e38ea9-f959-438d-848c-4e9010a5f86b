package common

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model"
)

func TestConfigDaoImpl_CreateConfig(t *testing.T) {
	config := model.Config{
		ID:       "test",
		ParentId: "unittest",
		Value:    "value",
		Label:    "test",
		Notice:   "test",
		Type:     "text",
		SortNum:  1,
	}
	dao, _ := NewConfigDao(nil, context.TODO())
	id, err := dao.CreateConfig(&config)
	if err != nil {
		t.<PERSON>rrorf("ConfigDaoImpl CreateConfig fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("ConfigDaoImpl CreateConfig success, id=", id)
	}
}

func TestConfigDaoImpl_DeleteConfig(t *testing.T) {
	dao, _ := NewConfigDao(nil, context.TODO())
	affected, err := dao.DeleteConfig("3")
	if err != nil {
		t.<PERSON><PERSON>("ConfigDaoImpl DeleteConfig fail,as[%s]", err.Error())
	} else {
		t.Log("ConfigDaoImpl DeleteConfig success, affected=", affected)
	}
}

func TestConfigDaoImpl_UpdateConfig(t *testing.T) {
	config := model.Config{
		ID:       "test",
		ParentId: "unittest",
		Value:    "value",
		Label:    "test",
		Notice:   "test",
		Type:     "text",
		SortNum:  2,
	}
	dao, _ := NewConfigDao(nil, context.TODO())
	affected, err := dao.UpdateConfig(&config)
	if err != nil {
		t.Errorf("ConfigDaoImpl UpdateConfig fail,as[%s]", err.Error())
	} else {
		t.Log("ConfigDaoImpl UpdateConfig success, affected=", affected)
	}
}

func TestConfigDaoImpl_ReadConfigPage(t *testing.T) {
	dao, _ := NewConfigDao(nil, context.TODO())
	configs, err := dao.ReadConfigPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("ConfigDaoImpl ReadConfigPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(configs)
		t.Log("ConfigDaoImpl ReadConfigPage success, data=", string(bytes))
	}
}

func TestConfigDaoImpl_FindAll(t *testing.T) {
	dao, _ := NewConfigDao(nil, context.TODO())
	configs, err := dao.FindAll()
	if err != nil {
		t.Errorf("ConfigDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(configs)
		t.Log("ConfigDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestConfigDaoImpl_ReadConfig(t *testing.T) {
	dao, _ := NewConfigDao(nil, context.TODO())
	config, err := dao.ReadConfig("test")
	if err != nil {
		t.Errorf("ConfigDaoImpl ReadConfig fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(config)
		t.Log("ConfigDaoImpl ReadConfig success, data=", string(bytes))
	}
}
