package common

import (
	"context"
	"fmt"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/auth"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for Permission
type PermissionDaoImpl struct {
	database.DaoImpl
}

func NewPermissionDao(session *xorm.Session, ctx context.Context) (*PermissionDaoImpl, error) {
	dao := PermissionDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(auth.Permission)
	if err := dao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}

	dao.NewSlice = func() interface{} {
		var slice []auth.Permission
		return &slice
	}

	return &dao, nil
}

func (d *PermissionDaoImpl) CreatePermission(permission *auth.Permission) (int64, error) {
	affected, err := d.Create(permission)
	if err != nil {
		log.Errorf(d.Ctx, "PermissionDaoImpl CreatePermission [%v] fail,as:%s", permission, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PermissionDaoImpl) DeletePermission(permissionId uint64) (int64, error) {
	affected, err := d.HardDeleteById(permissionId)
	if err != nil {
		log.Errorf(d.Ctx, "PermissionDaoImpl DeletePermission [%v] fail,as:%s", permissionId, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PermissionDaoImpl) UpdatePermission(permission *auth.Permission, fields ...string) (int64, error) {
	if permission == nil {
		return 0, fmt.Errorf("invalid paramter, permission=nil")
	}

	affected, err := d.Update(permission.ID, permission, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "PermissionDaoImpl UpdatePermission [%v] fail,as:%s", permission.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PermissionDaoImpl) FindAll() ([]auth.Permission, error) {
	var permissions []auth.Permission
	err := d.SortListBy(&permissions, "api ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "PermissionDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return permissions, nil
}

func (d *PermissionDaoImpl) ReadPermission(permissionId uint64) (*auth.Permission, error) {
	var permission auth.Permission
	exist, err := d.FindById(permissionId, &permission)
	if err != nil {
		log.Errorf(d.Ctx, "PermissionDaoImpl Read [%d] fail,as:%s", permissionId, err.Error())
		return nil, err
	} else if exist {
		return &permission, nil
	} else {
		log.Debugf(d.Ctx, "PermissionDaoImpl the permission id=[%d] not exist", permissionId)
		return nil, fmt.Errorf("the permission [%d] not exist", permissionId)
	}
}

func (d *PermissionDaoImpl) ReadPermissionPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "PermissionDaoImpl ReadPermissionPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *PermissionDaoImpl) ReadByCategory(categoryId uint64) ([]auth.Permission, error) {
	records := make([]auth.Permission, 0)
	err := d.ListBy(&records, "category_id=?", categoryId)
	if err != nil {
		log.Errorf(d.Ctx, "PermissionDaoImpl ReadByCategory fail,as:%s", err.Error())
		return nil, err
	} else {
		return records, nil
	}
}

func (d *PermissionDaoImpl) ReadConfigAble() (*[]auth.Permission, error) {
	permissiones := make([]auth.Permission, 0)
	err := d.ListBy(&permissiones, "status=?", auth.Normal)
	if err != nil {
		log.Errorf(d.Ctx, "PermissionDaoImpl ReadPermissionList fail,as:%s", err.Error())
		return nil, err
	} else {
		return &permissiones, nil
	}
}

func (d *PermissionDaoImpl) ReadByAPI(api string) (*auth.Permission, error) {
	var permission auth.Permission
	exist, err := d.Find(&permission, "api=?", api)
	if err != nil {
		log.Errorf(d.Ctx, "PermissionDaoImpl ReadByAPI fail,as:%s", err.Error())
		return nil, err
	} else if exist {
		return &permission, nil
	} else {
		return nil, nil
	}
}

func (d *PermissionDaoImpl) IsPermissionExist(api string) (bool, error) {
	num, err := d.Count("api=?", api)
	if log.IfError(err, "IsPermissionExist") {
		return false, err
	}
	return num > 0, nil
}

func (d *PermissionDaoImpl) FindPermissionPage(filter *auth.PermissionFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "PermissionDaoImpl FindPermissionPage") {
		return nil, err
	} else {
		return page, err
	}
}
