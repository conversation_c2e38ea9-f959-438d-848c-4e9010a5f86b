package common

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/auth"
)

func TestPermissionDaoImpl_CreatePermission(t *testing.T) {
	permission := auth.Permission{}
	dao, _ := NewPermissionDao(nil, context.TODO())
	id, err := dao.CreatePermission(&permission)
	if err != nil {
		t.<PERSON>("PermissionDaoImpl CreatePermission fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("PermissionDaoImpl CreatePermission success, id=", id)
	}
}

func TestPermissionDaoImpl_DeletePermission(t *testing.T) {
	dao, _ := NewPermissionDao(nil, context.TODO())
	affected, err := dao.DeletePermission(3)
	if err != nil {
		t.<PERSON>rf("PermissionDaoImpl DeletePermission fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("PermissionDaoImpl DeletePermission success, affected=", affected)
	}
}

func TestPermissionDaoImpl_UpdatePermission(t *testing.T) {
	permission := auth.Permission{}
	dao, _ := NewPermissionDao(nil, context.TODO())
	affected, err := dao.UpdatePermission(&permission)
	if err != nil {
		t.Errorf("PermissionDaoImpl UpdatePermission fail,as[%s]", err.Error())
	} else {
		t.Log("PermissionDaoImpl UpdatePermission success, affected=", affected)
	}
}

func TestPermissionDaoImpl_ReadPermissionPage(t *testing.T) {
	dao, _ := NewPermissionDao(nil, context.TODO())
	permissions, err := dao.ReadPermissionPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("PermissionDaoImpl ReadPermissionPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(permissions)
		t.Log("PermissionDaoImpl ReadPermissionPage success, data=", string(bytes))
	}
}

func TestPermissionDaoImpl_FindAll(t *testing.T) {
	dao, _ := NewPermissionDao(nil, context.TODO())
	permissions, err := dao.FindAll()
	if err != nil {
		t.Errorf("PermissionDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(permissions)
		t.Log("PermissionDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestPermissionDaoImpl_ReadPermission(t *testing.T) {
	dao, _ := NewPermissionDao(nil, context.TODO())
	permission, err := dao.ReadPermission(1)
	if err != nil {
		t.Errorf("PermissionDaoImpl ReadPermission fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(permission)
		t.Log("PermissionDaoImpl ReadPermission success, data=", string(bytes))
	}
}
