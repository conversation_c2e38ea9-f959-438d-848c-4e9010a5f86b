package common

import (
	"context"
	"fmt"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/auth"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

type AuthorizeDaoImpl struct {
	database.DaoImpl
}

func NewAuthorizeDao(session *xorm.Session, ctx context.Context) (*AuthorizeDaoImpl, error) {
	dao := AuthorizeDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(auth.Authorize)
	dao.Init(m.TableName(), m, session)
	dao.NewSlice = func() interface{} {
		var slice []auth.Authorize
		return &slice
	}

	return &dao, nil
}

func (d *AuthorizeDaoImpl) CreateAuthorize(authorize *auth.Authorize) (int64, error) {
	affected, err := d.Create(authorize)
	if err != nil {
		log.Errorf(d.Ctx, "AuthorizeDaoImpl CreateAuthorize [%v] fail,as:%s", authorize, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AuthorizeDaoImpl) DeleteAuthorize(roleId uint64) (int64, error) {
	affected, err := d.HardDeleteById(roleId)
	if err != nil {
		log.Errorf(d.Ctx, "AuthorizeDaoImpl DeleteAuthorize [%v] fail,as:%s", roleId, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AuthorizeDaoImpl) UpdateAuthorize(authorize *auth.Authorize, fields ...string) (int64, error) {
	if authorize == nil {
		return 0, fmt.Errorf("invalid paramter, authorize=nil")
	}

	affected, err := d.Update(authorize.ID, authorize, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "AuthorizeDaoImpl UpdateAuthorize [%v] fail,as:%s", authorize.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AuthorizeDaoImpl) FindAll() (*[]auth.Authorize, error) {
	var authorizes []auth.Authorize
	err := d.SortListBy(&authorizes, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "AuthorizeDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return &authorizes, nil
}

func (d *AuthorizeDaoImpl) ReadAuthorize(roleId uint64) (*auth.Authorize, error) {
	var authorize auth.Authorize
	exist, err := d.FindById(roleId, &authorize)
	if err != nil {
		log.Errorf(d.Ctx, "AuthorizeDaoImpl Read [%d] fail,as:%s", roleId, err.Error())
		return nil, err
	} else if exist {
		return &authorize, nil
	} else {
		log.Debugf(d.Ctx, "AuthorizeDaoImpl the authorize id=[%d] not exist", roleId)
		return nil, fmt.Errorf("the authorize [%d] not exist", roleId)
	}
}

func (d *AuthorizeDaoImpl) ReadAuthorizePage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "AuthorizeDaoImpl ReadAuthorizePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *AuthorizeDaoImpl) ReadByAPI(api string) (*auth.Authorize, error) {
	var authorize auth.Authorize
	exist, err := d.Find(&authorize, "api=?", api)
	if err != nil {
		log.Errorf(d.Ctx, "AuthorizeDaoImpl ReadByAPI fail,as:%s", err.Error())
		return nil, err
	} else if exist {
		return &authorize, nil
	} else {
		return nil, nil
	}
}

func (d *AuthorizeDaoImpl) IsRoleExist(roleId uint64) (bool, error) {
	num, err := d.Count("id=?", roleId)
	if log.IfError(err, "IsAuthorizeExist") {
		return false, err
	}
	return num > 0, nil
}

func (d *AuthorizeDaoImpl) FindAuthorizePage(filter *auth.AuthorizeFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "AuthorizeDaoImpl FindAuthorizePage") {
		return nil, err
	} else {
		return page, err
	}
}
