package common

import (
	"context"
	"fmt"

	"git.platform.io/resource/information"

	"git.platform.io/environment/environment/model"

	"git.platform.io/environment/environment/database"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

// Data Access Object for ip
type ConfigDaoImpl struct {
	database.DaoImpl
}

func NewConfigDao(session *xorm.Session, ctx context.Context) (*ConfigDaoImpl, error) {
	dao := ConfigDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(model.Config)
	if err := dao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	dao.NewSlice = func() interface{} {
		var slice []model.Config
		return &slice
	}

	return &dao, nil
}

func (d *ConfigDaoImpl) CreateConfig(config *model.Config) (int64, error) {
	affected, err := d.Create(config)
	if err != nil {
		log.Errorf(d.Ctx, "ConfigDaoImpl CreateConfig [%v] fail,as:%s", config, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ConfigDaoImpl) CreateConfigByMap(config *map[string]interface{}) (int64, error) {
	affected, err := d.CreateWithMap(config)
	if err != nil {
		log.Errorf(d.Ctx, "ConfigDaoImpl CreateConfig [%v] fail,as:%s", config, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ConfigDaoImpl) DeleteConfig(id string) (int64, error) {
	affected, err := d.DeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "ConfigDaoImpl DeleteConfig [%s] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ConfigDaoImpl) UpdateValue(code, value string) (int64, error) {
	config := model.Config{
		Value: value,
	}
	affected, err := d.UpdateBy(&config, "id=?", code)
	if err != nil {
		log.Errorf(d.Ctx, "ConfigDaoImpl UpdateValue [%v=%s] fail,as:%s", code, value, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ConfigDaoImpl) UpdateConfig(config *model.Config) (int64, error) {
	if config == nil {
		return 0, fmt.Errorf("invalid paramter, config=nil")
	}

	affected, err := d.Update(config.ID, config)
	if err != nil {
		log.Errorf(d.Ctx, "ConfigDaoImpl UpdateConfig [%v] fail,as:%s", config.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ConfigDaoImpl) FindAll() ([]model.Config, error) {
	configs := make([]model.Config, 0)
	err := d.ListAll(&configs)
	if err != nil {
		log.Error(d.Ctx, "ConfigDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return configs, nil
}

func (d *ConfigDaoImpl) ReadConfig(configId string) (*model.Config, error) {
	var config model.Config
	exist, err := d.FindById(configId, &config)
	if err != nil {
		log.Errorf(d.Ctx, "ConfigDaoImpl Read [%s] fail,as:%s", configId, err.Error())
		return nil, err
	} else if exist {
		return &config, nil
	} else {
		log.Debugf(d.Ctx, "ConfigDaoImpl the config id=[%s] not exist", configId)
		return nil, fmt.Errorf("the config [%s] not exist", configId)
	}
}

func (d *ConfigDaoImpl) ReadByCode(code string) (*model.Config, error) {
	var configObj model.Config
	exist, err := d.Find(&configObj, "id=?", code)
	if err != nil {
		log.Errorf(d.Ctx, "ConfigDaoImpl ReadByCode [%s] fail,as:%s", code, err.Error())
		return nil, err
	} else if exist {
		return &configObj, nil
	} else {
		log.Debugf(d.Ctx, "ConfigDaoImpl the config [%s] not exist", code)
		return nil, fmt.Errorf("the config [%s] not exist", code)
	}
}

func (d *ConfigDaoImpl) ReadByFilter(filter model.ConfigFilter) ([]model.Config, error) {
	query, args := filter.ToSql()
	records := make([]model.Config, 0)
	err := d.ListBy(&records, query, args)
	if err != nil {
		log.Errorf(d.Ctx, "ConfigDaoImpl ReadByFilter [%v] fail,as:%s", filter, err.Error())
		return nil, err
	} else {
		return records, nil
	}
}

func (d *ConfigDaoImpl) ReadByParentID(parentID string) ([]model.Config, error) {
	records := make([]model.Config, 0)
	err := d.ListBy(&records, "parent_id=?", parentID)
	if err != nil {
		log.Errorf(d.Ctx, "ConfigDaoImpl ReadByParentID [%s] fail,as:%s", parentID, err.Error())
		return nil, err
	} else {
		return records, nil
	}
}

func (d *ConfigDaoImpl) ReadMapByParentID(parentID string) (map[string]string, error) {
	records, err := d.ReadByParentID(parentID)
	if err != nil {
		log.Errorf(d.Ctx, "ConfigDaoImpl ReadByParentID [%s] fail,as:%s", parentID, err.Error())
		return nil, err
	}
	data := make(map[string]string)
	for _, cfg := range records {
		data[cfg.ID] = cfg.Value
	}
	return data, nil
}

func (d *ConfigDaoImpl) ReadConfigPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "ConfigDaoImpl ReadConfigPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *ConfigDaoImpl) FindPageByFilter(filter *model.ConfigFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "ConfigDaoImpl FindPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}
