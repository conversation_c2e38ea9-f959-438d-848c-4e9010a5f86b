package common

import (
	"context"
	"fmt"
	"time"

	"git.platform.io/resource/information"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/auth"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

// Data Access Object for ip
type AuditLogDaoImpl struct {
	database.DaoImpl
}

func NewAuditLogDao(session *xorm.Session, ctx context.Context) (*AuditLogDaoImpl, error) {
	dao := AuditLogDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(auth.AuditLog)
	if err := dao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	dao.NewSlice = func() interface{} {
		var slice []auth.AuditLog
		return &slice
	}

	return &dao, nil
}

func (d *AuditLogDaoImpl) NewLog(level int, action, message string, args ...string) error {
	user := "system"
	if len(args) > 0 {
		user = args[0]
	}

	module := "User"
	if len(args) > 1 {
		module = args[1]
	}

	newLog := &auth.AuditLog{
		Level:   level,
		Action:  action,
		Message: message,
		Module:  module,
		User:    user,
		AddTime: time.Now().Unix(),
	}
	_, err := d.Create(newLog)
	if err != nil {
		return err
	} else {
		return nil
	}
}

func (d *AuditLogDaoImpl) CreateAuditLog(auditLog *auth.AuditLog) (int64, error) {
	affected, err := d.Create(auditLog)
	if err != nil {
		log.Errorf(d.Ctx, "AuditLogDaoImpl CreateAuditLog [%v] fail,as:%s", auditLog, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AuditLogDaoImpl) CreateAuditLogByMap(auditLog *map[string]interface{}) (int64, error) {
	affected, err := d.CreateWithMap(auditLog)
	if err != nil {
		log.Errorf(d.Ctx, "AuditLogDaoImpl CreateAuditLogByMap [%v] fail,as:%s", auditLog, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AuditLogDaoImpl) DeleteAuditLog(auditLogId int64) (int64, error) {
	affected, err := d.DeleteById(auditLogId)
	if err != nil {
		log.Errorf(d.Ctx, "AuditLogDaoImpl DeleteAuditLog [%v] fail,as:%s", auditLogId, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AuditLogDaoImpl) UpdateAuditLog(auditLog *auth.AuditLog) (int64, error) {
	if auditLog == nil {
		return 0, fmt.Errorf("invalid paramter, auditLog=nil")
	}

	affected, err := d.Update(auditLog.ID, auditLog)
	if err != nil {
		log.Errorf(d.Ctx, "AuditLogDaoImpl UpdateAuditLog [%v] fail,as:%s", auditLog.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *AuditLogDaoImpl) FindAll() (*[]auth.AuditLog, error) {
	var auditLogs []auth.AuditLog
	err := d.ListAll(&auditLogs)
	if err != nil {
		log.Error(d.Ctx, "AuditLogDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return &auditLogs, nil
}

func (d *AuditLogDaoImpl) ReadAuditLog(auditLogId int64) (*auth.AuditLog, error) {
	var auditLog auth.AuditLog
	exist, err := d.FindById(auditLogId, &auditLog)
	if err != nil {
		log.Errorf(d.Ctx, "AuditLogDaoImpl ReadAuditLog [%d] fail,as:%s", auditLogId, err.Error())
		return nil, err
	} else if exist {
		return &auditLog, nil
	} else {
		log.Debugf(d.Ctx, "AuditLogDaoImpl the auditLog id=[%d] not exist", auditLogId)
		return nil, fmt.Errorf("the auditLog [%d] not exist", auditLogId)
	}
}

func (d *AuditLogDaoImpl) ReadByAuditLog(code string) (*auth.AuditLog, error) {
	var auditLogObj auth.AuditLog
	exist, err := d.Find(&auditLogObj, "code=?", code)
	if err != nil {
		log.Errorf(d.Ctx, "AuditLogDaoImpl ReadByAuditLog [%s] fail,as:%s", code, err.Error())
		return nil, err
	} else if exist {
		return &auditLogObj, nil
	} else {
		log.Debugf(d.Ctx, "AuditLogDaoImpl the auditLog [%s] not exist", code)
		return nil, fmt.Errorf("the auditLog [%s] not exist", code)
	}
}

func (d *AuditLogDaoImpl) ReadByUserId(userId int64) (*auth.AuditLog, error) {
	var auditLogObj auth.AuditLog
	exist, err := d.Find(&auditLogObj, "uid=?", userId)
	if err != nil {
		log.Errorf(d.Ctx, "AuditLogDaoImpl ReadByUserId [%d] fail,as:%s", userId, err.Error())
		return nil, err
	} else if exist {
		return &auditLogObj, nil
	} else {
		log.Debugf(d.Ctx, "AuditLogDaoImpl the userid=auditLog [%d] not exist", userId)
		return nil, fmt.Errorf("the auditLog userid=[%d] not exist", userId)
	}
}

func (d *AuditLogDaoImpl) ReadAuditLogPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "AuditLogDaoImpl ReadAuditLogPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}
