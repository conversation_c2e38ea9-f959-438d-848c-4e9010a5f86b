package common

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	. "git.platform.io/environment/environment/dao/test"

	"git.platform.io/environment/environment/model/auth"
)

func TestAuditLogDaoImpl_CreateAuditLog(t *testing.T) {
	auditLog := auth.AuditLog{}
	dao, _ := NewAuditLogDao(nil, context.TODO())
	id, err := dao.CreateAuditLog(&auditLog)
	if err != nil {
		t.<PERSON>("AuditLogDaoImpl CreateAuditLog fail,as[%s]", err.Error())
	} else {
		t.Log("AuditLogDaoImpl CreateAuditLog success, id=", id)
	}
}

func TestAuditLogDaoImpl_CreateAuditLog2(t *testing.T) {
	data := map[string]interface{}{
		"level":    1,
		"user":     "user001",
		"action":   "add",
		"module":   "-",
		"message":  "test",
		"add_time": int64(1234234),
	}
	dao, _ := NewAuditLogDao(nil, context.TODO())
	id, err := dao.CreateAuditLogByMap(&data)
	if err != nil {
		t.Errorf("AuditLogDaoImpl CreateAuditLogByMap fail,as[%s]", err.Error())
	} else {
		t.Log("AuditLogDaoImpl CreateAuditLogByMap success, id=", id)
	}
}

func TestAuditLogDaoImpl_DeleteAuditLog(t *testing.T) {
	dao, _ := NewAuditLogDao(nil, context.TODO())
	affected, err := dao.DeleteAuditLog(3)
	if err != nil {
		t.Errorf("AuditLogDaoImpl DeleteAuditLog fail,as[%s]", err.Error())
	} else {
		t.Log("AuditLogDaoImpl DeleteAuditLog success, affected=", affected)
	}
}

func TestAuditLogDaoImpl_UpdateAuditLog(t *testing.T) {
	auditLog := auth.AuditLog{
		ID:      1,
		Message: "test",
	}
	dao, _ := NewAuditLogDao(nil, context.TODO())
	affected, err := dao.UpdateAuditLog(&auditLog)
	if err != nil {
		t.Errorf("AuditLogDaoImpl UpdateAuditLog fail,as[%s]", err.Error())
	} else {
		t.Log("AuditLogDaoImpl UpdateAuditLog success, affected=", affected)
	}
}

func TestAuditLogDaoImpl_ReadAuditLogPage(t *testing.T) {
	dao, _ := NewAuditLogDao(nil, context.TODO())
	auditLogs, err := dao.ReadAuditLogPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("AuditLogDaoImpl ReadAuditLogPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(auditLogs)
		t.Log("AuditLogDaoImpl ReadAuditLogPage success, data=", string(bytes))
	}
}

func TestAuditLogDaoImpl_FindAll(t *testing.T) {
	dao, _ := NewAuditLogDao(nil, context.TODO())
	auditLogs, err := dao.FindAll()
	if err != nil {
		t.Errorf("AuditLogDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(auditLogs)
		t.Log("AuditLogDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestAuditLogDaoImpl_ReadAuditLog(t *testing.T) {
	dao, _ := NewAuditLogDao(nil, context.TODO())
	auditLog, err := dao.ReadAuditLog(1)
	if err != nil {
		t.Errorf("AuditLogDaoImpl ReadAuditLog fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(auditLog)
		t.Log("AuditLogDaoImpl ReadAuditLog success, data=", string(bytes))
	}
}

func TestMain(m *testing.M) {
	Setup()
	code := m.Run()
	Teardown()
	os.Exit(code)
}
