package common

import (
	"context"
	"fmt"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model"
	"git.platform.io/resource/common/log"
	"github.com/go-xorm/xorm"
)

type IdentificationDaoImpl struct {
	database.DaoImpl
}

func NewIdentificationDao(session *xorm.Session, ctx context.Context) *IdentificationDaoImpl {
	dao := IdentificationDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(model.Identification)
	dao.Init(m.TableName(), m, session)
	dao.NewSlice = func() interface{} {
		var slice []model.Identification
		return &slice
	}

	return &dao
}

func (d *IdentificationDaoImpl) CreateIdentification(record *model.Identification) (int64, error) {
	affected, err := d.Create(record)
	if err != nil {
		log.Errorf(d.Ctx, "IdentificationDaoImpl CreateIdentification [%v] fail,as:%s", record, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *IdentificationDaoImpl) UpdateIdentification(record *model.Identification) (int64, error) {
	if record == nil {
		return 0, fmt.Errorf("invalid paramter, record=nil")
	}

	affected, err := d.Update(record.ID, record)
	if err != nil {
		log.Errorf(d.Ctx, "IdentificationDaoImpl UpdateIdentification [%v] fail,as:%s", record.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *IdentificationDaoImpl) ReadIdentification(id string) (*model.Identification, error) {
	var record model.Identification
	exist, err := d.FindById(id, &record)
	if err != nil {
		log.Errorf(d.Ctx, "IdentificationDaoImpl Read [%s] fail,as:%s", id, err.Error())
		return nil, err
	} else if exist {
		return &record, nil
	} else {
		log.Debugf(d.Ctx, "IdentificationDaoImpl the record id=[%d] not exist", id)
		return nil, fmt.Errorf("the record [%s] not exist", id)
	}
}

func (d *IdentificationDaoImpl) ReadByFilter(filter *model.IdentificationFilter) ([]model.Identification, error) {
	query, args := filter.ToSql()
	records := make([]model.Identification, 0)
	err := d.ListBy(&records, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "IdentificationDaoImpl ReadByFilter [%v] fail,as:%s", filter, err.Error())
		return nil, err
	} else {
		return records, nil
	}
}

func (d *IdentificationDaoImpl) CheckExist(id string) (bool, error) {
	return d.Exist("id=?", id)
}
