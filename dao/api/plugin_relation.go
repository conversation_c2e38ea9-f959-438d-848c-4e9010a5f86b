package api

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/utils"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/api"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for gateway.GatewayPlugin
type GatewayPluginRelationDaoImpl struct {
	db.DaoImpl
}

// new a data access object for gateway plugin
func NewGatewayPluginRelationDao(session *xorm.Session, ctx context.Context) (*GatewayPluginRelationDaoImpl, error) {
	gatewayPluginRelationDao := GatewayPluginRelationDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(api.GatewayPluginRelation)
	if err := gatewayPluginRelationDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	gatewayPluginRelationDao.NewSlice = func() interface{} {
		slice := make([]api.GatewayPluginRelation, 0)
		return &slice
	}

	return &gatewayPluginRelationDao, nil
}

func (d *GatewayPluginRelationDaoImpl) CreateGatewayPluginRelation(relation *api.GatewayPluginRelation) (int64, error) {
	affected, err := d.Create(relation)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginRelationDaoImpl CreateGateway [%v] fail,as:%s", relation, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayPluginRelationDaoImpl) DeleteGatewayPluginRelation(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginRelationDaoImpl DeleteGatewayRelation [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayPluginRelationDaoImpl) UpdateGatewayPluginRelation(plugin *api.GatewayPluginRelation, plugins ...string) (int64, error) {
	if plugin == nil {
		return 0, fmt.Errorf("invalid paramter, gateway=nil")
	}

	affected, err := d.Update(plugin.ID, plugin, plugins...)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginRelationDaoImpl UpdateGatewayPluginRelation [%v] fail,as:%s", plugin.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayPluginRelationDaoImpl) FindAll() ([]api.GatewayPluginRelation, error) {
	var plugins []api.GatewayPluginRelation
	err := d.SortListBy(&plugins, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "GatewayPluginRelationDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return plugins, nil
}

func (d *GatewayPluginRelationDaoImpl) ReadGatewayPluginRelation(relationId uint64) (*api.GatewayPluginRelation, error) {
	var plugin api.GatewayPluginRelation
	exist, err := d.FindById(relationId, &plugin)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginRelationDaoImpl Read [%d] fail,as:%s", relationId, err.Error())
		return nil, err
	} else if exist {
		return &plugin, nil
	} else {
		log.Debugf(d.Ctx, "GatewayPluginRelationDaoImpl the relationId id=[%d] not exist", relationId)
		return nil, fmt.Errorf("the relationId [%d] not exist", relationId)
	}
}

func (d *GatewayPluginRelationDaoImpl) ReadGatewayPluginRelationPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginRelationDaoImpl ReadGatewayPluginRelationPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *GatewayPluginRelationDaoImpl) ReadLastByFilter(filter *api.GatewayPluginRelationFilter) (*api.GatewayPluginRelation, error) {
	records := make([]api.GatewayPluginRelation, 0)
	query, args := filter.ToSql()
	sort := "id desc"

	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "GatewayDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *GatewayPluginRelationDaoImpl) FindGatewayPluginRelationPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "GatewayPluginRelationDaoImpl FindGatewayPluginRelationPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *GatewayPluginRelationDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]api.GatewayPluginRelationInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "GatewayPluginRelationDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *GatewayPluginRelationDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]api.GatewayPluginRelationInfo, error) {
	records := make([]api.GatewayPluginRelationInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "GatewayPluginRelationDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *GatewayPluginRelationDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*api.GatewayPluginRelationInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(api.GatewayPluginRelationInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginRelationDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "GatewayPluginRelationDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *GatewayPluginRelationDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindGatewayPluginRelationPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *GatewayPluginRelationDaoImpl) ReadByFilter(filter information.IFilter) ([]api.GatewayPluginRelation, error) {
	records := make([]api.GatewayPluginRelation, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "GatewayPluginRelationDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *GatewayPluginRelationDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *GatewayPluginRelationDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *GatewayPluginRelationDaoImpl) Entity() information.IRecord {
	return new(api.GatewayPluginRelation)
}

func (d *GatewayPluginRelationDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(api.GatewayPluginRelationFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
