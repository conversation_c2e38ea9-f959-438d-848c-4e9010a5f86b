package api

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/api"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for api.Function
type FunctionDaoImpl struct {
	database.DaoImpl
}

// new a data access object for function
func NewFunctionDao(session *xorm.Session, ctx context.Context) (*FunctionDaoImpl, error) {
	functionDao := FunctionDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(api.Function)
	if err := functionDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	functionDao.NewSlice = func() interface{} {
		slice := make([]api.Function, 0)
		return &slice
	}

	return &functionDao, nil
}

func (d *FunctionDaoImpl) CreateFunction(function *api.Function) (int64, error) {
	affected, err := d.Create(function)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionDaoImpl CreateFunction [%v] fail,as:%s", function, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *FunctionDaoImpl) DeleteFunction(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionDaoImpl DeleteFunction [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *FunctionDaoImpl) UpdateFunction(function *api.Function, fields ...string) (int64, error) {
	if function == nil {
		return 0, fmt.Errorf("invalid paramter, function=nil")
	}

	affected, err := d.Update(function.ID, function, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionDaoImpl UpdateFunction [%v] fail,as:%s", function.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *FunctionDaoImpl) FindAll() ([]api.Function, error) {
	var functions []api.Function
	err := d.SortListBy(&functions, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "FunctionDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return functions, nil
}

func (d *FunctionDaoImpl) ReadFunction(functionId uint64) (*api.Function, error) {
	var function api.Function
	exist, err := d.FindById(functionId, &function)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionDaoImpl Read [%d] fail,as:%s", functionId, err.Error())
		return nil, err
	} else if exist {
		return &function, nil
	} else {
		log.Debugf(d.Ctx, "FunctionDaoImpl the function id=[%d] not exist", functionId)
		return nil, fmt.Errorf("the function [%d] not exist", functionId)
	}
}

func (d *FunctionDaoImpl) ReadFunctionPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionDaoImpl ReadFunctionPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *FunctionDaoImpl) ReadFunctionInfo(functionId uint64) (*api.FunctionInfo, error) {
	var function api.FunctionInfo
	filter := api.FunctionFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(functionId, &function, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionDaoImpl ReadFunctionInfo [%d] fail,as:%s", functionId, err.Error())
		return nil, err
	} else if exist {
		return &function, nil
	} else {
		log.Debugf(d.Ctx, "FunctionDaoImpl the function id=[%d] not exist", functionId)
		return nil, fmt.Errorf("the function [%d] not exist", functionId)
	}
}

func (d *FunctionDaoImpl) FindFunctionPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "FunctionDaoImpl FindFunctionPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *FunctionDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]api.FunctionInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "FunctionDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *FunctionDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]api.FunctionInfo, error) {
	records := make([]api.FunctionInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "FunctionDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *FunctionDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*api.FunctionInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(api.FunctionInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "FunctionDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *FunctionDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindFunctionPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *FunctionDaoImpl) ReadByFilter(filter information.IFilter) ([]api.Function, error) {
	records := make([]api.Function, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "FunctionDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *FunctionDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *FunctionDaoImpl) Entity() information.IRecord {
	return new(api.Function)
}

func (d *FunctionDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(api.FunctionFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}

func (d *FunctionDaoImpl) ReadLastByFilter(filter *api.FunctionFilter) (*api.Function, error) {
	records := make([]api.Function, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "FunctionDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *FunctionDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}
