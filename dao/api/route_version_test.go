package api

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/api"
)

func TestRouteVersionDaoImpl_CreateRouteVersion(t *testing.T) {
	routeVersion := api.RouteVersion{}
	routeVersionDao, _ := NewRouteVersionDao(nil, context.TODO())
	id, err := routeVersionDao.CreateRouteVersion(&routeVersion)
	if err != nil {
		t.<PERSON>rf("RouteVersionDaoImpl CreateRouteVersion fail,as[%s]", err.Error())
	} else {
		t.Log("RouteVersionDaoImpl CreateRouteVersion success, id=", id)
	}
}

func TestRouteVersionDaoImpl_UpdateRouteVersion(t *testing.T) {
	routeVersion := api.RouteVersion{}
	routeVersionDao, _ := NewRouteVersionDao(nil, context.TODO())
	affected, err := routeVersionDao.UpdateRouteVersion(&routeVersion)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("RouteVersionDaoImpl UpdateRouteVersion fail,as[%s]", err.Error())
	} else {
		t.Log("RouteVersionDaoImpl UpdateRouteVersion success, affected=", affected)
	}
}

func TestRouteVersionDaoImpl_ReadRouteVersionPage(t *testing.T) {
	routeVersionDao, _ := NewRouteVersionDao(nil, context.TODO())
	routeVersions, err := routeVersionDao.ReadRouteVersionPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("RouteVersionDaoImpl ReadRouteVersionPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(routeVersions)
		t.Log("RouteVersionDaoImpl ReadRouteVersionPage success, data=", string(bytes))
	}
}

func TestRouteVersionDaoImpl_FindAll(t *testing.T) {
	routeVersionDao, _ := NewRouteVersionDao(nil, context.TODO())
	routeVersions, err := routeVersionDao.FindAll()
	if err != nil {
		t.Errorf("RouteVersionDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(routeVersions)
		t.Log("RouteVersionDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestRouteVersionDaoImpl_ReadRouteVersion(t *testing.T) {
	routeVersionDao, _ := NewRouteVersionDao(nil, context.TODO())
	routeVersion, err := routeVersionDao.ReadRouteVersion(1)
	if err != nil {
		t.Errorf("RouteVersionDaoImpl ReadRouteVersion fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(routeVersion)
		t.Log("RouteVersionDaoImpl ReadRouteVersion success, data=", string(bytes))
	}
}

func TestRouteVersionDaoImpl_DeleteRouteVersion(t *testing.T) {
	routeVersionDao, _ := NewRouteVersionDao(nil, context.TODO())
	affected, err := routeVersionDao.DeleteRouteVersion(1)
	if err != nil {
		t.Errorf("RouteVersionDaoImpl DeleteRouteVersion fail,as[%s]", err.Error())
	} else {
		t.Log("RouteVersionDaoImpl DeleteRouteVersion success, affected=", affected)
	}
}
