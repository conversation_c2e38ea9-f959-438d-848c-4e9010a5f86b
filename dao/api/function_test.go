package api

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/api"
)

func aTestFunctionDaoImpl_CreateFunction(t *testing.T) {
	function := api.Function{}
	functionDao, _ := NewFunctionDao(nil, context.TODO())
	id, err := functionDao.CreateFunction(&function)
	if err != nil {
		t.<PERSON>("FunctionDaoImpl CreateFunction fail,as[%s]", err.<PERSON>())
	} else {
		t.Log("FunctionDaoImpl CreateFunction success, id=", id)
	}
}

func aTestFunctionDaoImpl_UpdateFunction(t *testing.T) {
	function := api.Function{}
	functionDao, _ := NewFunctionDao(nil, context.TODO())
	affected, err := functionDao.UpdateFunction(&function)
	if err != nil {
		t.<PERSON>rf("FunctionDaoImpl UpdateFunction fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("FunctionDaoImpl UpdateFunction success, affected=", affected)
	}
}

func aTestFunctionDaoImpl_ReadFunctionPage(t *testing.T) {
	functionDao, _ := NewFunctionDao(nil, context.TODO())
	functions, err := functionDao.ReadFunctionPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("FunctionDaoImpl ReadFunctionPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(functions)
		t.Log("FunctionDaoImpl ReadFunctionPage success, data=", string(bytes))
	}
}

func aTestFunctionDaoImpl_FindAll(t *testing.T) {
	functionDao, _ := NewFunctionDao(nil, context.TODO())
	functions, err := functionDao.FindAll()
	if err != nil {
		t.Errorf("FunctionDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(functions)
		t.Log("FunctionDaoImpl FindAll success, data=", string(bytes))
	}
}

func aTestFunctionDaoImpl_ReadFunction(t *testing.T) {
	functionDao, _ := NewFunctionDao(nil, context.TODO())
	function, err := functionDao.ReadFunction(1)
	if err != nil {
		t.Errorf("FunctionDaoImpl ReadFunction fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(function)
		t.Log("FunctionDaoImpl ReadFunction success, data=", string(bytes))
	}
}

func aTestFunctionDaoImpl_DeleteFunction(t *testing.T) {
	functionDao, _ := NewFunctionDao(nil, context.TODO())
	affected, err := functionDao.DeleteFunction(1)
	if err != nil {
		t.Errorf("FunctionDaoImpl DeleteFunction fail,as[%s]", err.Error())
	} else {
		t.Log("FunctionDaoImpl DeleteFunction success, affected=", affected)
	}
}
