package api

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/api"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for api.WhiteTopic
type WhiteTopicDaoImpl struct {
	database.DaoImpl
}

// new a data access object for whiteTopic
func NewWhiteTopicDao(session *xorm.Session, ctx context.Context) (*WhiteTopicDaoImpl, error) {
	whiteTopicDao := WhiteTopicDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(api.WhiteTopic)
	if err := whiteTopicDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	whiteTopicDao.NewSlice = func() interface{} {
		slice := make([]api.WhiteTopic, 0)
		return &slice
	}

	return &whiteTopicDao, nil
}

func (d *WhiteTopicDaoImpl) CreateWhiteTopic(whiteTopic *api.WhiteTopic) (int64, error) {
	affected, err := d.Create(whiteTopic)
	if err != nil {
		log.Errorf(d.Ctx, "WhiteTopicDaoImpl CreateWhiteTopic [%v] fail,as:%s", whiteTopic, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *WhiteTopicDaoImpl) DeleteWhiteTopic(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "WhiteTopicDaoImpl DeleteWhiteTopic [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *WhiteTopicDaoImpl) UpdateWhiteTopic(whiteTopic *api.WhiteTopic, fields ...string) (int64, error) {
	if whiteTopic == nil {
		return 0, fmt.Errorf("invalid paramter, whiteTopic=nil")
	}

	affected, err := d.Update(whiteTopic.ID, whiteTopic, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "WhiteTopicDaoImpl UpdateWhiteTopic [%v] fail,as:%s", whiteTopic.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *WhiteTopicDaoImpl) FindAll() ([]api.WhiteTopic, error) {
	var whiteTopics []api.WhiteTopic
	err := d.SortListBy(&whiteTopics, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "WhiteTopicDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return whiteTopics, nil
}

func (d *WhiteTopicDaoImpl) ReadWhiteTopic(whiteTopicId uint64) (*api.WhiteTopic, error) {
	var whiteTopic api.WhiteTopic
	exist, err := d.FindById(whiteTopicId, &whiteTopic)
	if err != nil {
		log.Errorf(d.Ctx, "WhiteTopicDaoImpl Read [%d] fail,as:%s", whiteTopicId, err.Error())
		return nil, err
	} else if exist {
		return &whiteTopic, nil
	} else {
		log.Debugf(d.Ctx, "WhiteTopicDaoImpl the whiteTopic id=[%d] not exist", whiteTopicId)
		return nil, fmt.Errorf("the whiteTopic [%d] not exist", whiteTopicId)
	}
}

func (d *WhiteTopicDaoImpl) ReadWhiteTopicPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "WhiteTopicDaoImpl ReadWhiteTopicPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *WhiteTopicDaoImpl) ReadWhiteTopicInfo(whiteTopicId uint64) (*api.WhiteTopicInfo, error) {
	var whiteTopic api.WhiteTopicInfo
	filter := api.WhiteTopicFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(whiteTopicId, &whiteTopic, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "WhiteTopicDaoImpl ReadWhiteTopicInfo [%d] fail,as:%s", whiteTopicId, err.Error())
		return nil, err
	} else if exist {
		return &whiteTopic, nil
	} else {
		log.Debugf(d.Ctx, "WhiteTopicDaoImpl the whiteTopic id=[%d] not exist", whiteTopicId)
		return nil, fmt.Errorf("the whiteTopic [%d] not exist", whiteTopicId)
	}
}

func (d *WhiteTopicDaoImpl) FindWhiteTopicPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "WhiteTopicDaoImpl FindWhiteTopicPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *WhiteTopicDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]api.WhiteTopicInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "WhiteTopicDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *WhiteTopicDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]api.WhiteTopicInfo, error) {
	records := make([]api.WhiteTopicInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "WhiteTopicDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *WhiteTopicDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*api.WhiteTopicInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(api.WhiteTopicInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "WhiteTopicDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "WhiteTopicDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *WhiteTopicDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindWhiteTopicPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *WhiteTopicDaoImpl) ReadByFilter(filter information.IFilter) ([]api.WhiteTopic, error) {
	records := make([]api.WhiteTopic, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "WhiteTopicDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *WhiteTopicDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *WhiteTopicDaoImpl) Entity() information.IRecord {
	return new(api.WhiteTopic)
}

func (d *WhiteTopicDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(api.WhiteTopicFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}

func (d *WhiteTopicDaoImpl) ReadLastByFilter(filter *api.WhiteTopicFilter) (*api.WhiteTopic, error) {
	records := make([]api.WhiteTopic, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "WhiteTopicDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}
