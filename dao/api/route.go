package api

import (
	"context"
	"fmt"
	"strings"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/api"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for gateway.Route
type RouteDaoImpl struct {
	db.DaoImpl
}

// new a data access object for route
func NewRouteDao(session *xorm.Session, ctx context.Context) (*RouteDaoImpl, error) {
	routeDao := RouteDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(api.Route)
	if err := routeDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	routeDao.NewSlice = func() interface{} {
		slice := make([]api.Route, 0)
		return &slice
	}

	return &routeDao, nil
}

func (d *RouteDaoImpl) CreateRoute(route *api.Route) (int64, error) {
	affected, err := d.Create(route)
	if err != nil {
		log.Errorf(d.Ctx, "RouteDaoImpl CreateRoute [%v] fail,as:%s", route, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RouteDaoImpl) DeleteRoute(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "RouteDaoImpl DeleteRoute [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RouteDaoImpl) UpdateRoute(route *api.Route, fields ...string) (int64, error) {
	if route == nil {
		return 0, fmt.Errorf("invalid paramter, route=nil")
	}

	affected, err := d.Update(route.ID, route, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "RouteDaoImpl UpdateRoute [%v] fail,as:%s", route.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RouteDaoImpl) FindAll() ([]api.Route, error) {
	var routes []api.Route
	err := d.SortListBy(&routes, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "RouteDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return routes, nil
}

func (d *RouteDaoImpl) ReadRoute(routeId uint64) (*api.Route, error) {
	var route api.Route
	exist, err := d.FindById(routeId, &route)
	if err != nil {
		log.Errorf(d.Ctx, "RouteDaoImpl Read [%d] fail,as:%s", routeId, err.Error())
		return nil, err
	} else if exist {
		return &route, nil
	} else {
		log.Debugf(d.Ctx, "RouteDaoImpl the route id=[%d] not exist", routeId)
		return nil, fmt.Errorf("the route [%d] not exist", routeId)
	}
}

func (d *RouteDaoImpl) ReadRoutePage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "RouteDaoImpl ReadRoutePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *RouteDaoImpl) ReadLastByFilter(filter *api.RouteFilter) (*api.Route, error) {
	records := make([]api.Route, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "RouteDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *RouteDaoImpl) ReadRouteInfo(routeId uint64) (*api.RouteInfo, error) {
	var route api.RouteInfo
	filter := api.RouteFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(routeId, &route, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "RouteDaoImpl ReadRouteInfo [%d] fail,as:%s", routeId, err.Error())
		return nil, err
	} else if exist {
		return &route, nil
	} else {
		log.Debugf(d.Ctx, "RouteDaoImpl the route id=[%d] not exist", routeId)
		return nil, fmt.Errorf("the route [%d] not exist", routeId)
	}
}

func (d *RouteDaoImpl) ReadLastInfoByFilter(filter *api.RouteFilter) (*api.RouteInfo, error) {
	filter.Sort = 1 // 逆序排列，返回最后一条
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]api.RouteInfo, 0)
	err := d.JoinFirst(&records, conditions, filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "RouteDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *RouteDaoImpl) FindRoutePage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "RouteDaoImpl FindRoutePage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *RouteDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]api.RouteInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "RouteDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *RouteDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]api.RouteInfo, error) {
	records := make([]api.RouteInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "RouteDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *RouteDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*api.RouteInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(api.RouteInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "RouteDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "RouteDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *RouteDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindRoutePage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *RouteDaoImpl) ReadByFilter(filter information.IFilter) ([]api.Route, error) {
	records := make([]api.Route, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "RouteDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *RouteDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *RouteDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *RouteDaoImpl) Entity() information.IRecord {
	return new(api.Route)
}

func (d *RouteDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(api.RouteFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}

func (d *RouteDaoImpl) ReadPathListByFilter(filter information.IFilter) ([]api.Route, error) {
	records := make([]api.Route, 0)
	query, args := filter.ToSql()

	fields := []string{"id", "event_code", "path"}
	err := d.ListCol(&records, fields, query, args...)
	if log.IfError(err, "ServiceInstanceDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}
