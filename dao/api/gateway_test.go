package api

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/api"
)

func TestGatewayDaoImpl_CreateGateway(t *testing.T) {
	gateway := api.Gateway{}
	gatewayDao, _ := NewGatewayDao(nil, context.TODO())
	id, err := gatewayDao.CreateGateway(&gateway)
	if err != nil {
		t.<PERSON>("GatewayDaoImpl CreateGateway fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("GatewayDaoImpl CreateGateway success, id=", id)
	}
}

func TestGatewayDaoImpl_UpdateGateway(t *testing.T) {
	gateway := api.Gateway{}
	gatewayDao, _ := NewGatewayDao(nil, context.TODO())
	affected, err := gatewayDao.UpdateGateway(&gateway)
	if err != nil {
		t.Errorf("GatewayDaoImpl UpdateGateway fail,as[%s]", err.<PERSON>r())
	} else {
		t.Log("GatewayDaoImpl UpdateGateway success, affected=", affected)
	}
}

func TestGatewayDaoImpl_ReadGatewayPage(t *testing.T) {
	gatewayDao, _ := NewGatewayDao(nil, context.TODO())
	gateways, err := gatewayDao.ReadGatewayPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("GatewayDaoImpl ReadGatewayPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(gateways)
		t.Log("GatewayDaoImpl ReadGatewayPage success, data=", string(bytes))
	}
}

func TestGatewayDaoImpl_FindAll(t *testing.T) {
	gatewayDao, _ := NewGatewayDao(nil, context.TODO())
	gateways, err := gatewayDao.FindAll()
	if err != nil {
		t.Errorf("GatewayDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(gateways)
		t.Log("GatewayDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestGatewayDaoImpl_ReadGateway(t *testing.T) {
	gatewayDao, _ := NewGatewayDao(nil, context.TODO())
	gateway, err := gatewayDao.ReadGateway(1)
	if err != nil {
		t.Errorf("GatewayDaoImpl ReadGateway fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(gateway)
		t.Log("GatewayDaoImpl ReadGateway success, data=", string(bytes))
	}
}

func TestGatewayDaoImpl_DeleteGateway(t *testing.T) {
	gatewayDao, _ := NewGatewayDao(nil, context.TODO())
	affected, err := gatewayDao.DeleteGateway(1)
	if err != nil {
		t.Errorf("GatewayDaoImpl DeleteGateway fail,as[%s]", err.Error())
	} else {
		t.Log("GatewayDaoImpl DeleteGateway success, affected=", affected)
	}
}
