package api

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/utils"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/api"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for gateway.GatewayPlugin
type GatewayPluginDaoImpl struct {
	db.DaoImpl
}

// new a data access object for gateway plugin
func NewGatewayPluginDao(session *xorm.Session, ctx context.Context) (*GatewayPluginDaoImpl, error) {
	gatewayPluginDao := GatewayPluginDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(api.GatewayPlugin)
	if err := gatewayPluginDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	gatewayPluginDao.NewSlice = func() interface{} {
		slice := make([]api.GatewayPlugin, 0)
		return &slice
	}

	return &gatewayPluginDao, nil
}

func (d *GatewayPluginDaoImpl) CreateGatewayPlugin(gatewayPlugin *api.GatewayPlugin) (int64, error) {
	affected, err := d.Create(gatewayPlugin)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginDaoImpl CreateGateway [%v] fail,as:%s", gatewayPlugin, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayPluginDaoImpl) DeleteGatewayPlugin(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginDaoImpl DeleteGateway [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayPluginDaoImpl) UpdateGatewayPlugin(plugin *api.GatewayPlugin, plugins ...string) (int64, error) {
	if plugin == nil {
		return 0, fmt.Errorf("invalid paramter, gateway=nil")
	}

	affected, err := d.Update(plugin.ID, plugin, plugins...)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginDaoImpl UpdateGatewayPlugin [%v] fail,as:%s", plugin.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayPluginDaoImpl) FindAll() ([]api.GatewayPlugin, error) {
	var plugins []api.GatewayPlugin
	err := d.SortListBy(&plugins, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "GatewayPluginDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return plugins, nil
}

func (d *GatewayPluginDaoImpl) ReadGatewayPlugin(pluginId uint64) (*api.GatewayPlugin, error) {
	var plugin api.GatewayPlugin
	exist, err := d.FindById(pluginId, &plugin)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginDaoImpl Read [%d] fail,as:%s", pluginId, err.Error())
		return nil, err
	} else if exist {
		return &plugin, nil
	} else {
		log.Debugf(d.Ctx, "GatewayPluginDaoImpl the pluginId id=[%d] not exist", pluginId)
		return nil, fmt.Errorf("the pluginId [%d] not exist", pluginId)
	}
}

func (d *GatewayPluginDaoImpl) ReadGatewayPluginPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginDaoImpl ReadGatewayPluginPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *GatewayPluginDaoImpl) ReadLastByFilter(filter *api.GatewayPluginFilter) (*api.GatewayPlugin, error) {
	records := make([]api.GatewayPlugin, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "GatewayDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *GatewayPluginDaoImpl) FindGatewayPluginPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "GatewayPluginDaoImpl FindGatewayPluginPage") {
		return nil, err
	} else {
		return page, err
	}
}

//func (d *GatewayPluginDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
//	conditions := filter.GetJoinCondition()
//	query, args := filter.ToAliasSql()
//
//	records := make([]api.GatewayPluginInfo, 0)
//	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
//	if log.IfError(err, "GatewayPluginDaoImpl ReadInfoPageByFilter") {
//		return nil, err
//	} else {
//		return page, err
//	}
//}
//
//func (d *GatewayPluginDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]api.GatewayPluginInfo, error) {
//	records := make([]api.GatewayPluginInfo, 0)
//	err := d.JoinQueryByFilter(&records, filter)
//	if log.IfError(err, "GatewayPluginDaoImpl ReadInfoListByFilter") {
//		return nil, err
//	} else {
//		return records, err
//	}
//}
//
//func (d *GatewayPluginDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*api.GatewayPluginInfo, error) {
//	conditions := filter.GetJoinCondition()
//	query, args := filter.ToAliasSql()
//
//	record := new(api.GatewayPluginInfo)
//	exist, err := d.JoinFind(record, conditions, query, args...)
//	if err != nil {
//		log.Errorf("GatewayPluginDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
//		return nil, err
//	} else if exist {
//		return record, nil
//	} else {
//		log.Debugf("GatewayPluginDaoImpl the record [%++v] not exist", filter)
//		return nil, fmt.Errorf("the record [%++v] not exist", filter)
//	}
//}

func (d *GatewayPluginDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	return d.FindGatewayPluginPage(f)
}

func (d *GatewayPluginDaoImpl) ReadByFilter(filter information.IFilter) ([]api.GatewayPlugin, error) {
	records := make([]api.GatewayPlugin, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "GatewayPluginDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *GatewayPluginDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *GatewayPluginDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *GatewayPluginDaoImpl) Entity() information.IRecord {
	return new(api.GatewayPlugin)
}

func (d *GatewayPluginDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(api.GatewayPluginFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
