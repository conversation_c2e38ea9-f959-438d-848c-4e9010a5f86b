package api

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/api"
)

func aTestWhiteTopicDaoImpl_CreateWhiteTopic(t *testing.T) {
	whiteTopic := api.WhiteTopic{}
	whiteTopicDao, _ := NewWhiteTopicDao(nil, context.TODO())
	id, err := whiteTopicDao.CreateWhiteTopic(&whiteTopic)
	if err != nil {
		t.<PERSON>("WhiteTopicDaoImpl CreateWhiteTopic fail,as[%s]", err.Error())
	} else {
		t.Log("WhiteTopicDaoImpl CreateWhiteTopic success, id=", id)
	}
}

func aTestWhiteTopicDaoImpl_UpdateWhiteTopic(t *testing.T) {
	whiteTopic := api.WhiteTopic{}
	whiteTopicDao, _ := NewWhiteTopicDao(nil, context.TODO())
	affected, err := whiteTopicDao.UpdateWhiteTopic(&whiteTopic)
	if err != nil {
		t.<PERSON>("WhiteTopicDaoImpl UpdateWhiteTopic fail,as[%s]", err.<PERSON><PERSON>r())
	} else {
		t.Log("WhiteTopicDaoImpl UpdateWhiteTopic success, affected=", affected)
	}
}

func aTestWhiteTopicDaoImpl_ReadWhiteTopicPage(t *testing.T) {
	whiteTopicDao, _ := NewWhiteTopicDao(nil, context.TODO())
	whiteTopics, err := whiteTopicDao.ReadWhiteTopicPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("WhiteTopicDaoImpl ReadWhiteTopicPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(whiteTopics)
		t.Log("WhiteTopicDaoImpl ReadWhiteTopicPage success, data=", string(bytes))
	}
}

func aTestWhiteTopicDaoImpl_FindAll(t *testing.T) {
	whiteTopicDao, _ := NewWhiteTopicDao(nil, context.TODO())
	whiteTopics, err := whiteTopicDao.FindAll()
	if err != nil {
		t.Errorf("WhiteTopicDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(whiteTopics)
		t.Log("WhiteTopicDaoImpl FindAll success, data=", string(bytes))
	}
}

func aTestWhiteTopicDaoImpl_ReadWhiteTopic(t *testing.T) {
	whiteTopicDao, _ := NewWhiteTopicDao(nil, context.TODO())
	whiteTopic, err := whiteTopicDao.ReadWhiteTopic(1)
	if err != nil {
		t.Errorf("WhiteTopicDaoImpl ReadWhiteTopic fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(whiteTopic)
		t.Log("WhiteTopicDaoImpl ReadWhiteTopic success, data=", string(bytes))
	}
}

func aTestWhiteTopicDaoImpl_DeleteWhiteTopic(t *testing.T) {
	whiteTopicDao, _ := NewWhiteTopicDao(nil, context.TODO())
	affected, err := whiteTopicDao.DeleteWhiteTopic(1)
	if err != nil {
		t.Errorf("WhiteTopicDaoImpl DeleteWhiteTopic fail,as[%s]", err.Error())
	} else {
		t.Log("WhiteTopicDaoImpl DeleteWhiteTopic success, affected=", affected)
	}
}
