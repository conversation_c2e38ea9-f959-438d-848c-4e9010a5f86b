package api

import (
	"context"
	"fmt"
	"strings"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/api"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for gateway.RouteVersion
type RouteVersionDaoImpl struct {
	db.DaoImpl
}

// new a data access object for routeVersion
func NewRouteVersionDao(session *xorm.Session, ctx context.Context) (*RouteVersionDaoImpl, error) {
	routeVersionDao := RouteVersionDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(api.RouteVersion)
	if err := routeVersionDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	routeVersionDao.NewSlice = func() interface{} {
		slice := make([]api.RouteVersion, 0)
		return &slice
	}

	return &routeVersionDao, nil
}

func (d *RouteVersionDaoImpl) CreateRouteVersion(routeVersion *api.RouteVersion) (int64, error) {
	affected, err := d.Create(routeVersion)
	if err != nil {
		log.Errorf(d.Ctx, "RouteVersionDaoImpl CreateRouteVersion [%v] fail,as:%s", routeVersion, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RouteVersionDaoImpl) DeleteRouteVersion(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "RouteVersionDaoImpl DeleteRouteVersion [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RouteVersionDaoImpl) UpdateRouteVersion(routeVersion *api.RouteVersion, routeVersions ...string) (int64, error) {
	if routeVersion == nil {
		return 0, fmt.Errorf("invalid paramter, routeVersion=nil")
	}

	affected, err := d.Update(routeVersion.ID, routeVersion, routeVersions...)
	if err != nil {
		log.Errorf(d.Ctx, "RouteVersionDaoImpl UpdateRouteVersion [%v] fail,as:%s", routeVersion.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *RouteVersionDaoImpl) FindAll() ([]api.RouteVersion, error) {
	var routeVersions []api.RouteVersion
	err := d.SortListBy(&routeVersions, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "RouteVersionDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return routeVersions, nil
}

func (d *RouteVersionDaoImpl) ReadRouteVersion(routeVersionId uint64) (*api.RouteVersion, error) {
	var routeVersion api.RouteVersion
	exist, err := d.FindById(routeVersionId, &routeVersion)
	if err != nil {
		log.Errorf(d.Ctx, "RouteVersionDaoImpl Read [%d] fail,as:%s", routeVersionId, err.Error())
		return nil, err
	} else if exist {
		return &routeVersion, nil
	} else {
		log.Debugf(d.Ctx, "RouteVersionDaoImpl the routeVersion id=[%d] not exist", routeVersionId)
		return nil, fmt.Errorf("the routeVersion [%d] not exist", routeVersionId)
	}
}

func (d *RouteVersionDaoImpl) ReadRouteVersionPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "RouteVersionDaoImpl ReadRouteVersionPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

//func (d *RouteVersionDaoImpl) ReadInfoPageByFilter(filter *api.RouteVersionFilter) (*information.PageData, error) {
//	conditions := filter.GetJoinCondition()
//	query, args := filter.ToAliasSql()
//
//	records := make([]api.RouteVersionInfo, 0)
//	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
//	if log.IfError(err, "SectionDaoImpl ReadInfoPageByFilter") {
//		return nil, err
//	} else {
//		return page, err
//	}
//}

func (d *RouteVersionDaoImpl) ReadLastByFilter(filter *api.RouteVersionFilter) (*api.RouteVersion, error) {
	records := make([]api.RouteVersion, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "RouteVersionDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

//func (d *RouteVersionDaoImpl) ReadRouteVersionInfo(routeVersionId uint64) (*api.RouteVersionInfo, error) {
//	var routeVersion api.RouteVersionInfo
//	filter := api.RouteVersionFilter{}
//	conditions := filter.GetJoinCondition()
//	exist, err := d.JoinFindById(routeVersionId, &routeVersion, conditions)
//	if err != nil {
//		log.Errorf("RouteVersionDaoImpl ReadRouteVersionInfo [%d] fail,as:%s", routeVersionId, err.Error())
//		return nil, err
//	} else if exist {
//		return &routeVersion, nil
//	} else {
//		log.Debugf("RouteVersionDaoImpl the routeVersion id=[%d] not exist", routeVersionId)
//		return nil, fmt.Errorf("the routeVersion [%d] not exist", routeVersionId)
//	}
//}

//func (d *RouteVersionDaoImpl) ReadInfoListByFilter(filter *api.RouteVersionFilter) ([]api.RouteVersionInfo, error) {
//	conditions := filter.GetJoinCondition()
//	query, args := filter.ToAliasSql()
//
//	records := make([]api.RouteVersionInfo, 0)
//	err := d.JoinQuery(&records, conditions, filter.GetOrderBy(true), query, args...)
//	if log.IfError(err, "RouteVersionDaoImpl ReadInfoListByFilter") {
//		return nil, err
//	} else {
//		return records, err
//	}
//}

//
//func (d *RouteVersionDaoImpl) ReadLastInfoByFilter(filter *api.RouteVersionFilter) (*api.RouteVersionInfo, error) {
//	filter.Sort = 1 // 逆序排列，返回最后一条
//	conditions := filter.GetJoinCondition()
//	query, args := filter.ToAliasSql()
//
//	records := make([]api.RouteVersionInfo, 0)
//	err := d.JoinFirst(&records, conditions, filter.GetOrderBy(true), query, args...)
//	if log.IfError(err, "RouteVersionDaoImpl ReadInfoListByFilter") {
//		return nil, err
//	} else if len(records) > 0 {
//		return &records[0], err
//	} else {
//		return nil, nil
//	}
//}

func (d *RouteVersionDaoImpl) FindRouteVersionPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "RouteVersionDaoImpl FindRouteVersionPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *RouteVersionDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]api.RouteVersionInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "RouteVersionDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *RouteVersionDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]api.RouteVersionInfo, error) {
	records := make([]api.RouteVersionInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "RouteVersionDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *RouteVersionDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*api.RouteVersionInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(api.RouteVersionInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "RouteVersionDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "RouteVersionDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *RouteVersionDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindRouteVersionPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *RouteVersionDaoImpl) ReadByFilter(filter information.IFilter) ([]api.RouteVersion, error) {
	records := make([]api.RouteVersion, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "RouteVersionDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *RouteVersionDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *RouteVersionDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *RouteVersionDaoImpl) Entity() information.IRecord {
	return new(api.RouteVersion)
}

func (d *RouteVersionDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(api.RouteVersionFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
