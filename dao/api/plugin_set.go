package api

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/utils"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/api"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for gateway.GatewayPluginSet
type GatewayPluginSetDaoImpl struct {
	db.DaoImpl
}

// new a data access object for gateway
func NewGatewayPluginSetDao(session *xorm.Session, ctx context.Context) (*GatewayPluginSetDaoImpl, error) {
	gatewayDao := GatewayPluginSetDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(api.GatewayPluginSet)
	if err := gatewayDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	gatewayDao.NewSlice = func() interface{} {
		slice := make([]api.GatewayPluginSet, 0)
		return &slice
	}

	return &gatewayDao, nil
}

func (d *GatewayPluginSetDaoImpl) CreateGatewayPluginSet(gateway *api.GatewayPluginSet) (int64, error) {
	affected, err := d.Create(gateway)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginSetDaoImpl CreateGatewayPluginSet [%v] fail,as:%s", gateway, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayPluginSetDaoImpl) DeleteGatewayPluginSet(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginSetDaoImpl DeleteGatewayPluginSet [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayPluginSetDaoImpl) UpdateGatewayPluginSet(gateway *api.GatewayPluginSet, gateways ...string) (int64, error) {
	if gateway == nil {
		return 0, fmt.Errorf("invalid paramter, gateway=nil")
	}

	affected, err := d.Update(gateway.ID, gateway, gateways...)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginSetDaoImpl UpdateGatewayPluginSet [%v] fail,as:%s", gateway.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayPluginSetDaoImpl) FindAll() ([]api.GatewayPluginSet, error) {
	var gateways []api.GatewayPluginSet
	err := d.SortListBy(&gateways, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "GatewayPluginSetDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return gateways, nil
}

func (d *GatewayPluginSetDaoImpl) ReadGatewayPluginSet(gatewayId uint64) (*api.GatewayPluginSet, error) {
	var gateway api.GatewayPluginSet
	exist, err := d.FindById(gatewayId, &gateway)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginSetDaoImpl Read [%d] fail,as:%s", gatewayId, err.Error())
		return nil, err
	} else if exist {
		return &gateway, nil
	} else {
		log.Debugf(d.Ctx, "GatewayPluginSetDaoImpl the gateway id=[%d] not exist", gatewayId)
		return nil, fmt.Errorf("the gateway [%d] not exist", gatewayId)
	}
}

func (d *GatewayPluginSetDaoImpl) ReadGatewayPluginSetByGatewayId(gatewayId uint64) (*api.GatewayPluginSet, error) {
	set := new(api.GatewayPluginSet)
	exist, err := d.Find(set, "gateway_id=?", gatewayId)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginSetDaoImpl.ReadGatewayPluginSetByGatewayId Read [%d] fail,as:%s", gatewayId, err.Error())
		return nil, err
	} else if exist {
		return set, nil
	} else {
		log.Debugf(d.Ctx, "GatewayPluginSetDaoImpl the gateway id=[%d] not exist", gatewayId)
		return nil, fmt.Errorf("the gateway [%d] not exist", gatewayId)
	}
}

func (d *GatewayPluginSetDaoImpl) ReadGatewayPluginSetByType(setType string) (*api.GatewayPluginSet, error) {
	var gateway api.GatewayPluginSet
	exist, err := d.Find(&gateway, "set_type=?", setType)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginSetDaoImpl Read setType=[%s] fail,as: %s", setType, err.Error())
		return nil, err
	} else if exist {
		return &gateway, nil
	} else {
		log.Debugf(d.Ctx, "GatewayPluginSetDaoImpl the gateway setType=[%s] not exist", setType)
		return nil, nil
	}
}

func (d *GatewayPluginSetDaoImpl) ReadGatewayPluginSetPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginSetDaoImpl ReadGatewayPluginSetPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *GatewayPluginSetDaoImpl) ReadLastByFilter(filter *api.GatewayPluginSetFilter) (*api.GatewayPluginSet, error) {
	records := make([]api.GatewayPluginSet, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "GatewayPluginSetDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *GatewayPluginSetDaoImpl) ReadGatewayPluginSetInfo(setId uint64) (*api.GatewayPluginSetInfo, error) {
	var set api.GatewayPluginSetInfo
	filter := new(api.GatewayPluginSetFilter)
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(setId, &set, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginSetDaoImpl ReadGatewayPluginSetInfo id=[%d] fail,as:%s", setId, err.Error())
		return nil, err
	} else if exist {
		return &set, nil
	} else {
		log.Debugf(d.Ctx, "GatewayPluginSetDaoImpl the set id=[%d] not exist", setId)
		return nil, fmt.Errorf("the set id=[%d] not exist", setId)
	}
}

func (d *GatewayPluginSetDaoImpl) ReadLastInfoByFilter(filter *api.GatewayPluginSetFilter) (*api.GatewayPluginSetInfo, error) {
	filter.Sort = 1 // 逆序排列，返回最后一条
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]api.GatewayPluginSetInfo, 0)
	err := d.JoinFirst(&records, conditions, filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "GatewayPluginSetDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *GatewayPluginSetDaoImpl) FindGatewayPluginSetPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "GatewayPluginSetDaoImpl FindGatewayPluginSetPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *GatewayPluginSetDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]api.GatewayPluginSetInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "GatewayPluginSetDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *GatewayPluginSetDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]api.GatewayPluginSetInfo, error) {
	records := make([]api.GatewayPluginSetInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "GatewayPluginSetDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *GatewayPluginSetDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*api.GatewayPluginSetInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(api.GatewayPluginSetInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayPluginSetDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "GatewayPluginSetDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *GatewayPluginSetDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindGatewayPluginSetPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *GatewayPluginSetDaoImpl) ReadByFilter(filter information.IFilter) ([]api.GatewayPluginSet, error) {
	records := make([]api.GatewayPluginSet, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "GatewayPluginSetDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *GatewayPluginSetDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *GatewayPluginSetDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *GatewayPluginSetDaoImpl) Entity() information.IRecord {
	return new(api.GatewayPluginSet)
}

func (d *GatewayPluginSetDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(api.GatewayPluginSetFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
