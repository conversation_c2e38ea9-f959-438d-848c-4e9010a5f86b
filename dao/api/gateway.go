package api

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/api"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for gateway.Gateway
type GatewayDaoImpl struct {
	db.DaoImpl
}

// new a data access object for gateway
func NewGatewayDao(session *xorm.Session, ctx context.Context) (*GatewayDaoImpl, error) {
	gatewayDao := GatewayDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(api.Gateway)
	if err := gatewayDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	gatewayDao.NewSlice = func() interface{} {
		slice := make([]api.Gateway, 0)
		return &slice
	}

	return &gatewayDao, nil
}

func (d *GatewayDaoImpl) CreateGateway(gateway *api.Gateway) (int64, error) {
	affected, err := d.Create(gateway)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayDaoImpl CreateGateway [%v] fail,as:%s", gateway, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayDaoImpl) DeleteGateway(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayDaoImpl DeleteGateway [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayDaoImpl) UpdateGateway(gateway *api.Gateway, gateways ...string) (int64, error) {
	if gateway == nil {
		return 0, fmt.Errorf("invalid paramter, gateway=nil")
	}

	affected, err := d.Update(gateway.ID, gateway, gateways...)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayDaoImpl UpdateGateway [%v] fail,as:%s", gateway.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayDaoImpl) UpdateGatewayStack(envID, stackID uint64) (int64, error) {
	gateway := api.Gateway{
		StackID: stackID,
	}
	fields := []string{"stack_id"}
	affected, err := d.UpdateColBy(&gateway, fields, "environment_id=?", envID)
	if err != nil {
		log.Errorf(d.Ctx, "ServiceCustomConfigDaoImpl UpdateGatewayStack [%d] fail,as:%s", envID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GatewayDaoImpl) FindAll() ([]api.Gateway, error) {
	var gateways []api.Gateway
	err := d.SortListBy(&gateways, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "GatewayDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return gateways, nil
}

func (d *GatewayDaoImpl) ReadGateway(gatewayId uint64) (*api.Gateway, error) {
	var gateway api.Gateway
	exist, err := d.FindById(gatewayId, &gateway)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayDaoImpl Read [%d] fail,as:%s", gatewayId, err.Error())
		return nil, err
	} else if exist {
		return &gateway, nil
	} else {
		log.Debugf(d.Ctx, "GatewayDaoImpl the gateway id=[%d] not exist", gatewayId)
		return nil, fmt.Errorf("the gateway [%d] not exist", gatewayId)
	}
}

func (d *GatewayDaoImpl) ReadGatewayPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayDaoImpl ReadGatewayPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *GatewayDaoImpl) ReadLastByFilter(filter *api.GatewayFilter) (*api.Gateway, error) {
	records := make([]api.Gateway, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "GatewayDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *GatewayDaoImpl) ReadGatewayInfo(gatewayId uint64) (*api.GatewayInfo, error) {
	var gateway api.GatewayInfo
	filter := api.GatewayFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(gatewayId, &gateway, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayDaoImpl ReadGatewayInfo [%d] fail,as:%s", gatewayId, err.Error())
		return nil, err
	} else if exist {
		return &gateway, nil
	} else {
		log.Debugf(d.Ctx, "GatewayDaoImpl the gateway id=[%d] not exist", gatewayId)
		return nil, fmt.Errorf("the gateway [%d] not exist", gatewayId)
	}
}

func (d *GatewayDaoImpl) ReadLastInfoByFilter(filter *api.GatewayFilter) (*api.GatewayInfo, error) {
	filter.Sort = 1 // 逆序排列，返回最后一条
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]api.GatewayInfo, 0)
	err := d.JoinFirst(&records, conditions, filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "GatewayDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *GatewayDaoImpl) FindGatewayPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "GatewayDaoImpl FindGatewayPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *GatewayDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]api.GatewayInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "GatewayDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *GatewayDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]api.GatewayInfo, error) {
	records := make([]api.GatewayInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "GatewayDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *GatewayDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*api.GatewayInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(api.GatewayInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GatewayDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "GatewayDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *GatewayDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindGatewayPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *GatewayDaoImpl) ReadByFilter(filter information.IFilter) ([]api.Gateway, error) {
	records := make([]api.Gateway, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "GatewayDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *GatewayDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *GatewayDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *GatewayDaoImpl) Entity() information.IRecord {
	return new(api.Gateway)
}

func (d *GatewayDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(api.GatewayFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
