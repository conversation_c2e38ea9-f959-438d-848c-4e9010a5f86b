package api

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/api"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for api.FunctionOpLog
type FunctionOpLogDaoImpl struct {
	database.DaoImpl
}

// new a data access object for function
func NewFunctionOpLogDao(session *xorm.Session, ctx context.Context) (*FunctionOpLogDaoImpl, error) {
	functionDao := FunctionOpLogDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(api.FunctionOpLog)
	if err := functionDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	functionDao.NewSlice = func() interface{} {
		slice := make([]api.FunctionOpLog, 0)
		return &slice
	}

	return &functionDao, nil
}

func (d *FunctionOpLogDaoImpl) CreateFunctionOpLog(function *api.FunctionOpLog) (int64, error) {
	affected, err := d.Create(function)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionOpLogDaoImpl CreateFunctionOpLog [%v] fail,as:%s", function, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *FunctionOpLogDaoImpl) DeleteFunctionOpLog(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionOpLogDaoImpl DeleteFunctionOpLog [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *FunctionOpLogDaoImpl) UpdateFunctionOpLog(function *api.FunctionOpLog, fields ...string) (int64, error) {
	if function == nil {
		return 0, fmt.Errorf("invalid paramter, function=nil")
	}

	affected, err := d.Update(function.ID, function, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionOpLogDaoImpl UpdateFunctionOpLog [%v] fail,as:%s", function.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *FunctionOpLogDaoImpl) FindAll() ([]api.FunctionOpLog, error) {
	var functions []api.FunctionOpLog
	err := d.SortListBy(&functions, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "FunctionOpLogDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return functions, nil
}

func (d *FunctionOpLogDaoImpl) ReadFunctionOpLog(functionId uint64) (*api.FunctionOpLog, error) {
	var function api.FunctionOpLog
	exist, err := d.FindById(functionId, &function)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionOpLogDaoImpl Read [%d] fail,as:%s", functionId, err.Error())
		return nil, err
	} else if exist {
		return &function, nil
	} else {
		log.Debugf(d.Ctx, "FunctionOpLogDaoImpl the function id=[%d] not exist", functionId)
		return nil, fmt.Errorf("the function [%d] not exist", functionId)
	}
}

func (d *FunctionOpLogDaoImpl) ReadFunctionOpLogPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionOpLogDaoImpl ReadFunctionOpLogPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *FunctionOpLogDaoImpl) ReadFunctionOpLogInfo(functionId uint64) (*api.FunctionOpLogInfo, error) {
	var function api.FunctionOpLogInfo
	filter := api.FunctionOpLogFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(functionId, &function, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionOpLogDaoImpl ReadFunctionOpLogInfo [%d] fail,as:%s", functionId, err.Error())
		return nil, err
	} else if exist {
		return &function, nil
	} else {
		log.Debugf(d.Ctx, "FunctionOpLogDaoImpl the function id=[%d] not exist", functionId)
		return nil, fmt.Errorf("the function [%d] not exist", functionId)
	}
}

func (d *FunctionOpLogDaoImpl) FindFunctionOpLogPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "FunctionOpLogDaoImpl FindFunctionOpLogPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *FunctionOpLogDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]api.FunctionOpLogInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "FunctionOpLogDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *FunctionOpLogDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]api.FunctionOpLogInfo, error) {
	records := make([]api.FunctionOpLogInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "FunctionOpLogDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *FunctionOpLogDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*api.FunctionOpLogInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(api.FunctionOpLogInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "FunctionOpLogDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "FunctionOpLogDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *FunctionOpLogDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindFunctionOpLogPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *FunctionOpLogDaoImpl) ReadByFilter(filter information.IFilter) ([]api.FunctionOpLog, error) {
	records := make([]api.FunctionOpLog, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "FunctionOpLogDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *FunctionOpLogDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *FunctionOpLogDaoImpl) Entity() information.IRecord {
	return new(api.FunctionOpLog)
}

func (d *FunctionOpLogDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(api.FunctionOpLogFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	return filter, err
}

func (d *FunctionOpLogDaoImpl) ReadLastByFilter(filter *api.FunctionOpLogFilter) (*api.FunctionOpLog, error) {
	records := make([]api.FunctionOpLog, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "FunctionOpLogDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *FunctionOpLogDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}
