package api

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/api"
)

func TestRouteDaoImpl_CreateRoute(t *testing.T) {
	route := api.Route{}
	routeDao, _ := NewRouteDao(nil, context.TODO())
	id, err := routeDao.CreateRoute(&route)
	if err != nil {
		t.<PERSON><PERSON>("RouteDaoImpl CreateRoute fail,as[%s]", err.Error())
	} else {
		t.Log("RouteDaoImpl CreateRoute success, id=", id)
	}
}

func TestRouteDaoImpl_UpdateRoute(t *testing.T) {
	route := api.Route{}
	routeDao, _ := NewRouteDao(nil, context.TODO())
	affected, err := routeDao.UpdateRoute(&route)
	if err != nil {
		t.Errorf("RouteDaoImpl UpdateRoute fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("RouteDaoImpl UpdateRoute success, affected=", affected)
	}
}

func TestRouteDaoImpl_ReadRoutePage(t *testing.T) {
	routeDao, _ := NewRouteDao(nil, context.TODO())
	routes, err := routeDao.ReadRoutePage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("RouteDaoImpl ReadRoutePage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(routes)
		t.Log("RouteDaoImpl ReadRoutePage success, data=", string(bytes))
	}
}

func TestRouteDaoImpl_FindAll(t *testing.T) {
	routeDao, _ := NewRouteDao(nil, context.TODO())
	routes, err := routeDao.FindAll()
	if err != nil {
		t.Errorf("RouteDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(routes)
		t.Log("RouteDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestRouteDaoImpl_ReadRoute(t *testing.T) {
	routeDao, _ := NewRouteDao(nil, context.TODO())
	route, err := routeDao.ReadRoute(1)
	if err != nil {
		t.Errorf("RouteDaoImpl ReadRoute fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(route)
		t.Log("RouteDaoImpl ReadRoute success, data=", string(bytes))
	}
}

func TestRouteDaoImpl_DeleteRoute(t *testing.T) {
	routeDao, _ := NewRouteDao(nil, context.TODO())
	affected, err := routeDao.DeleteRoute(1)
	if err != nil {
		t.Errorf("RouteDaoImpl DeleteRoute fail,as[%s]", err.Error())
	} else {
		t.Log("RouteDaoImpl DeleteRoute success, affected=", affected)
	}
}
