package gls

import (
	"fmt"
	"time"

	model "git.platform.io/environment/environment/model/gls"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/information"

	"git.platform.io/environment/environment/database"
	"git.platform.io/resource/common/log"
)

// Data Access Object for ip
type GlsClusterDaoImpl struct {
	database.DaoImpl
}

func NewGlsClusterDao(request app.IRequest) (*GlsClusterDaoImpl, error) {
	dao := GlsClusterDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: request.GetCtx(),
		},
	}
	m := new(model.GlsCluster)
	if err := dao.InitWithRequest(request, m.TableName(), m); err != nil {
		return nil, err
	}
	dao.NewSlice = func() interface{} {
		var slice []model.GlsCluster
		return &slice
	}

	return &dao, nil
}

func (d *GlsClusterDaoImpl) NewGlsCluster(name, description, status string, args ...string) error {
	user := "system"
	if len(args) > 0 {
		user = args[0]
	}

	now := time.Now()
	newGlsCluster := &model.GlsCluster{
		Name:        name,
		Description: description,
		Status:      status,
		CreatedAt:   now,
		CreatorId:   user,
		UpdatedAt:   now,
		UpdaterId:   user,
	}
	_, err := d.Create(newGlsCluster)
	if err != nil {
		return err
	} else {
		return nil
	}
}

func (d *GlsClusterDaoImpl) ModifyGlsClusters(creates []model.GlsCluster, removes []model.GlsCluster) (string, error) {
	// start session
	alert := ""
	var err error
	session := d.NewSession()
	if session == nil {
		err = fmt.Errorf("GlsClusterDaoImpl Create Xorm Session fail, get empty return")
		log.Error(d.Ctx, err.Error())
		return alert, err
	}
	defer func() {
		if err != nil {
			err = session.Rollback()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		} else {
			err = session.Commit()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		}
	}()

	// get create or update records
	ins := make([]interface{}, 0)
	var args []interface{}
	where := "1 = 1"
	for _, relation := range creates {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
		}
		var b bool
		b, err = session.NoAutoCondition(true).Where(where, args...).Get(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
		if b { // update
			_, err = session.NoAutoCondition(true).Cols("name", "description", "status", "updated_at", "updater_id").Where(where, args...).Update(relation)
			if err != nil {
				log.Error(d.Ctx, err.Error())
				return alert, err
			}
			continue
		}
		if relation.Id > 0 {
			// ignore
			continue
		}

		ins = append(ins, relation)
	}
	if len(ins) > 0 {
		_, err = session.Insert(ins...)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}
	// remove
	for _, relation := range removes {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
		}
		_, err := session.NoAutoCondition(true).Where(where, args...).Delete(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}

	return alert, err
}

func (d *GlsClusterDaoImpl) CreateGlsClusterByMap(GlsCluster *map[string]interface{}) (int64, error) {
	affected, err := d.CreateWithMap(GlsCluster)
	if err != nil {
		log.Errorf(d.Ctx, "GlsClusterDaoImpl CreateGlsClusterByMap [%v] fail,as:%s", GlsCluster, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GlsClusterDaoImpl) DeleteGlsCluster(GlsCluster *model.GlsCluster) (int64, error) {
	affected, err := d.DeleteById(GlsCluster.Id)
	if err != nil {
		log.Errorf(d.Ctx, "GlsClusterDaoImpl DeleteGlsCluster [%v] fail,as:%s", GlsCluster.Id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GlsClusterDaoImpl) UpdateGlsCluster(id uint64, bean *model.GlsCluster, fields ...string) (int64, error) {
	if bean == nil {
		return 0, fmt.Errorf("invalid paramter, GlsCluster=nil")
	}
	if id == 0 {
		return 0, fmt.Errorf("invalid paramter for zero id")
	}
	affected, err := d.Update(id, bean, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "GlsClusterDaoImpl UpdateGlsCluster [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GlsClusterDaoImpl) FindAll() (*[]model.GlsCluster, error) {
	GlsClusters := make([]model.GlsCluster, 0)
	err := d.ListAll(&GlsClusters)
	if err != nil {
		log.Error(d.Ctx, "GlsClusterDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return &GlsClusters, nil
}

func (d *GlsClusterDaoImpl) CheckGlsClusterWithName(name string) (*model.GlsCluster, bool, error) {
	var glsCluster model.GlsCluster
	exist, err := d.Find(&glsCluster, "name = ?", name)
	return &glsCluster, exist, err
}

func (d *GlsClusterDaoImpl) ReadGlsClusterWithName(name string) (*model.GlsCluster, error) {
	glsCluster, exist, err := d.CheckGlsClusterWithName(name)
	if err != nil {
		log.Errorf(d.Ctx, "GlsClusterDaoImpl ReadGlsCluster [%s] fail,as:%s", name, err.Error())
		return nil, err
	} else if exist {
		return glsCluster, nil
	} else {
		log.Debugf(d.Ctx, "GlsClusterDaoImpl the GlsCluster id=[%s] not exist", name)
		return nil, fmt.Errorf("the GlsCluster [%s] not exist", name)
	}
}

func (d *GlsClusterDaoImpl) ReadGlsCluster(GlsClusterId uint64) (*model.GlsCluster, error) {
	var GlsCluster model.GlsCluster
	exist, err := d.FindById(GlsClusterId, &GlsCluster)
	if err != nil {
		log.Errorf(d.Ctx, "GlsClusterDaoImpl ReadGlsCluster [%d] fail,as:%s", GlsClusterId, err.Error())
		return nil, err
	} else if exist {
		return &GlsCluster, nil
	} else {
		log.Debugf(d.Ctx, "GlsClusterDaoImpl the GlsCluster id=[%d] not exist", GlsClusterId)
		return nil, fmt.Errorf("the GlsCluster [%d] not exist", GlsClusterId)
	}
}

func (d *GlsClusterDaoImpl) ReadGlsClusterPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GlsClusterDaoImpl ReadGlsClusterPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *GlsClusterDaoImpl) ReadByFilter(filter *model.GlsClusterFilter) ([]model.GlsCluster, error) {
	records := make([]model.GlsCluster, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "PlanDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *GlsClusterDaoImpl) FindGlsClusterPage(filter *model.GlsClusterFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "PlanDaoImpl FindPlanPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *GlsClusterDaoImpl) ReadGlsClusters(query interface{}, args ...interface{}) (*[]model.GlsCluster, error) {
	result := make([]model.GlsCluster, 0)
	err := d.ListBy(&result, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GlsClusterDaoImpl ReadGlsClusterPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return &result, nil
	}
}
