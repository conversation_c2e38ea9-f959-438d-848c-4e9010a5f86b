package gls

import (
	"fmt"
	"time"

	model "git.platform.io/environment/environment/model/gls"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/information"

	"git.platform.io/environment/environment/database"
	"git.platform.io/resource/common/log"
)

const (
	GlsSyncService = "majmad"
)

// Data Access Object for ip
type GlsServiceDaoImpl struct {
	database.DaoImpl
}

func NewGlsServiceDao(request app.IRequest) (*GlsServiceDaoImpl, error) {
	dao := GlsServiceDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: request.GetCtx(),
		},
	}
	m := new(model.GlsService)
	if err := dao.InitWithRequest(request, m.TableName(), m); err != nil {
		return nil, err
	}
	dao.NewSlice = func() interface{} {
		var slice []model.GlsService
		return &slice
	}

	return &dao, nil
}

func (d *GlsServiceDaoImpl) NewGlsService(name, description, status, _type, instanceNum, version, logLevel, cpuRequest, cpuLimit, memoryRequest, memoryLimit, nodeIP string, args ...string) error {
	user := "system"
	if len(args) > 0 {
		user = args[0]
	}

	now := time.Now()
	newGlsService := &model.GlsService{
		Name:          name,
		Description:   description,
		Status:        status,
		Type:          _type,
		InstanceNum:   instanceNum,
		Version:       version,
		LogLevel:      logLevel,
		CPURequest:    cpuRequest,
		CPULimit:      cpuLimit,
		MemoryRequest: memoryRequest,
		MemoryLimit:   memoryLimit,
		NodeIP:        nodeIP,
		CreatedAt:     now,
		CreatorId:     user,
		UpdatedAt:     now,
		UpdaterId:     user,
	}
	_, err := d.Create(newGlsService)
	if err != nil {
		return err
	} else {
		return nil
	}
}

func (d *GlsServiceDaoImpl) CreateGlsService(GlsService *model.GlsService) (int64, error) {
	affected, err := d.Create(GlsService)
	if err != nil {
		log.Errorf(d.Ctx, "GlsServiceDaoImpl CreateGlsService [%v] fail,as:%s", GlsService, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GlsServiceDaoImpl) ModifyGlsServices(creates []model.GlsService, removes []model.GlsService) (string, error) {
	// start session
	alert := ""
	var err error
	session := d.NewSession()
	if session == nil {
		err = fmt.Errorf("GlsServiceDaoImpl Create Xorm Session fail, get empty return")
		log.Error(d.Ctx, err.Error())
		return alert, err
	}
	defer func() {
		if err != nil {
			err = session.Rollback()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		} else {
			err = session.Commit()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		}
	}()

	// get create or update records
	ins := make([]interface{}, 0)
	var args []interface{}
	where := "1 = 1"
	for _, relation := range creates {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and cluster_name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
			if len(relation.Type) > 0 {
				where += " and `type` = ?"
				args = append(args, relation.Type)
			}
			if len(relation.InstanceNum) > 0 {
				where += " and instance_num = ?"
				args = append(args, relation.InstanceNum)
			}
			if len(relation.Version) > 0 {
				where += " and version = ?"
				args = append(args, relation.Version)
			}
			if len(relation.LogLevel) > 0 {
				where += " and log_level = ?"
				args = append(args, relation.LogLevel)
			}
			if len(relation.CPURequest) > 0 {
				where += " and cpu_request = ?"
				args = append(args, relation.CPURequest)
			}
			if len(relation.CPULimit) > 0 {
				where += " and cpu_limit = ?"
				args = append(args, relation.CPULimit)
			}
			if len(relation.MemoryRequest) > 0 {
				where += " and memory_request = ?"
				args = append(args, relation.MemoryRequest)
			}
			if len(relation.MemoryLimit) > 0 {
				where += " and memory_limit = ?"
				args = append(args, relation.MemoryLimit)
			}
			if len(relation.NodeIP) > 0 {
				where += " and node_ip = ?"
				args = append(args, relation.NodeIP)
			}
		}
		var b bool
		b, err = session.NoAutoCondition(true).Where(where, args...).Get(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
		if b { // update
			_, err = session.NoAutoCondition(true).Cols("cluster_name", "description", "status", "updated_at", "updater_id").Where(where, args...).Update(relation)
			if err != nil {
				log.Error(d.Ctx, err.Error())
				return alert, err
			}
			continue
		}
		if relation.Id > 0 {
			// ignore
			continue
		}

		ins = append(ins, relation)
	}
	if len(ins) > 0 {
		_, err = session.Insert(ins...)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}
	// remove
	for _, relation := range removes {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and cluster_name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
			if len(relation.Type) > 0 {
				where += " and `type` = ?"
				args = append(args, relation.Type)
			}
			if len(relation.InstanceNum) > 0 {
				where += " and instance_num = ?"
				args = append(args, relation.InstanceNum)
			}
			if len(relation.Version) > 0 {
				where += " and version = ?"
				args = append(args, relation.Version)
			}
			if len(relation.LogLevel) > 0 {
				where += " and log_level = ?"
				args = append(args, relation.LogLevel)
			}
			if len(relation.CPURequest) > 0 {
				where += " and cpu_request = ?"
				args = append(args, relation.CPURequest)
			}
			if len(relation.CPULimit) > 0 {
				where += " and cpu_limit = ?"
				args = append(args, relation.CPULimit)
			}
			if len(relation.MemoryRequest) > 0 {
				where += " and memory_request = ?"
				args = append(args, relation.MemoryRequest)
			}
			if len(relation.MemoryLimit) > 0 {
				where += " and memory_limit = ?"
				args = append(args, relation.MemoryLimit)
			}
			if len(relation.NodeIP) > 0 {
				where += " and node_ip = ?"
				args = append(args, relation.NodeIP)
			}
		}
		_, err := session.NoAutoCondition(true).Where(where, args...).Delete(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}

	return alert, err
}

func (d *GlsServiceDaoImpl) CreateGlsServiceByMap(GlsService *map[string]interface{}) (int64, error) {
	affected, err := d.CreateWithMap(GlsService)
	if err != nil {
		log.Errorf(d.Ctx, "GlsServiceDaoImpl CreateGlsServiceByMap [%v] fail,as:%s", GlsService, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GlsServiceDaoImpl) DeleteGlsService(GlsService *model.GlsService) (int64, error) {
	affected, err := d.DeleteById(GlsService.Id)
	if err != nil {
		log.Errorf(d.Ctx, "GlsServiceDaoImpl DeleteGlsService [%v] fail,as:%s", GlsService.Id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GlsServiceDaoImpl) UpdateGlsService(id uint64, bean *model.GlsService, fields ...string) (int64, error) {
	if bean == nil {
		return 0, fmt.Errorf("invalid paramter, GlsService=nil")
	}
	if id == 0 {
		return 0, fmt.Errorf("invalid paramter for zero id")
	}
	affected, err := d.Update(id, bean, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "GlsServiceDaoImpl UpdateGlsService [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GlsServiceDaoImpl) FindAll() (*[]model.GlsService, error) {
	GlsServices := make([]model.GlsService, 0)
	err := d.ListAll(&GlsServices)
	if err != nil {
		log.Error(d.Ctx, "GlsServiceDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return &GlsServices, nil
}

func (d *GlsServiceDaoImpl) ReadGlsService(GlsServiceId uint64) (*model.GlsService, error) {
	var GlsService model.GlsService
	exist, err := d.FindById(GlsServiceId, &GlsService)
	if err != nil {
		log.Errorf(d.Ctx, "GlsServiceDaoImpl ReadGlsService [%d] fail,as:%s", GlsServiceId, err.Error())
		return nil, err
	} else if exist {
		return &GlsService, nil
	} else {
		log.Debugf(d.Ctx, "GlsServiceDaoImpl the GlsService id=[%d] not exist", GlsServiceId)
		return nil, fmt.Errorf("the GlsService [%d] not exist", GlsServiceId)
	}
}

func (d *GlsServiceDaoImpl) ReadGlsServicePage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GlsServiceDaoImpl ReadGlsServicePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *GlsServiceDaoImpl) ReadGlsServices(query interface{}, args ...interface{}) (*[]model.GlsService, error) {
	result := make([]model.GlsService, 0)
	err := d.ListBy(&result, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GlsServiceDaoImpl ReadGlsServicePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return &result, nil
	}
}

func (d *GlsServiceDaoImpl) ReadGlsServiceWithServiceInstanceIdAndApplication(instanceId, appId uint64) (*model.GlsService, error) {
	glsCluster, exist, err := d.CheckGlsServiceWithServiceInstanceIdAndApplication(instanceId, appId)
	if err != nil {
		log.Errorf(d.Ctx, "GlsServiceDaoImpl ReadGlsService[%d][%d] fail,as:%s", instanceId, appId, err.Error())
		return nil, err
	} else if exist {
		return glsCluster, nil
	} else {
		log.Debugf(d.Ctx, "GlsServiceDaoImpl the GlsService id=[%d][%d] not exist", instanceId, appId)
		return nil, fmt.Errorf("the GlsService [%d][%d] not exist", instanceId, appId)
	}
}

func (d *GlsServiceDaoImpl) CheckGlsServiceWithServiceInstanceIdAndApplication(instanceId, appId uint64) (*model.GlsService, bool, error) {
	var glsService model.GlsService
	exist, err := d.Find(&glsService, "instance_ref_id = ? and application_id = ?", instanceId, appId)
	return &glsService, exist, err
}
