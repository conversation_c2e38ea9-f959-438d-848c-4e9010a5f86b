package gls

import (
	"context"
	"fmt"
	"strings"
	"time"

	model "git.platform.io/environment/environment/model/gls"
	"git.platform.io/environment/environment/model/release"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"

	"git.platform.io/environment/environment/database"
	"git.platform.io/resource/common/log"
)

// Data Access Object for ip
type GlsShardingDataDaoImpl struct {
	database.DaoImpl
}

func NewGlsShardingDataDao(request app.IRequest) (*GlsShardingDataDaoImpl, error) {
	dao := GlsShardingDataDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: request.GetCtx(),
		},
	}
	m := new(model.GlsShardingData)
	if err := dao.InitWithRequest(request, m.TableName(), m); err != nil {
		return nil, err
	}
	dao.NewSlice = func() interface{} {
		var slice []model.GlsShardingData
		return &slice
	}

	return &dao, nil
}

func NewGlsShardingDataDao2(session *xorm.Session, ctx context.Context) (*GlsShardingDataDaoImpl, error) {
	daoInst := GlsShardingDataDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(model.GlsShardingData)
	if err := daoInst.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	daoInst.NewSlice = func() interface{} {
		slice := make([]release.Batch, 0)
		return &slice
	}

	return &daoInst, nil
}

func (d *GlsShardingDataDaoImpl) NewGlsShardingData(secondaryShardingId uint64, key, value string, args ...string) error {
	user := "system"
	if len(args) > 0 {
		user = args[0]
	}

	now := time.Now()
	newGlsShardingData := &model.GlsShardingData{
		Key:       key,
		Value:     value,
		CreatedAt: now,
		CreatorId: user,
		UpdatedAt: now,
		UpdaterId: user,
	}
	_, err := d.Create(newGlsShardingData)
	if err != nil {
		return err
	} else {
		return nil
	}
}

func (d *GlsShardingDataDaoImpl) CreateGlsShardingData(GlsShardingData *model.GlsShardingData) (int64, error) {
	affected, err := d.Create(GlsShardingData)
	if err != nil {
		log.Errorf(d.Ctx, "GlsShardingDataDaoImpl CreateGlsShardingData [%v] fail,as:%s", GlsShardingData, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GlsShardingDataDaoImpl) ModifyGlsShardingDatas(creates []model.GlsShardingData, removes []model.GlsShardingData) (string, error) {
	// start session
	alert := ""
	var err error
	session := d.NewSession()
	if session == nil {
		err = fmt.Errorf("GlsShardingDataDaoImpl Create Xorm Session fail, get empty return")
		log.Error(d.Ctx, err.Error())
		return alert, err
	}
	defer func() {
		if err != nil {
			err = session.Rollback()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		} else {
			err = session.Commit()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		}
	}()

	// get create or update records
	ins := make([]interface{}, 0)
	var args []interface{}
	where := "1 = 1"
	for _, relation := range creates {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.ID > 0 {
			where += " and id = ?"
			args = append(args, relation.ID)
		} else {
			if len(relation.Key) > 0 {
				where += " and `key' = ?"
				args = append(args, relation.Key)
			}
			if len(relation.Value) > 0 {
				where += " and value = ?"
				args = append(args, relation.Value)
			}
		}
		var b bool
		b, err = session.NoAutoCondition(true).Where(where, args...).Get(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
		if b { // update
			_, err = session.NoAutoCondition(true).Cols("primary_sharing_id", "key", "value", "updated_at", "updater_id").Where(where, args...).Update(relation)
			if err != nil {
				log.Error(d.Ctx, err.Error())
				return alert, err
			}
			continue
		}
		if relation.ID > 0 {
			// ignore
			continue
		}

		ins = append(ins, relation)
	}
	if len(ins) > 0 {
		_, err = session.Insert(ins...)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}
	// remove
	for _, relation := range removes {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.ID > 0 {
			where += " and id = ?"
			args = append(args, relation.ID)
		} else {
			if len(relation.Key) > 0 {
				where += " and `key' = ?"
				args = append(args, relation.Key)
			}
			if len(relation.Value) > 0 {
				where += " and value = ?"
				args = append(args, relation.Value)
			}
		}
		_, err := session.NoAutoCondition(true).Where(where, args...).Delete(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}

	return alert, err
}

func (d *GlsShardingDataDaoImpl) CreateGlsShardingDataByMap(GlsShardingData *map[string]interface{}) (int64, error) {
	affected, err := d.CreateWithMap(GlsShardingData)
	if err != nil {
		log.Errorf(d.Ctx, "GlsShardingDataDaoImpl CreateGlsShardingDataByMap [%v] fail,as:%s", GlsShardingData, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GlsShardingDataDaoImpl) DeleteGlsShardingData(GlsShardingData *model.GlsShardingData) (int64, error) {
	affected, err := d.DeleteById(GlsShardingData.ID)
	if err != nil {
		log.Errorf(d.Ctx, "GlsShardingDataDaoImpl DeleteGlsShardingData [%v] fail,as:%s", GlsShardingData.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GlsShardingDataDaoImpl) UpdateGlsShardingData(id uint64, bean *model.GlsShardingData, fields ...string) (int64, error) {
	if bean == nil {
		return 0, fmt.Errorf("invalid paramter, GlsShardingData=nil")
	}
	if id == 0 {
		return 0, fmt.Errorf("invalid paramter for zero id")
	}
	affected, err := d.Update(id, bean, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "GlsShardingDataDaoImpl UpdateGlsShardingData [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *GlsShardingDataDaoImpl) FindAll() (*[]model.GlsShardingData, error) {
	GlsShardingDatas := make([]model.GlsShardingData, 0)
	err := d.ListAll(&GlsShardingDatas)
	if err != nil {
		log.Error(d.Ctx, "GlsShardingDataDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return &GlsShardingDatas, nil
}

func (d *GlsShardingDataDaoImpl) ReadGlsShardingData(GlsShardingDataId uint64) (*model.GlsShardingData, error) {
	var GlsShardingData model.GlsShardingData
	exist, err := d.FindById(GlsShardingDataId, &GlsShardingData)
	if err != nil {
		log.Errorf(d.Ctx, "GlsShardingDataDaoImpl ReadGlsShardingData [%d] fail,as:%s", GlsShardingDataId, err.Error())
		return nil, err
	} else if exist {
		return &GlsShardingData, nil
	} else {
		log.Debugf(d.Ctx, "GlsShardingDataDaoImpl the GlsShardingData id=[%d] not exist", GlsShardingDataId)
		return nil, fmt.Errorf("the GlsShardingData [%d] not exist", GlsShardingDataId)
	}
}

func (d *GlsShardingDataDaoImpl) ReadGlsShardingDataPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GlsShardingDataDaoImpl ReadGlsShardingDataPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *GlsShardingDataDaoImpl) ReadGlsShardingDatas(query interface{}, args ...interface{}) (*[]model.GlsShardingData, error) {
	result := make([]model.GlsShardingData, 0)
	err := d.ListBy(&result, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GlsShardingDataDaoImpl ReadGlsShardingDataPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return &result, nil
	}
}

func (d *GlsShardingDataDaoImpl) FindGlsShardingDataPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "GlsShardingDataDaoImpl FindGlsShardingDataPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *GlsShardingDataDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]model.GlsShardingDataInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "GlsShardingDataDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *GlsShardingDataDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]model.GlsShardingDataInfo, error) {
	records := make([]model.GlsShardingDataInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "GlsShardingDataDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *GlsShardingDataDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*model.GlsShardingDataInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(model.GlsShardingDataInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "GlsShardingDataDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "GlsShardingDataDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *GlsShardingDataDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindGlsShardingDataPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *GlsShardingDataDaoImpl) ReadByFilter(filter information.IFilter) ([]model.GlsShardingData, error) {
	records := make([]model.GlsShardingData, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "GlsShardingDataDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *GlsShardingDataDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *GlsShardingDataDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *GlsShardingDataDaoImpl) Entity() information.IRecord {
	return new(model.GlsShardingData)
}

func (d *GlsShardingDataDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(model.GlsShardingDataFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	// filter.TenantID = op.TenantID

	return filter, err
}
