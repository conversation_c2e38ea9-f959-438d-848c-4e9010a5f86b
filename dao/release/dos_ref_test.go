package release

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/release"
)

func TestDosRefDaoImpl_CreateDosRef(t *testing.T) {
	dosRef := release.DosRef{}
	dosRefDao, _ := NewDosRefDao(nil, context.TODO())
	id, err := dosRefDao.CreateDosRef(&dosRef)
	if err != nil {
		t.<PERSON><PERSON>("DosRefDaoImpl CreateDosRef fail,as[%s]", err.Error())
	} else {
		t.Log("DosRefDaoImpl CreateDosRef success, id=", id)
	}
}

func TestDosRefDaoImpl_UpdateDosRef(t *testing.T) {
	dosRef := release.DosRef{}
	dosRefDao, _ := NewDosRefDao(nil, context.TODO())
	affected, err := dosRefDao.UpdateDosRef(&dosRef)
	if err != nil {
		t.<PERSON><PERSON>("DosRefDaoImpl UpdateDosRef fail,as[%s]", err.Error())
	} else {
		t.Log("DosRefDaoImpl UpdateDosRef success, affected=", affected)
	}
}

func TestDosRefDaoImpl_ReadDosRefPage(t *testing.T) {
	dosRefDao, _ := NewDosRefDao(nil, context.TODO())
	dosRefs, err := dosRefDao.ReadDosRefPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("DosRefDaoImpl ReadDosRefPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dosRefs)
		t.Log("DosRefDaoImpl ReadDosRefPage success, data=", string(bytes))
	}
}

func TestDosRefDaoImpl_FindAll(t *testing.T) {
	dosRefDao, _ := NewDosRefDao(nil, context.TODO())
	dosRefs, err := dosRefDao.FindAll()
	if err != nil {
		t.Errorf("DosRefDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dosRefs)
		t.Log("DosRefDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestDosRefDaoImpl_ReadDosRef(t *testing.T) {
	dosRefDao, _ := NewDosRefDao(nil, context.TODO())
	dosRef, err := dosRefDao.ReadDosRef(1)
	if err != nil {
		t.Errorf("DosRefDaoImpl ReadDosRef fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dosRef)
		t.Log("DosRefDaoImpl ReadDosRef success, data=", string(bytes))
	}
}

func TestDosRefDaoImpl_DeleteDosRef(t *testing.T) {
	dosRefDao, _ := NewDosRefDao(nil, context.TODO())
	affected, err := dosRefDao.DeleteDosRef(1)
	if err != nil {
		t.Errorf("DosRefDaoImpl DeleteDosRef fail,as[%s]", err.Error())
	} else {
		t.Log("DosRefDaoImpl DeleteDosRef success, affected=", affected)
	}
}
