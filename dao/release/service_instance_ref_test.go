package release

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/release"
)

func TestServiceInstanceRefDaoImpl_CreateServiceInstanceRef(t *testing.T) {
	serviceInstanceRef := release.ServiceInstanceRef{}
	serviceInstanceRefDao, _ := NewServiceInstanceRefDao(nil, context.TODO())
	id, err := serviceInstanceRefDao.CreateServiceInstanceRef(&serviceInstanceRef)
	if err != nil {
		t.Errorf("ServiceInstanceRefDaoImpl CreateServiceInstanceRef fail,as[%s]", err.Error())
	} else {
		t.Log("ServiceInstanceRefDaoImpl CreateServiceInstanceRef success, id=", id)
	}
}

func TestServiceInstanceRefDaoImpl_UpdateServiceInstanceRef(t *testing.T) {
	serviceInstanceRef := release.ServiceInstanceRef{}
	serviceInstanceRefDao, _ := NewServiceInstanceRefDao(nil, context.TODO())
	affected, err := serviceInstanceRefDao.UpdateServiceInstanceRef(&serviceInstanceRef)
	if err != nil {
		t.Errorf("ServiceInstanceRefDaoImpl UpdateServiceInstanceRef fail,as[%s]", err.Error())
	} else {
		t.Log("ServiceInstanceRefDaoImpl UpdateServiceInstanceRef success, affected=", affected)
	}
}

func TestServiceInstanceRefDaoImpl_ReadServiceInstanceRefPage(t *testing.T) {
	serviceInstanceRefDao, _ := NewServiceInstanceRefDao(nil, context.TODO())
	serviceInstanceRefs, err := serviceInstanceRefDao.ReadServiceInstanceRefPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("ServiceInstanceRefDaoImpl ReadServiceInstanceRefPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(serviceInstanceRefs)
		t.Log("ServiceInstanceRefDaoImpl ReadServiceInstanceRefPage success, data=", string(bytes))
	}
}

func TestServiceInstanceRefDaoImpl_FindAll(t *testing.T) {
	serviceInstanceRefDao, _ := NewServiceInstanceRefDao(nil, context.TODO())
	serviceInstanceRefs, err := serviceInstanceRefDao.FindAll()
	if err != nil {
		t.Errorf("ServiceInstanceRefDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(serviceInstanceRefs)
		t.Log("ServiceInstanceRefDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestServiceInstanceRefDaoImpl_ReadServiceInstanceRef(t *testing.T) {
	serviceInstanceRefDao, _ := NewServiceInstanceRefDao(nil, context.TODO())
	serviceInstanceRef, err := serviceInstanceRefDao.ReadServiceInstanceRef(1)
	if err != nil {
		t.Errorf("ServiceInstanceRefDaoImpl ReadServiceInstanceRef fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(serviceInstanceRef)
		t.Log("ServiceInstanceRefDaoImpl ReadServiceInstanceRef success, data=", string(bytes))
	}
}

func TestServiceInstanceRefDaoImpl_DeleteServiceInstanceRef(t *testing.T) {
	serviceInstanceRefDao, _ := NewServiceInstanceRefDao(nil, context.TODO())
	affected, err := serviceInstanceRefDao.DeleteServiceInstanceRef(1)
	if err != nil {
		t.Errorf("ServiceInstanceRefDaoImpl DeleteServiceInstanceRef fail,as[%s]", err.Error())
	} else {
		t.Log("ServiceInstanceRefDaoImpl DeleteServiceInstanceRef success, affected=", affected)
	}
}
