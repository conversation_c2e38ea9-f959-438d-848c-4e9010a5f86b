package release

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/release"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for release.DbRef
type DbRefDaoImpl struct {
	database.DaoImpl
}

// new a data access object for DbRef
func NewDbRefDao(session *xorm.Session, ctx context.Context) (*DbRefDaoImpl, error) {
	dbRefDao := DbRefDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(release.DbRef)
	if err := dbRefDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	dbRefDao.NewSlice = func() interface{} {
		slice := make([]release.DbRef, 0)
		return &slice
	}

	return &dbRefDao, nil
}

func (d *DbRefDaoImpl) CreateDbRef(DbRef *release.DbRef) (int64, error) {
	affected, err := d.Create(DbRef)
	if err != nil {
		log.Errorf(d.Ctx, "DbRefDaoImpl CreateDbRef [%v] fail,as:%s", DbRef, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DbRefDaoImpl) DeleteDbRef(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "DbRefDaoImpl DeleteDbRef [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DbRefDaoImpl) DeleteByIDList(idList []uint64) (int64, error) {
	arr := make([]string, 0, len(idList))
	values := make([]interface{}, 0)
	for _, id := range idList {
		arr = append(arr, "?")
		values = append(values, id)
	}

	sql := fmt.Sprintf("`id` in ( %s )", strings.Join(arr, ","))
	affected, err := d.DeleteBy(sql, values...)
	if err != nil {
		log.Errorf(d.Ctx, "DbRefDaoImpl DeleteByIDList [%v] fail,as:%s", idList, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DbRefDaoImpl) UpdateDbRef(DbRef *release.DbRef, fields ...string) (int64, error) {
	if DbRef == nil {
		return 0, fmt.Errorf("invalid paramter, DbRef=nil")
	}

	affected, err := d.Update(DbRef.ID, DbRef, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "DbRefDaoImpl UpdateDbRef [%v] fail,as:%s", DbRef.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DbRefDaoImpl) FindAll() ([]release.DbRef, error) {
	var dbRefs []release.DbRef
	err := d.SortListBy(&dbRefs, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "DbRefDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return dbRefs, nil
}

func (d *DbRefDaoImpl) ReadDbRef(dbRefId uint64) (*release.DbRef, error) {
	var DbRef release.DbRef
	exist, err := d.FindById(dbRefId, &DbRef)
	if err != nil {
		log.Errorf(d.Ctx, "DbRefDaoImpl Read [%d] fail,as:%s", dbRefId, err.Error())
		return nil, err
	} else if exist {
		return &DbRef, nil
	} else {
		log.Debugf(d.Ctx, "DbRefDaoImpl the DbRef id=[%d] not exist", dbRefId)
		return nil, fmt.Errorf("the DbRef [%d] not exist", dbRefId)
	}
}

func (d *DbRefDaoImpl) ReadDbRefPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DbRefDaoImpl ReadDbRefPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *DbRefDaoImpl) FindDbRefPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DbRefDaoImpl FindDbRefPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DbRefDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]release.DbRef, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "DbRefDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DbRefDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]release.DbRef, error) {
	records := make([]release.DbRef, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "DbRefDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DbRefDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*release.DbRef, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(release.DbRef)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DbRefDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	}

	if !exist {
		return nil, nil
	}

	return record, nil
}

func (d *DbRefDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindDbRefPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *DbRefDaoImpl) ReadByFilter(filter information.IFilter) ([]release.DbRef, error) {
	records := make([]release.DbRef, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DbRefDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DbRefDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *DbRefDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *DbRefDaoImpl) Entity() information.IRecord {
	return new(release.DbRef)
}

func (d *DbRefDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(release.DbRefFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
