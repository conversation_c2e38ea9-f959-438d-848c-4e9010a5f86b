package release

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/release"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for release.MeshRef
type MeshRefDaoImpl struct {
	database.DaoImpl
}

// new a data access object for meshRef
func NewMeshRefDao(session *xorm.Session, ctx context.Context) (*MeshRefDaoImpl, error) {
	meshRefDao := MeshRefDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(release.MeshRef)
	if err := meshRefDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	meshRefDao.NewSlice = func() interface{} {
		slice := make([]release.MeshRef, 0)
		return &slice
	}

	return &meshRefDao, nil
}

func (d *MeshRefDaoImpl) CreateMeshRef(meshRef *release.MeshRef) (int64, error) {
	affected, err := d.Create(meshRef)
	if err != nil {
		log.Errorf(d.Ctx, "MeshRefDaoImpl CreateMeshRef [%v] fail,as:%s", meshRef, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *MeshRefDaoImpl) DeleteMeshRef(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "MeshRefDaoImpl DeleteMeshRef [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *MeshRefDaoImpl) DeleteByIDList(idList []uint64) (int64, error) {
	arr := make([]string, 0, len(idList))
	values := make([]interface{}, 0)
	for _, id := range idList {
		arr = append(arr, "?")
		values = append(values, id)
	}

	sql := fmt.Sprintf("`id` in ( %s )", strings.Join(arr, ","))
	affected, err := d.DeleteBy(sql, values...)
	if err != nil {
		log.Errorf(d.Ctx, "MeshRefDaoImpl DeleteByIDList [%v] fail,as:%s", idList, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *MeshRefDaoImpl) UpdateMeshRef(meshRef *release.MeshRef, fields ...string) (int64, error) {
	if meshRef == nil {
		return 0, fmt.Errorf("invalid paramter, meshRef=nil")
	}

	affected, err := d.Update(meshRef.ID, meshRef, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "MeshRefDaoImpl UpdateMeshRef [%v] fail,as:%s", meshRef.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *MeshRefDaoImpl) FindAll() ([]release.MeshRef, error) {
	var meshRefs []release.MeshRef
	err := d.SortListBy(&meshRefs, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "MeshRefDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return meshRefs, nil
}

func (d *MeshRefDaoImpl) ReadMeshRef(meshRefId uint64) (*release.MeshRef, error) {
	var meshRef release.MeshRef
	exist, err := d.FindById(meshRefId, &meshRef)
	if err != nil {
		log.Errorf(d.Ctx, "MeshRefDaoImpl Read [%d] fail,as:%s", meshRefId, err.Error())
		return nil, err
	} else if exist {
		return &meshRef, nil
	} else {
		log.Debugf(d.Ctx, "MeshRefDaoImpl the meshRef id=[%d] not exist", meshRefId)
		return nil, fmt.Errorf("the meshRef [%d] not exist", meshRefId)
	}
}

func (d *MeshRefDaoImpl) ReadMeshRefPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "MeshRefDaoImpl ReadMeshRefPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *MeshRefDaoImpl) ReadMeshRefInfo(meshRefId uint64) (*release.MeshRefInfo, error) {
	var meshRef release.MeshRefInfo
	filter := release.MeshRefFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(meshRefId, &meshRef, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "MeshRefDaoImpl ReadMeshRefInfo [%d] fail,as:%s", meshRefId, err.Error())
		return nil, err
	} else if exist {
		return &meshRef, nil
	} else {
		log.Debugf(d.Ctx, "MeshRefDaoImpl the meshRef id=[%d] not exist", meshRefId)
		return nil, fmt.Errorf("the meshRef [%d] not exist", meshRefId)
	}
}

func (d *MeshRefDaoImpl) FindMeshRefPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "MeshRefDaoImpl FindMeshRefPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *MeshRefDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]release.MeshRefInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "MeshRefDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *MeshRefDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]release.MeshRefInfo, error) {
	records := make([]release.MeshRefInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "MeshRefDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *MeshRefDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*release.MeshRefInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(release.MeshRefInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "MeshRefDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "MeshRefDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *MeshRefDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindMeshRefPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *MeshRefDaoImpl) ReadByFilter(filter information.IFilter) ([]release.MeshRef, error) {
	records := make([]release.MeshRef, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "MeshRefDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *MeshRefDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *MeshRefDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *MeshRefDaoImpl) Entity() information.IRecord {
	return new(release.MeshRef)
}

func (d *MeshRefDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(release.MeshRefFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
