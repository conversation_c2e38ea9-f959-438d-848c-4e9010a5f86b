package release

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/release"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for release.Plan
type PlanDaoImpl struct {
	database.DaoImpl
}

// new a data access object for plan
func NewPlanDao(session *xorm.Session, ctx context.Context) (*PlanDaoImpl, error) {
	planDao := PlanDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(release.Plan)
	if err := planDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	planDao.NewSlice = func() interface{} {
		slice := make([]release.Plan, 0)
		return &slice
	}

	return &planDao, nil
}

func (d *PlanDaoImpl) CreatePlan(plan *release.Plan) (int64, error) {
	affected, err := d.Create(plan)
	if err != nil {
		log.Errorf(d.Ctx, "PlanDaoImpl CreatePlan [%v] fail,as:%s", plan, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PlanDaoImpl) DeletePlan(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "PlanDaoImpl DeletePlan [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PlanDaoImpl) UpdatePlan(plan *release.Plan, fields ...string) (int64, error) {
	if plan == nil {
		return 0, fmt.Errorf("invalid paramter, plan=nil")
	}

	affected, err := d.Update(plan.ID, plan, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "PlanDaoImpl UpdatePlan [%v] fail,as:%s", plan.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *PlanDaoImpl) FindAll() ([]release.Plan, error) {
	var plans []release.Plan
	err := d.SortListBy(&plans, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "PlanDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return plans, nil
}

func (d *PlanDaoImpl) ReadPlan(planId uint64) (*release.Plan, error) {
	var plan release.Plan
	exist, err := d.FindById(planId, &plan)
	if err != nil {
		log.Errorf(d.Ctx, "PlanDaoImpl Read [%d] fail,as:%s", planId, err.Error())
		return nil, err
	} else if exist {
		return &plan, nil
	} else {
		log.Debugf(d.Ctx, "PlanDaoImpl the plan id=[%d] not exist", planId)
		return nil, fmt.Errorf("the plan [%d] not exist", planId)
	}
}

func (d *PlanDaoImpl) ReadPlanPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "PlanDaoImpl ReadPlanPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

//func (d *PlanDaoImpl) ReadInfoPageByFilter(filter *release.PlanFilter) (*information.PageData, error) {
//	conditions := filter.GetJoinCondition()
//	query, args := filter.ToAliasSql()
//
//	records := make([]release.PlanInfo, 0)
//	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
//	if log.IfError(err, "PlanDaoImpl ReadInfoPageByFilter") {
//		return nil, err
//	} else {
//		return page, err
//	}
//}

//	func (d *PlanDaoImpl) ReadInfoListByFilter(filter *release.PlanFilter) ([]release.PlanInfo, error) {
//		conditions := filter.GetJoinCondition()
//		query, args := filter.ToAliasSql()
//
//		records := make([]release.PlanInfo, 0)
//		err := d.JoinQuery(&records, conditions, filter.GetOrderBy(true), query, args...)
//		if log.IfError(err, "PlanDaoImpl ReadInfoPageByFilter") {
//			return nil, err
//		} else {
//			return records, err
//		}
//	}
func (d *PlanDaoImpl) ReadPlanInfo(planId uint64) (*release.PlanInfo, error) {
	var plan release.PlanInfo
	filter := release.PlanFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(planId, &plan, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "PlanDaoImpl ReadPlanInfo [%d] fail,as:%s", planId, err.Error())
		return nil, err
	} else if exist {
		return &plan, nil
	} else {
		log.Debugf(d.Ctx, "PlanDaoImpl the plan id=[%d] not exist", planId)
		return nil, fmt.Errorf("the plan [%d] not exist", planId)
	}
}

func (d *PlanDaoImpl) FindPlanPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "PlanDaoImpl FindPlanPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *PlanDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]release.PlanInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "PlanDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *PlanDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]release.PlanInfo, error) {
	records := make([]release.PlanInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "PlanDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *PlanDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*release.PlanInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(release.PlanInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "PlanDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "PlanDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *PlanDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindPlanPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *PlanDaoImpl) ReadByFilter(filter information.IFilter) ([]release.Plan, error) {
	records := make([]release.Plan, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "PlanDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *PlanDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *PlanDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *PlanDaoImpl) Entity() information.IRecord {
	return new(release.Plan)
}

func (d *PlanDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(release.PlanFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
