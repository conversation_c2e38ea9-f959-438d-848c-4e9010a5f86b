package release

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/release"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for release.DatabaseRef
type DatabaseRefDaoImpl struct {
	database.DaoImpl
}

// new a data access object for databaseRef
func NewDatabaseRefDao(session *xorm.Session, ctx context.Context) (*DatabaseRefDaoImpl, error) {
	databaseRefDao := DatabaseRefDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(release.DatabaseRef)
	if err := databaseRefDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	databaseRefDao.NewSlice = func() interface{} {
		slice := make([]release.DatabaseRef, 0)
		return &slice
	}

	return &databaseRefDao, nil
}

func (d *DatabaseRefDaoImpl) CreateDatabaseRef(databaseRef *release.DatabaseRef) (int64, error) {
	affected, err := d.Create(databaseRef)
	if err != nil {
		log.Errorf(d.Ctx, "DatabaseRefDaoImpl CreateDatabaseRef [%v] fail,as:%s", databaseRef, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DatabaseRefDaoImpl) DeleteDatabaseRef(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "DatabaseRefDaoImpl DeleteDatabaseRef [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DatabaseRefDaoImpl) DeleteByIDList(idList []uint64) (int64, error) {
	arr := make([]string, 0, len(idList))
	values := make([]interface{}, 0)
	for _, id := range idList {
		arr = append(arr, "?")
		values = append(values, id)
	}

	sql := fmt.Sprintf("`id` in ( %s )", strings.Join(arr, ","))
	affected, err := d.DeleteBy(sql, values...)
	if err != nil {
		log.Errorf(d.Ctx, "DatabaseRefDaoImpl DeleteByIDList [%v] fail,as:%s", idList, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DatabaseRefDaoImpl) UpdateDatabaseRef(databaseRef *release.DatabaseRef, fields ...string) (int64, error) {
	if databaseRef == nil {
		return 0, fmt.Errorf("invalid paramter, databaseRef=nil")
	}

	affected, err := d.Update(databaseRef.ID, databaseRef, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "DatabaseRefDaoImpl UpdateDatabaseRef [%v] fail,as:%s", databaseRef.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DatabaseRefDaoImpl) FindAll() ([]release.DatabaseRef, error) {
	var databaseRefs []release.DatabaseRef
	err := d.SortListBy(&databaseRefs, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "DatabaseRefDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return databaseRefs, nil
}

func (d *DatabaseRefDaoImpl) ReadDatabaseRef(databaseRefId uint64) (*release.DatabaseRef, error) {
	var databaseRef release.DatabaseRef
	exist, err := d.FindById(databaseRefId, &databaseRef)
	if err != nil {
		log.Errorf(d.Ctx, "DatabaseRefDaoImpl Read [%d] fail,as:%s", databaseRefId, err.Error())
		return nil, err
	} else if exist {
		return &databaseRef, nil
	} else {
		log.Debugf(d.Ctx, "DatabaseRefDaoImpl the databaseRef id=[%d] not exist", databaseRefId)
		return nil, fmt.Errorf("the databaseRef [%d] not exist", databaseRefId)
	}
}

func (d *DatabaseRefDaoImpl) ReadDatabaseRefPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DatabaseRefDaoImpl ReadDatabaseRefPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *DatabaseRefDaoImpl) ReadDatabaseRefInfo(databaseRefId uint64) (*release.DatabaseRefInfo, error) {
	var databaseRef release.DatabaseRefInfo
	filter := release.DatabaseRefFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(databaseRefId, &databaseRef, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "DatabaseRefDaoImpl ReadDatabaseRefInfo [%d] fail,as:%s", databaseRefId, err.Error())
		return nil, err
	} else if exist {
		return &databaseRef, nil
	} else {
		log.Debugf(d.Ctx, "DatabaseRefDaoImpl the databaseRef id=[%d] not exist", databaseRefId)
		return nil, fmt.Errorf("the databaseRef [%d] not exist", databaseRefId)
	}
}

func (d *DatabaseRefDaoImpl) FindDatabaseRefPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DatabaseRefDaoImpl FindDatabaseRefPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DatabaseRefDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]release.DatabaseRefInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "DatabaseRefDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DatabaseRefDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]release.DatabaseRefInfo, error) {
	records := make([]release.DatabaseRefInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "DatabaseRefDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DatabaseRefDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*release.DatabaseRefInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(release.DatabaseRefInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DatabaseRefDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "DatabaseRefDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *DatabaseRefDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindDatabaseRefPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *DatabaseRefDaoImpl) ReadByFilter(filter information.IFilter) ([]release.DatabaseRef, error) {
	records := make([]release.DatabaseRef, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DatabaseRefDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DatabaseRefDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *DatabaseRefDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *DatabaseRefDaoImpl) Entity() information.IRecord {
	return new(release.DatabaseRef)
}

func (d *DatabaseRefDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(release.DatabaseRefFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
