package release

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/release"
)

func TestVersionDaoImpl_CreateVersion(t *testing.T) {
	version := release.Version{}
	daoInst, _ := NewVersionDao(nil, context.TODO())
	id, err := daoInst.CreateVersion(&version)
	if err != nil {
		t.<PERSON>rf("VersionDaoImpl CreateVersion fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("VersionDaoImpl CreateVersion success, id=", id)
	}
}

func TestVersionDaoImpl_UpdateVersion(t *testing.T) {
	version := release.Version{}
	daoInst, _ := NewVersionDao(nil, context.TODO())
	affected, err := daoInst.UpdateVersion(&version)
	if err != nil {
		t.<PERSON>rrorf("VersionDaoImpl UpdateVersion fail,as[%s]", err.<PERSON><PERSON><PERSON>())
	} else {
		t.Log("VersionDaoImpl UpdateVersion success, affected=", affected)
	}
}

func TestVersionDaoImpl_ReadVersionPage(t *testing.T) {
	daoInst, _ := NewVersionDao(nil, context.TODO())
	versions, err := daoInst.ReadVersionPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("VersionDaoImpl ReadVersionPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(versions)
		t.Log("VersionDaoImpl ReadVersionPage success, data=", string(bytes))
	}
}

func TestVersionDaoImpl_FindAll(t *testing.T) {
	daoInst, _ := NewVersionDao(nil, context.TODO())
	versions, err := daoInst.FindAll()
	if err != nil {
		t.Errorf("VersionDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(versions)
		t.Log("VersionDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestVersionDaoImpl_ReadVersion(t *testing.T) {
	daoInst, _ := NewVersionDao(nil, context.TODO())
	version, err := daoInst.ReadVersion(1)
	if err != nil {
		t.Errorf("VersionDaoImpl ReadVersion fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(version)
		t.Log("VersionDaoImpl ReadVersion success, data=", string(bytes))
	}
}

func TestVersionDaoImpl_DeleteVersion(t *testing.T) {
	daoInst, _ := NewVersionDao(nil, context.TODO())
	affected, err := daoInst.DeleteVersion(1)
	if err != nil {
		t.Errorf("VersionDaoImpl DeleteVersion fail,as[%s]", err.Error())
	} else {
		t.Log("VersionDaoImpl DeleteVersion success, affected=", affected)
	}
}
