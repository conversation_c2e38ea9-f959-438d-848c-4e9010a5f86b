package release

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/release"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for release.DosRef
type DosRefDaoImpl struct {
	database.DaoImpl
}

// new a data access object for DosRef
func NewDosRefDao(session *xorm.Session, ctx context.Context) (*DosRefDaoImpl, error) {
	dbRefDao := DosRefDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(release.DosRef)
	if err := dbRefDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	dbRefDao.NewSlice = func() interface{} {
		slice := make([]release.DosRef, 0)
		return &slice
	}

	return &dbRefDao, nil
}

func (d *DosRefDaoImpl) CreateDosRef(DosRef *release.DosRef) (int64, error) {
	affected, err := d.Create(DosRef)
	if err != nil {
		log.Errorf(d.Ctx, "DosRefDaoImpl CreateDosRef [%v] fail,as:%s", DosRef, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DosRefDaoImpl) DeleteDosRef(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "DosRefDaoImpl DeleteDosRef [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DosRefDaoImpl) DeleteByIDList(idList []uint64) (int64, error) {
	arr := make([]string, 0, len(idList))
	values := make([]interface{}, 0)
	for _, id := range idList {
		arr = append(arr, "?")
		values = append(values, id)
	}

	sql := fmt.Sprintf("`id` in ( %s )", strings.Join(arr, ","))
	affected, err := d.DeleteBy(sql, values...)
	if err != nil {
		log.Errorf(d.Ctx, "DosRefDaoImpl DeleteByIDList [%v] fail,as:%s", idList, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DosRefDaoImpl) UpdateDosRef(DosRef *release.DosRef, fields ...string) (int64, error) {
	if DosRef == nil {
		return 0, fmt.Errorf("invalid paramter, DosRef=nil")
	}

	affected, err := d.Update(DosRef.ID, DosRef, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "DosRefDaoImpl UpdateDosRef [%v] fail,as:%s", DosRef.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DosRefDaoImpl) FindAll() ([]release.DosRef, error) {
	var dbRefs []release.DosRef
	err := d.SortListBy(&dbRefs, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "DosRefDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return dbRefs, nil
}

func (d *DosRefDaoImpl) ReadDosRef(dbRefId uint64) (*release.DosRef, error) {
	var DosRef release.DosRef
	exist, err := d.FindById(dbRefId, &DosRef)
	if err != nil {
		log.Errorf(d.Ctx, "DosRefDaoImpl Read [%d] fail,as:%s", dbRefId, err.Error())
		return nil, err
	} else if exist {
		return &DosRef, nil
	} else {
		log.Debugf(d.Ctx, "DosRefDaoImpl the DosRef id=[%d] not exist", dbRefId)
		return nil, fmt.Errorf("the DosRef [%d] not exist", dbRefId)
	}
}

func (d *DosRefDaoImpl) ReadDosRefPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DosRefDaoImpl ReadDosRefPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *DosRefDaoImpl) FindDosRefPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DosRefDaoImpl FindDosRefPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DosRefDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]release.DosRef, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "DosRefDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DosRefDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]release.DosRef, error) {
	records := make([]release.DosRef, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "DosRefDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DosRefDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*release.DosRef, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(release.DosRef)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DosRefDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "DosRefDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *DosRefDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindDosRefPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *DosRefDaoImpl) ReadByFilter(filter information.IFilter) ([]release.DosRef, error) {
	records := make([]release.DosRef, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DosRefDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DosRefDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *DosRefDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *DosRefDaoImpl) Entity() information.IRecord {
	return new(release.DosRef)
}

func (d *DosRefDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(release.DosRefFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
