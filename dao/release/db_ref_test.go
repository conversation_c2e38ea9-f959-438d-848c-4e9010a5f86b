package release

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/release"
)

func TestDbRefDaoImpl_CreateDbRef(t *testing.T) {
	dbRef := release.DbRef{}
	dbRefDao, _ := NewDbRefDao(nil, context.TODO())
	id, err := dbRefDao.CreateDbRef(&dbRef)
	if err != nil {
		t.Errorf("DbRefDaoImpl CreateDbRef fail,as[%s]", err.Error())
	} else {
		t.Log("DbRefDaoImpl CreateDbRef success, id=", id)
	}
}

func TestDbRefDaoImpl_UpdateDbRef(t *testing.T) {
	dbRef := release.DbRef{}
	dbRefDao, _ := NewDbRefDao(nil, context.TODO())
	affected, err := dbRefDao.UpdateDbRef(&dbRef)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("DbRefDaoImpl UpdateDbRef fail,as[%s]", err.Error())
	} else {
		t.Log("DbRefDaoImpl UpdateDbRef success, affected=", affected)
	}
}

func TestDbRefDaoImpl_ReadDbRefPage(t *testing.T) {
	dbRefDao, _ := NewDbRefDao(nil, context.TODO())
	dbRefs, err := dbRefDao.ReadDbRefPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("DbRefDaoImpl ReadDbRefPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbRefs)
		t.Log("DbRefDaoImpl ReadDbRefPage success, data=", string(bytes))
	}
}

func TestDbRefDaoImpl_FindAll(t *testing.T) {
	dbRefDao, _ := NewDbRefDao(nil, context.TODO())
	dbRefs, err := dbRefDao.FindAll()
	if err != nil {
		t.Errorf("DbRefDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbRefs)
		t.Log("DbRefDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestDbRefDaoImpl_ReadDbRef(t *testing.T) {
	dbRefDao, _ := NewDbRefDao(nil, context.TODO())
	dbRef, err := dbRefDao.ReadDbRef(1)
	if err != nil {
		t.Errorf("DbRefDaoImpl ReadDbRef fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbRef)
		t.Log("DbRefDaoImpl ReadDbRef success, data=", string(bytes))
	}
}

func TestDbRefDaoImpl_DeleteDbRef(t *testing.T) {
	dbRefDao, _ := NewDbRefDao(nil, context.TODO())
	affected, err := dbRefDao.DeleteDbRef(1)
	if err != nil {
		t.Errorf("DbRefDaoImpl DeleteDbRef fail,as[%s]", err.Error())
	} else {
		t.Log("DbRefDaoImpl DeleteDbRef success, affected=", affected)
	}
}
