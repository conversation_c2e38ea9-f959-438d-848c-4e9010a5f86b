package release

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/release"
)

func TestDatabaseRefDaoImpl_CreateDatabaseRef(t *testing.T) {
	databaseRef := release.DatabaseRef{}
	databaseRefDao, _ := NewDatabaseRefDao(nil, context.TODO())
	id, err := databaseRefDao.CreateDatabaseRef(&databaseRef)
	if err != nil {
		t.Errorf("DatabaseRefDaoImpl CreateDatabaseRef fail,as[%s]", err.Error())
	} else {
		t.Log("DatabaseRefDaoImpl CreateDatabaseRef success, id=", id)
	}
}

func TestDatabaseRefDaoImpl_UpdateDatabaseRef(t *testing.T) {
	databaseRef := release.DatabaseRef{}
	databaseRefDao, _ := NewDatabaseRefDao(nil, context.TODO())
	affected, err := databaseRefDao.UpdateDatabaseRef(&databaseRef)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("DatabaseRefDaoImpl UpdateDatabaseRef fail,as[%s]", err.Error())
	} else {
		t.Log("DatabaseRefDaoImpl UpdateDatabaseRef success, affected=", affected)
	}
}

func TestDatabaseRefDaoImpl_ReadDatabaseRefPage(t *testing.T) {
	databaseRefDao, _ := NewDatabaseRefDao(nil, context.TODO())
	databaseRefs, err := databaseRefDao.ReadDatabaseRefPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("DatabaseRefDaoImpl ReadDatabaseRefPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(databaseRefs)
		t.Log("DatabaseRefDaoImpl ReadDatabaseRefPage success, data=", string(bytes))
	}
}

func TestDatabaseRefDaoImpl_FindAll(t *testing.T) {
	databaseRefDao, _ := NewDatabaseRefDao(nil, context.TODO())
	databaseRefs, err := databaseRefDao.FindAll()
	if err != nil {
		t.Errorf("DatabaseRefDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(databaseRefs)
		t.Log("DatabaseRefDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestDatabaseRefDaoImpl_ReadDatabaseRef(t *testing.T) {
	databaseRefDao, _ := NewDatabaseRefDao(nil, context.TODO())
	databaseRef, err := databaseRefDao.ReadDatabaseRef(1)
	if err != nil {
		t.Errorf("DatabaseRefDaoImpl ReadDatabaseRef fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(databaseRef)
		t.Log("DatabaseRefDaoImpl ReadDatabaseRef success, data=", string(bytes))
	}
}

func TestDatabaseRefDaoImpl_DeleteDatabaseRef(t *testing.T) {
	databaseRefDao, _ := NewDatabaseRefDao(nil, context.TODO())
	affected, err := databaseRefDao.DeleteDatabaseRef(1)
	if err != nil {
		t.Errorf("DatabaseRefDaoImpl DeleteDatabaseRef fail,as[%s]", err.Error())
	} else {
		t.Log("DatabaseRefDaoImpl DeleteDatabaseRef success, affected=", affected)
	}
}
