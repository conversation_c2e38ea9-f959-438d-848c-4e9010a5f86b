package release

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/release"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for release.TableRef
type TableRefDaoImpl struct {
	database.DaoImpl
}

// new a data access object for tableRef
func NewTableRefDao(session *xorm.Session, ctx context.Context) (*TableRefDaoImpl, error) {
	tableRefDao := TableRefDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(release.TableRef)
	if err := tableRefDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	tableRefDao.NewSlice = func() interface{} {
		slice := make([]release.TableRef, 0)
		return &slice
	}

	return &tableRefDao, nil
}

func (d *TableRefDaoImpl) CreateTableRef(tableRef *release.TableRef) (int64, error) {
	affected, err := d.Create(tableRef)
	if err != nil {
		log.Errorf(d.Ctx, "TableRefDaoImpl CreateTableRef [%v] fail,as:%s", tableRef, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableRefDaoImpl) DeleteTableRef(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "TableRefDaoImpl DeleteTableRef [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableRefDaoImpl) DeleteByIDList(idList []uint64) (int64, error) {
	arr := make([]string, 0, len(idList))
	values := make([]interface{}, 0)
	for _, id := range idList {
		arr = append(arr, "?")
		values = append(values, id)
	}

	sql := fmt.Sprintf("`id` in ( %s )", strings.Join(arr, ","))
	affected, err := d.DeleteBy(sql, values...)
	if err != nil {
		log.Errorf(d.Ctx, "TableRefDaoImpl DeleteByIDList [%v] fail,as:%s", idList, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableRefDaoImpl) UpdateTableRef(tableRef *release.TableRef, fields ...string) (int64, error) {
	if tableRef == nil {
		return 0, fmt.Errorf("invalid paramter, tableRef=nil")
	}

	affected, err := d.Update(tableRef.ID, tableRef, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "TableRefDaoImpl UpdateTableRef [%v] fail,as:%s", tableRef.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableRefDaoImpl) FindAll() ([]release.TableRef, error) {
	var tableRefs []release.TableRef
	err := d.SortListBy(&tableRefs, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "TableRefDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return tableRefs, nil
}

func (d *TableRefDaoImpl) ReadTableRef(tableRefId uint64) (*release.TableRef, error) {
	var tableRef release.TableRef
	exist, err := d.FindById(tableRefId, &tableRef)
	if err != nil {
		log.Errorf(d.Ctx, "TableRefDaoImpl Read [%d] fail,as:%s", tableRefId, err.Error())
		return nil, err
	} else if exist {
		return &tableRef, nil
	} else {
		log.Debugf(d.Ctx, "TableRefDaoImpl the tableRef id=[%d] not exist", tableRefId)
		return nil, fmt.Errorf("the tableRef [%d] not exist", tableRefId)
	}
}

func (d *TableRefDaoImpl) ReadTableRefPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "TableRefDaoImpl ReadTableRefPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *TableRefDaoImpl) ReadTableRefInfo(tableRefId uint64) (*release.TableRefInfo, error) {
	var tableRef release.TableRefInfo
	filter := release.TableRefFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(tableRefId, &tableRef, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "TableRefDaoImpl ReadTableRefInfo [%d] fail,as:%s", tableRefId, err.Error())
		return nil, err
	} else if exist {
		return &tableRef, nil
	} else {
		log.Debugf(d.Ctx, "TableRefDaoImpl the tableRef id=[%d] not exist", tableRefId)
		return nil, fmt.Errorf("the tableRef [%d] not exist", tableRefId)
	}
}

func (d *TableRefDaoImpl) FindTableRefPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "TableRefDaoImpl FindTableRefPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *TableRefDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]release.TableRefInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "TableRefDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *TableRefDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]release.TableRefInfo, error) {
	records := make([]release.TableRefInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "TableRefDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *TableRefDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*release.TableRefInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(release.TableRefInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "TableRefDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "TableRefDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *TableRefDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindTableRefPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *TableRefDaoImpl) ReadByFilter(filter information.IFilter) ([]release.TableRef, error) {
	records := make([]release.TableRef, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "TableRefDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *TableRefDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *TableRefDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *TableRefDaoImpl) Entity() information.IRecord {
	return new(release.TableRef)
}

func (d *TableRefDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(release.TableRefFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
