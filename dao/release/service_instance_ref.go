package release

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/release"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for release.ServiceInstanceRef
type ServiceInstanceRefDaoImpl struct {
	database.DaoImpl
}

// new a data access object for serviceInstanceRef
func NewServiceInstanceRefDao(session *xorm.Session, ctx context.Context) (*ServiceInstanceRefDaoImpl, error) {
	serviceInstanceRefDao := ServiceInstanceRefDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(release.ServiceInstanceRef)
	if err := serviceInstanceRefDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	serviceInstanceRefDao.NewSlice = func() interface{} {
		slice := make([]release.ServiceInstanceRef, 0)
		return &slice
	}

	return &serviceInstanceRefDao, nil
}

func (d *ServiceInstanceRefDaoImpl) CreateServiceInstanceRef(serviceInstanceRef *release.ServiceInstanceRef) (int64, error) {
	affected, err := d.Create(serviceInstanceRef)
	if err != nil {
		log.Errorf(d.Ctx, "ServiceInstanceRefDaoImpl CreateServiceInstanceRef [%v] fail,as:%s", serviceInstanceRef, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ServiceInstanceRefDaoImpl) DeleteServiceInstanceRef(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "ServiceInstanceRefDaoImpl DeleteServiceInstanceRef [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ServiceInstanceRefDaoImpl) DeleteByIDList(idList []uint64) (int64, error) {
	arr := make([]string, 0, len(idList))
	values := make([]interface{}, 0)
	for _, id := range idList {
		arr = append(arr, "?")
		values = append(values, id)
	}

	sql := fmt.Sprintf("`id` in ( %s )", strings.Join(arr, ","))
	affected, err := d.DeleteBy(sql, values...)
	if err != nil {
		log.Errorf(d.Ctx, "ServiceInstanceRefDaoImpl DeleteByIDList [%v] fail,as:%s", idList, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ServiceInstanceRefDaoImpl) UpdateServiceInstanceRef(serviceInstanceRef *release.ServiceInstanceRef, fields ...string) (int64, error) {
	if serviceInstanceRef == nil {
		return 0, fmt.Errorf("invalid paramter, serviceInstanceRef=nil")
	}

	affected, err := d.Update(serviceInstanceRef.ID, serviceInstanceRef, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "ServiceInstanceRefDaoImpl UpdateServiceInstanceRef [%v] fail,as:%s", serviceInstanceRef.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *ServiceInstanceRefDaoImpl) FindAll() ([]release.ServiceInstanceRef, error) {
	var serviceInstanceRefs []release.ServiceInstanceRef
	err := d.SortListBy(&serviceInstanceRefs, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "ServiceInstanceRefDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return serviceInstanceRefs, nil
}

func (d *ServiceInstanceRefDaoImpl) ReadServiceInstanceRef(serviceInstanceRefId uint64) (*release.ServiceInstanceRef, error) {
	var serviceInstanceRef release.ServiceInstanceRef
	exist, err := d.FindById(serviceInstanceRefId, &serviceInstanceRef)
	if err != nil {
		log.Errorf(d.Ctx, "ServiceInstanceRefDaoImpl Read [%d] fail,as:%s", serviceInstanceRefId, err.Error())
		return nil, err
	} else if exist {
		return &serviceInstanceRef, nil
	} else {
		log.Debugf(d.Ctx, "ServiceInstanceRefDaoImpl the serviceInstanceRef id=[%d] not exist", serviceInstanceRefId)
		return nil, fmt.Errorf("the serviceInstanceRef [%d] not exist", serviceInstanceRefId)
	}
}

func (d *ServiceInstanceRefDaoImpl) ReadServiceInstanceRefPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "ServiceInstanceRefDaoImpl ReadServiceInstanceRefPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *ServiceInstanceRefDaoImpl) ReadServiceInstanceRefInfo(serviceInstanceRefId uint64) (*release.ServiceInstanceRefInfo, error) {
	var serviceInstanceRef release.ServiceInstanceRefInfo
	filter := release.ServiceInstanceRefFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(serviceInstanceRefId, &serviceInstanceRef, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "ServiceInstanceRefDaoImpl ReadServiceInstanceRefInfo [%d] fail,as:%s", serviceInstanceRefId, err.Error())
		return nil, err
	} else if exist {
		return &serviceInstanceRef, nil
	} else {
		log.Debugf(d.Ctx, "ServiceInstanceRefDaoImpl the serviceInstanceRef id=[%d] not exist", serviceInstanceRefId)
		return nil, fmt.Errorf("the serviceInstanceRef [%d] not exist", serviceInstanceRefId)
	}
}

func (d *ServiceInstanceRefDaoImpl) FindServiceInstanceRefPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "ServiceInstanceRefDaoImpl FindServiceInstanceRefPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *ServiceInstanceRefDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]release.ServiceInstanceRefInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "ServiceInstanceRefDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *ServiceInstanceRefDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]release.ServiceInstanceRefInfo, error) {
	records := make([]release.ServiceInstanceRefInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "ServiceInstanceRefDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *ServiceInstanceRefDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*release.ServiceInstanceRefInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(release.ServiceInstanceRefInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "ServiceInstanceRefDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "ServiceInstanceRefDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *ServiceInstanceRefDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindServiceInstanceRefPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *ServiceInstanceRefDaoImpl) ReadByFilter(filter information.IFilter) ([]release.ServiceInstanceRef, error) {
	records := make([]release.ServiceInstanceRef, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "ServiceInstanceRefDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *ServiceInstanceRefDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *ServiceInstanceRefDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *ServiceInstanceRefDaoImpl) Entity() information.IRecord {
	return new(release.ServiceInstanceRef)
}

func (d *ServiceInstanceRefDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(release.ServiceInstanceRefFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
