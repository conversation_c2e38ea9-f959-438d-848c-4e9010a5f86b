package release

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/release"
)

func TestMeshRefDaoImpl_CreateMeshRef(t *testing.T) {
	meshRef := release.MeshRef{}
	meshRefDao, _ := NewMeshRefDao(nil, context.TODO())
	id, err := meshRefDao.CreateMeshRef(&meshRef)
	if err != nil {
		t.E<PERSON>rf("MeshRefDaoImpl CreateMeshRef fail,as[%s]", err.Error())
	} else {
		t.Log("MeshRefDaoImpl CreateMeshRef success, id=", id)
	}
}

func TestMeshRefDaoImpl_UpdateMeshRef(t *testing.T) {
	meshRef := release.MeshRef{}
	meshRefDao, _ := NewMeshRefDao(nil, context.TODO())
	affected, err := meshRefDao.UpdateMeshRef(&meshRef)
	if err != nil {
		t.<PERSON>rf("MeshRefDaoImpl UpdateMeshRef fail,as[%s]", err.Error())
	} else {
		t.Log("MeshRefDaoImpl UpdateMeshRef success, affected=", affected)
	}
}

func TestMeshRefDaoImpl_ReadMeshRefPage(t *testing.T) {
	meshRefDao, _ := NewMeshRefDao(nil, context.TODO())
	meshRefs, err := meshRefDao.ReadMeshRefPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("MeshRefDaoImpl ReadMeshRefPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(meshRefs)
		t.Log("MeshRefDaoImpl ReadMeshRefPage success, data=", string(bytes))
	}
}

func TestMeshRefDaoImpl_FindAll(t *testing.T) {
	meshRefDao, _ := NewMeshRefDao(nil, context.TODO())
	meshRefs, err := meshRefDao.FindAll()
	if err != nil {
		t.Errorf("MeshRefDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(meshRefs)
		t.Log("MeshRefDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestMeshRefDaoImpl_ReadMeshRef(t *testing.T) {
	meshRefDao, _ := NewMeshRefDao(nil, context.TODO())
	meshRef, err := meshRefDao.ReadMeshRef(1)
	if err != nil {
		t.Errorf("MeshRefDaoImpl ReadMeshRef fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(meshRef)
		t.Log("MeshRefDaoImpl ReadMeshRef success, data=", string(bytes))
	}
}

func TestMeshRefDaoImpl_DeleteMeshRef(t *testing.T) {
	meshRefDao, _ := NewMeshRefDao(nil, context.TODO())
	affected, err := meshRefDao.DeleteMeshRef(1)
	if err != nil {
		t.Errorf("MeshRefDaoImpl DeleteMeshRef fail,as[%s]", err.Error())
	} else {
		t.Log("MeshRefDaoImpl DeleteMeshRef success, affected=", affected)
	}
}
