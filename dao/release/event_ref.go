package release

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/release"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for release.EventRef
type EventRefDaoImpl struct {
	database.DaoImpl
}

// new a data access object for EventRef
func NewEventRefDao(session *xorm.Session, ctx context.Context) (*EventRefDaoImpl, error) {
	eventRefDao := EventRefDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(release.EventRef)
	if err := eventRefDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	eventRefDao.NewSlice = func() interface{} {
		slice := make([]release.EventRef, 0)
		return &slice
	}

	return &eventRefDao, nil
}

func (d *EventRefDaoImpl) CreateEventRef(EventRef *release.EventRef) (int64, error) {
	affected, err := d.Create(EventRef)
	if err != nil {
		log.Errorf(d.Ctx, "EventRefDaoImpl CreateEventRef [%v] fail,as:%s", EventRef, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *EventRefDaoImpl) DeleteEventRef(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "EventRefDaoImpl DeleteEventRef [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *EventRefDaoImpl) DeleteByIDList(idList []uint64) (int64, error) {
	arr := make([]string, 0, len(idList))
	values := make([]interface{}, 0)
	for _, id := range idList {
		arr = append(arr, "?")
		values = append(values, id)
	}

	sql := fmt.Sprintf("`id` in ( %s )", strings.Join(arr, ","))
	affected, err := d.DeleteBy(sql, values...)
	if err != nil {
		log.Errorf(d.Ctx, "EventRefDaoImpl DeleteByIDList [%v] fail,as:%s", idList, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *EventRefDaoImpl) UpdateEventRef(EventRef *release.EventRef, fields ...string) (int64, error) {
	if EventRef == nil {
		return 0, fmt.Errorf("invalid paramter, EventRef=nil")
	}

	affected, err := d.Update(EventRef.ID, EventRef, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "EventRefDaoImpl UpdateEventRef [%v] fail,as:%s", EventRef.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *EventRefDaoImpl) FindAll() ([]release.EventRef, error) {
	var eventRefs []release.EventRef
	err := d.SortListBy(&eventRefs, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "EventRefDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return eventRefs, nil
}

func (d *EventRefDaoImpl) ReadEventRef(eventRefId uint64) (*release.EventRef, error) {
	var EventRef release.EventRef
	exist, err := d.FindById(eventRefId, &EventRef)
	if err != nil {
		log.Errorf(d.Ctx, "EventRefDaoImpl Read [%d] fail,as:%s", eventRefId, err.Error())
		return nil, err
	} else if exist {
		return &EventRef, nil
	} else {
		log.Debugf(d.Ctx, "EventRefDaoImpl the EventRef id=[%d] not exist", eventRefId)
		return nil, fmt.Errorf("the EventRef [%d] not exist", eventRefId)
	}
}

func (d *EventRefDaoImpl) ReadEventRefPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "EventRefDaoImpl ReadEventRefPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *EventRefDaoImpl) FindEventRefPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "EventRefDaoImpl FindEventRefPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *EventRefDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]release.EventRef, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "EventRefDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *EventRefDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]release.EventRef, error) {
	records := make([]release.EventRef, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "EventRefDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *EventRefDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*release.EventRef, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(release.EventRef)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "EventRefDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "EventRefDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *EventRefDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindEventRefPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *EventRefDaoImpl) ReadByFilter(filter information.IFilter) ([]release.EventRef, error) {
	records := make([]release.EventRef, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "EventRefDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *EventRefDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *EventRefDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *EventRefDaoImpl) Entity() information.IRecord {
	return new(release.EventRef)
}

func (d *EventRefDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(release.EventRefFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
