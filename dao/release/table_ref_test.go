package release

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/release"
)

func TestTableRefDaoImpl_CreateTableRef(t *testing.T) {
	tableRef := release.TableRef{}
	tableRefDao, _ := NewTableRefDao(nil, context.TODO())
	id, err := tableRefDao.CreateTableRef(&tableRef)
	if err != nil {
		t.Errorf("TableRefDaoImpl CreateTableRef fail,as[%s]", err.Error())
	} else {
		t.Log("TableRefDaoImpl CreateTableRef success, id=", id)
	}
}

func TestTableRefDaoImpl_UpdateTableRef(t *testing.T) {
	tableRef := release.TableRef{}
	tableRefDao, _ := NewTableRefDao(nil, context.TODO())
	affected, err := tableRefDao.UpdateTableRef(&tableRef)
	if err != nil {
		t.<PERSON>("TableRefDaoImpl UpdateTableRef fail,as[%s]", err.Error())
	} else {
		t.Log("TableRefDaoImpl UpdateTableRef success, affected=", affected)
	}
}

func TestTableRefDaoImpl_ReadTableRefPage(t *testing.T) {
	tableRefDao, _ := NewTableRefDao(nil, context.TODO())
	tableRefs, err := tableRefDao.ReadTableRefPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("TableRefDaoImpl ReadTableRefPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableRefs)
		t.Log("TableRefDaoImpl ReadTableRefPage success, data=", string(bytes))
	}
}

func TestTableRefDaoImpl_FindAll(t *testing.T) {
	tableRefDao, _ := NewTableRefDao(nil, context.TODO())
	tableRefs, err := tableRefDao.FindAll()
	if err != nil {
		t.Errorf("TableRefDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableRefs)
		t.Log("TableRefDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestTableRefDaoImpl_ReadTableRef(t *testing.T) {
	tableRefDao, _ := NewTableRefDao(nil, context.TODO())
	tableRef, err := tableRefDao.ReadTableRef(1)
	if err != nil {
		t.Errorf("TableRefDaoImpl ReadTableRef fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableRef)
		t.Log("TableRefDaoImpl ReadTableRef success, data=", string(bytes))
	}
}

func TestTableRefDaoImpl_DeleteTableRef(t *testing.T) {
	tableRefDao, _ := NewTableRefDao(nil, context.TODO())
	affected, err := tableRefDao.DeleteTableRef(1)
	if err != nil {
		t.Errorf("TableRefDaoImpl DeleteTableRef fail,as[%s]", err.Error())
	} else {
		t.Log("TableRefDaoImpl DeleteTableRef success, affected=", affected)
	}
}
