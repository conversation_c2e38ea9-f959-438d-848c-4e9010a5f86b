package release

import (
	"context"
	"fmt"
	"strings"

	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/release"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for release.Version
type VersionDaoImpl struct {
	database.DaoImpl
}

// new a data access object for version
func NewVersionDao(session *xorm.Session, ctx context.Context) (*VersionDaoImpl, error) {
	daoInst := VersionDaoImpl{
		DaoImpl: database.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(release.Version)
	if err := daoInst.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	daoInst.NewSlice = func() interface{} {
		slice := make([]release.Version, 0)
		return &slice
	}

	return &daoInst, nil
}

func (d *VersionDaoImpl) CreateVersion(version *release.Version) (int64, error) {
	affected, err := d.Create(version)
	if err != nil {
		log.Errorf(d.Ctx, "VersionDaoImpl CreateVersion [%v] fail,as:%s", version, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *VersionDaoImpl) DeleteVersion(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "VersionDaoImpl DeleteVersion [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *VersionDaoImpl) DeleteByIDList(idList []uint64) (int64, error) {
	arr := make([]string, 0, len(idList))
	values := make([]interface{}, 0)
	for _, id := range idList {
		arr = append(arr, "?")
		values = append(values, id)
	}

	sql := fmt.Sprintf("`id` in ( %s )", strings.Join(arr, ","))
	affected, err := d.DeleteBy(sql, values...)
	if err != nil {
		log.Errorf(d.Ctx, "VersionDaoImpl DeleteByIDList [%v] fail,as:%s", idList, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *VersionDaoImpl) UpdateVersion(version *release.Version, fields ...string) (int64, error) {
	if version == nil {
		return 0, fmt.Errorf("invalid paramter, version=nil")
	}

	affected, err := d.Update(version.ID, version, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "VersionDaoImpl UpdateVersion [%v] fail,as:%s", version.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *VersionDaoImpl) FindAll() ([]release.Version, error) {
	var versions []release.Version
	err := d.SortListBy(&versions, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "VersionDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return versions, nil
}

func (d *VersionDaoImpl) ReadVersion(versionId uint64) (*release.Version, error) {
	var version release.Version
	exist, err := d.FindById(versionId, &version)
	if err != nil {
		log.Errorf(d.Ctx, "VersionDaoImpl Read [%d] fail,as:%s", versionId, err.Error())
		return nil, err
	} else if exist {
		return &version, nil
	} else {
		log.Debugf(d.Ctx, "VersionDaoImpl the version id=[%d] not exist", versionId)
		return nil, fmt.Errorf("the version [%d] not exist", versionId)
	}
}

func (d *VersionDaoImpl) ReadVersionPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "VersionDaoImpl ReadVersionPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *VersionDaoImpl) ReadVersionInfo(versionId uint64) (*release.VersionInfo, error) {
	var version release.VersionInfo
	filter := release.VersionFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(versionId, &version, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "VersionDaoImpl ReadVersionInfo [%d] fail,as:%s", versionId, err.Error())
		return nil, err
	} else if exist {
		return &version, nil
	} else {
		log.Debugf(d.Ctx, "VersionDaoImpl the version id=[%d] not exist", versionId)
		return nil, fmt.Errorf("the version [%d] not exist", versionId)
	}
}

func (d *VersionDaoImpl) FindVersionPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "VersionDaoImpl FindVersionPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *VersionDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]release.VersionInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "VersionDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *VersionDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]release.VersionInfo, error) {
	records := make([]release.VersionInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "VersionDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *VersionDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*release.VersionInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(release.VersionInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "VersionDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "VersionDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *VersionDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindVersionPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *VersionDaoImpl) ReadByFilter(filter information.IFilter) ([]release.Version, error) {
	records := make([]release.Version, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "VersionDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *VersionDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *VersionDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *VersionDaoImpl) Entity() information.IRecord {
	return new(release.Version)
}

func (d *VersionDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(release.VersionFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
