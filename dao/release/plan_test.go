package release

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	. "git.platform.io/environment/environment/dao/test"
	"git.platform.io/environment/environment/model/release"
)

func TestPlanDaoImpl_CreatePlan(t *testing.T) {
	plan := release.Plan{}
	planDao, _ := NewPlanDao(nil, context.TODO())
	id, err := planDao.CreatePlan(&plan)
	if err != nil {
		t.<PERSON>("PlanDaoImpl CreatePlan fail,as[%s]", err.Error())
	} else {
		t.Log("PlanDaoImpl CreatePlan success, id=", id)
	}
}

func TestPlanDaoImpl_UpdatePlan(t *testing.T) {
	plan := release.Plan{}
	planDao, _ := NewPlanDao(nil, context.TODO())
	affected, err := planDao.UpdatePlan(&plan)
	if err != nil {
		t.<PERSON>rrorf("PlanDaoImpl UpdatePlan fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("PlanDaoImpl UpdatePlan success, affected=", affected)
	}
}

func TestPlanDaoImpl_ReadPlanPage(t *testing.T) {
	planDao, _ := NewPlanDao(nil, context.TODO())
	plans, err := planDao.ReadPlanPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("PlanDaoImpl ReadPlanPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(plans)
		t.Log("PlanDaoImpl ReadPlanPage success, data=", string(bytes))
	}
}

func TestPlanDaoImpl_FindAll(t *testing.T) {
	planDao, _ := NewPlanDao(nil, context.TODO())
	plans, err := planDao.FindAll()
	if err != nil {
		t.Errorf("PlanDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(plans)
		t.Log("PlanDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestPlanDaoImpl_ReadPlan(t *testing.T) {
	planDao, _ := NewPlanDao(nil, context.TODO())
	plan, err := planDao.ReadPlan(1)
	if err != nil {
		t.Errorf("PlanDaoImpl ReadPlan fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(plan)
		t.Log("PlanDaoImpl ReadPlan success, data=", string(bytes))
	}
}

func TestPlanDaoImpl_DeletePlan(t *testing.T) {
	planDao, _ := NewPlanDao(nil, context.TODO())
	affected, err := planDao.DeletePlan(1)
	if err != nil {
		t.Errorf("PlanDaoImpl DeletePlan fail,as[%s]", err.Error())
	} else {
		t.Log("PlanDaoImpl DeletePlan success, affected=", affected)
	}
}

func TestMain(m *testing.M) {
	Setup()
	code := m.Run()
	Teardown()
	os.Exit(code)
}
