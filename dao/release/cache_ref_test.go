package release

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/release"
)

func TestCacheRefDaoImpl_CreateCacheRef(t *testing.T) {
	cacheRef := release.CacheRef{}
	cacheRefDao, _ := NewCacheRefDao(nil, context.TODO())
	id, err := cacheRefDao.CreateCacheRef(&cacheRef)
	if err != nil {
		t.Errorf("CacheRefDaoImpl CreateCacheRef fail,as[%s]", err.Error())
	} else {
		t.Log("CacheRefDaoImpl CreateCacheRef success, id=", id)
	}
}

func TestCacheRefDaoImpl_UpdateCacheRef(t *testing.T) {
	cacheRef := release.CacheRef{}
	cacheRefDao, _ := NewCacheRefDao(nil, context.TODO())
	affected, err := cacheRefDao.UpdateCacheRef(&cacheRef)
	if err != nil {
		t.<PERSON>("CacheRefDaoImpl UpdateCacheRef fail,as[%s]", err.Error())
	} else {
		t.Log("CacheRefDaoImpl UpdateCacheRef success, affected=", affected)
	}
}

func TestCacheRefDaoImpl_ReadCacheRefPage(t *testing.T) {
	cacheRefDao, _ := NewCacheRefDao(nil, context.TODO())
	cacheRefs, err := cacheRefDao.ReadCacheRefPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("CacheRefDaoImpl ReadCacheRefPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(cacheRefs)
		t.Log("CacheRefDaoImpl ReadCacheRefPage success, data=", string(bytes))
	}
}

func TestCacheRefDaoImpl_FindAll(t *testing.T) {
	cacheRefDao, _ := NewCacheRefDao(nil, context.TODO())
	cacheRefs, err := cacheRefDao.FindAll()
	if err != nil {
		t.Errorf("CacheRefDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(cacheRefs)
		t.Log("CacheRefDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestCacheRefDaoImpl_ReadCacheRef(t *testing.T) {
	cacheRefDao, _ := NewCacheRefDao(nil, context.TODO())
	cacheRef, err := cacheRefDao.ReadCacheRef(1)
	if err != nil {
		t.Errorf("CacheRefDaoImpl ReadCacheRef fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(cacheRef)
		t.Log("CacheRefDaoImpl ReadCacheRef success, data=", string(bytes))
	}
}

func TestCacheRefDaoImpl_DeleteCacheRef(t *testing.T) {
	cacheRefDao, _ := NewCacheRefDao(nil, context.TODO())
	affected, err := cacheRefDao.DeleteCacheRef(1)
	if err != nil {
		t.Errorf("CacheRefDaoImpl DeleteCacheRef fail,as[%s]", err.Error())
	} else {
		t.Log("CacheRefDaoImpl DeleteCacheRef success, affected=", affected)
	}
}
