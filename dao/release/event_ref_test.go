package release

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/release"
)

func TestEventRefDaoImpl_CreateEventRef(t *testing.T) {
	eventRef := release.EventRef{}
	eventRefDao, _ := NewEventRefDao(nil, context.TODO())
	id, err := eventRefDao.CreateEventRef(&eventRef)
	if err != nil {
		t.Errorf("EventRefDaoImpl CreateEventRef fail,as[%s]", err.Error())
	} else {
		t.Log("EventRefDaoImpl CreateEventRef success, id=", id)
	}
}

func TestEventRefDaoImpl_UpdateEventRef(t *testing.T) {
	eventRef := release.EventRef{}
	eventRefDao, _ := NewEventRefDao(nil, context.TODO())
	affected, err := eventRefDao.UpdateEventRef(&eventRef)
	if err != nil {
		t.<PERSON>rf("EventRefDaoImpl UpdateEventRef fail,as[%s]", err.Error())
	} else {
		t.Log("EventRefDaoImpl UpdateEventRef success, affected=", affected)
	}
}

func TestEventRefDaoImpl_ReadEventRefPage(t *testing.T) {
	eventRefDao, _ := NewEventRefDao(nil, context.TODO())
	eventRefs, err := eventRefDao.ReadEventRefPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("EventRefDaoImpl ReadEventRefPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(eventRefs)
		t.Log("EventRefDaoImpl ReadEventRefPage success, data=", string(bytes))
	}
}

func TestEventRefDaoImpl_FindAll(t *testing.T) {
	eventRefDao, _ := NewEventRefDao(nil, context.TODO())
	eventRefs, err := eventRefDao.FindAll()
	if err != nil {
		t.Errorf("EventRefDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(eventRefs)
		t.Log("EventRefDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestEventRefDaoImpl_ReadEventRef(t *testing.T) {
	eventRefDao, _ := NewEventRefDao(nil, context.TODO())
	eventRef, err := eventRefDao.ReadEventRef(1)
	if err != nil {
		t.Errorf("EventRefDaoImpl ReadEventRef fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(eventRef)
		t.Log("EventRefDaoImpl ReadEventRef success, data=", string(bytes))
	}
}

func TestEventRefDaoImpl_DeleteEventRef(t *testing.T) {
	eventRefDao, _ := NewEventRefDao(nil, context.TODO())
	affected, err := eventRefDao.DeleteEventRef(1)
	if err != nil {
		t.Errorf("EventRefDaoImpl DeleteEventRef fail,as[%s]", err.Error())
	} else {
		t.Log("EventRefDaoImpl DeleteEventRef success, affected=", affected)
	}
}
