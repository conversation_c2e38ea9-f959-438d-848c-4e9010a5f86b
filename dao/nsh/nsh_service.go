package nsh

import (
	"fmt"
	"time"

	model "git.platform.io/environment/environment/model/nsh"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/information"

	"git.platform.io/environment/environment/database"
	"git.platform.io/resource/common/log"
)

const (
	NshSyncService = "majmad"
)

// Data Access Object for ip
type NshServiceDaoImpl struct {
	database.DaoImpl
}

func NewNshServiceDao(request app.IRequest) (*NshServiceDaoImpl, error) {
	dao := NshServiceDaoImpl{}
	m := new(model.NshService)
	if err := dao.InitWithRequest(request, m.TableName(), m); err != nil {
		return nil, err
	}
	dao.NewSlice = func() interface{} {
		var slice []model.NshService
		return &slice
	}

	return &dao, nil
}

func (d *NshServiceDaoImpl) NewNshService(name, description, status, _type, instanceNum, version, logLevel, cpuRequest, cpuLimit, memoryRequest, memoryLimit, nodeIP string, args ...string) error {
	user := "system"
	if len(args) > 0 {
		user = args[0]
	}

	now := time.Now()
	newNshService := &model.NshService{
		Description:   description,
		Status:        status,
		Type:          _type,
		InstanceNum:   instanceNum,
		Version:       version,
		LogLevel:      logLevel,
		CPURequest:    cpuRequest,
		CPULimit:      cpuLimit,
		MemoryRequest: memoryRequest,
		MemoryLimit:   memoryLimit,
		NodeIP:        nodeIP,
		CreatedAt:     now,
		Creator:       user,
		UpdatedAt:     now,
		UpdaterId:     user,
	}
	_, err := d.Create(newNshService)
	if err != nil {
		return err
	} else {
		return nil
	}
}

func (d *NshServiceDaoImpl) CreateNshService(NshService *model.NshService) (int64, error) {
	affected, err := d.Create(NshService)
	if err != nil {
		log.Errorf(d.Ctx, "NshServiceDaoImpl CreateNshService [%v] fail,as:%s", NshService, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *NshServiceDaoImpl) ModifyNshServices(creates []model.NshService, removes []model.NshService) (string, error) {
	// start session
	alert := ""
	var err error
	session := d.NewSession()
	if session == nil {
		err = fmt.Errorf("NshServiceDaoImpl Create Xorm Session fail, get empty return")
		log.Error(d.Ctx, err.Error())
		return alert, err
	}
	defer func() {
		if err != nil {
			err = session.Rollback()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		} else {
			err = session.Commit()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		}
	}()

	// get create or update records
	ins := make([]interface{}, 0)
	var args []interface{}
	where := "1 = 1"
	for _, relation := range creates {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and cluster_name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
			if len(relation.Type) > 0 {
				where += " and `type` = ?"
				args = append(args, relation.Type)
			}
			if len(relation.InstanceNum) > 0 {
				where += " and instance_num = ?"
				args = append(args, relation.InstanceNum)
			}
			if len(relation.Version) > 0 {
				where += " and version = ?"
				args = append(args, relation.Version)
			}
			if len(relation.LogLevel) > 0 {
				where += " and log_level = ?"
				args = append(args, relation.LogLevel)
			}
			if len(relation.CPURequest) > 0 {
				where += " and cpu_request = ?"
				args = append(args, relation.CPURequest)
			}
			if len(relation.CPULimit) > 0 {
				where += " and cpu_limit = ?"
				args = append(args, relation.CPULimit)
			}
			if len(relation.MemoryRequest) > 0 {
				where += " and memory_request = ?"
				args = append(args, relation.MemoryRequest)
			}
			if len(relation.MemoryLimit) > 0 {
				where += " and memory_limit = ?"
				args = append(args, relation.MemoryLimit)
			}
			if len(relation.NodeIP) > 0 {
				where += " and node_ip = ?"
				args = append(args, relation.NodeIP)
			}
		}
		var b bool
		b, err = session.NoAutoCondition(true).Where(where, args...).Get(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
		if b { // update
			_, err = session.NoAutoCondition(true).Cols("cluster_name", "description", "status", "updated_at", "updater_id").Where(where, args...).Update(relation)
			if err != nil {
				log.Error(d.Ctx, err.Error())
				return alert, err
			}
			continue
		}
		if relation.Id > 0 {
			// ignore
			continue
		}

		ins = append(ins, relation)
	}
	if len(ins) > 0 {
		_, err = session.Insert(ins...)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}
	// remove
	for _, relation := range removes {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and cluster_name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
			if len(relation.InstanceNum) > 0 {
				where += " and instance_num = ?"
				args = append(args, relation.InstanceNum)
			}
			if len(relation.Version) > 0 {
				where += " and version = ?"
				args = append(args, relation.Version)
			}
			if len(relation.Type) > 0 {
				where += " and `type` = ?"
				args = append(args, relation.Type)
			}
			if len(relation.LogLevel) > 0 {
				where += " and log_level = ?"
				args = append(args, relation.LogLevel)
			}
			if len(relation.Type) > 0 {
				where += " and `type` = ?"
				args = append(args, relation.Type)
			}
			if len(relation.MemoryRequest) > 0 {
				where += " and cpu_request = ?"
				args = append(args, relation.CPURequest)
			}
			if len(relation.MemoryLimit) > 0 {
				where += " and cpu_limit = ?"
				args = append(args, relation.CPULimit)
			}
			if len(relation.NodeIP) > 0 {
				where += " and node_ip = ?"
				args = append(args, relation.NodeIP)
			}
		}
		_, err := session.NoAutoCondition(true).Where(where, args...).Delete(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}

	return alert, err
}

func (d *NshServiceDaoImpl) CreateNshServiceByMap(NshService *map[string]interface{}) (int64, error) {
	affected, err := d.CreateWithMap(NshService)
	if err != nil {
		log.Errorf(d.Ctx, "NshServiceDaoImpl CreateNshServiceByMap [%v] fail,as:%s", NshService, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *NshServiceDaoImpl) DeleteNshService(NshService *model.NshService) (int64, error) {
	affected, err := d.DeleteById(NshService.Id)
	if err != nil {
		log.Errorf(d.Ctx, "NshServiceDaoImpl DeleteNshService [%v] fail,as:%s", NshService.Id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *NshServiceDaoImpl) UpdateNshService(id uint64, bean *model.NshService, fields ...string) (int64, error) {
	if bean == nil {
		return 0, fmt.Errorf("invalid paramter, NshService=nil")
	}
	if id == 0 {
		return 0, fmt.Errorf("invalid paramter for zero id")
	}
	affected, err := d.Update(id, bean, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "NshServiceDaoImpl UpdateNshService [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *NshServiceDaoImpl) FindAll() (*[]model.NshService, error) {
	NshServices := make([]model.NshService, 0)
	err := d.ListAll(&NshServices)
	if err != nil {
		log.Error(d.Ctx, "NshServiceDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return &NshServices, nil
}

func (d *NshServiceDaoImpl) ReadNshService(NshServiceId uint64) (*model.NshService, error) {
	var NshService model.NshService
	exist, err := d.FindById(NshServiceId, &NshService)
	if err != nil {
		log.Errorf(d.Ctx, "NshServiceDaoImpl ReadNshService [%d] fail,as:%s", NshServiceId, err.Error())
		return nil, err
	} else if exist {
		return &NshService, nil
	} else {
		log.Debugf(d.Ctx, "NshServiceDaoImpl the NshService id=[%d] not exist", NshServiceId)
		return nil, fmt.Errorf("the NshService [%d] not exist", NshServiceId)
	}
}

func (d *NshServiceDaoImpl) ReadNshServicePage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "NshServiceDaoImpl ReadNshServicePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *NshServiceDaoImpl) ReadNshServices(query interface{}, args ...interface{}) (*[]model.NshService, error) {
	result := make([]model.NshService, 0)
	err := d.ListBy(&result, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "NshServiceDaoImpl ReadNshServicePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return &result, nil
	}
}

func (d *NshServiceDaoImpl) ReadNshServiceWithServiceInstanceIdAndApplication(instanceId, appId uint64) (*model.NshService, error) {
	nshCluster, exist, err := d.CheckNshServiceWithServiceInstanceIdAndApplication(instanceId, appId)
	if err != nil {
		log.Errorf(d.Ctx, "NshServiceDaoImpl ReadNshService[%d][%d] fail,as:%s", instanceId, appId, err.Error())
		return nil, err
	} else if exist {
		return nshCluster, nil
	} else {
		log.Debugf(d.Ctx, "NshServiceDaoImpl the NshService id=[%d][%d] not exist", instanceId, appId)
		return nil, fmt.Errorf("the NshService [%d][%d] not exist", instanceId, appId)
	}
}

func (d *NshServiceDaoImpl) CheckNshServiceWithServiceInstanceIdAndApplication(instanceId, appId uint64) (*model.NshService, bool, error) {
	var nshService model.NshService
	exist, err := d.Find(&nshService, "instance_ref_id = ? and application_id = ?", instanceId, appId)
	return &nshService, exist, err
}
