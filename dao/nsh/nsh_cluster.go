package nsh

import (
	"fmt"
	"time"

	model "git.platform.io/environment/environment/model/nsh"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/information"

	"git.platform.io/environment/environment/database"
	"git.platform.io/resource/common/log"
)

// Data Access Object for ip
type NshClusterDaoImpl struct {
	database.DaoImpl
}

func NewNshClusterDao(request app.IRequest) (*NshClusterDaoImpl, error) {
	dao := NshClusterDaoImpl{}
	m := new(model.NshCluster)
	if err := dao.InitWithRequest(request, m.TableName(), m); err != nil {
		return nil, err
	}
	dao.NewSlice = func() interface{} {
		var slice []model.NshCluster
		return &slice
	}

	return &dao, nil
}

func (d *NshClusterDaoImpl) NewNshCluster(name, description, status string, args ...string) error {
	user := "system"
	if len(args) > 0 {
		user = args[0]
	}

	now := time.Now()
	newNshCluster := &model.NshCluster{
		Name:        name,
		Description: description,
		Status:      status,
		CreatedAt:   now,
		CreatorId:   user,
		UpdatedAt:   now,
		UpdaterId:   user,
	}
	_, err := d.Create(newNshCluster)
	if err != nil {
		return err
	} else {
		return nil
	}
}

func (d *NshClusterDaoImpl) ModifyNshClusters(creates []model.NshCluster, removes []model.NshCluster) (string, error) {
	// start session
	alert := ""
	var err error
	session := d.NewSession()
	if session == nil {
		err = fmt.Errorf("NshClusterDaoImpl Create Xorm Session fail, get empty return")
		log.Error(d.Ctx, err.Error())
		return alert, err
	}
	defer func() {
		if err != nil {
			err = session.Rollback()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		} else {
			err = session.Commit()
			if err != nil {
				log.Error(d.Ctx, err.Error())
			}
		}
	}()

	// get create or update records
	ins := make([]interface{}, 0)
	var args []interface{}
	where := "1 = 1"
	for _, relation := range creates {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
		}
		var b bool
		b, err = session.NoAutoCondition(true).Where(where, args...).Get(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
		if b { // update
			_, err = session.NoAutoCondition(true).Cols("name", "description", "status", "updated_at", "updater_id").Where(where, args...).Update(relation)
			if err != nil {
				log.Error(d.Ctx, err.Error())
				return alert, err
			}
			continue
		}
		if relation.Id > 0 {
			// ignore
			continue
		}

		ins = append(ins, relation)
	}
	if len(ins) > 0 {
		_, err = session.Insert(ins...)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}
	// remove
	for _, relation := range removes {
		where = "1 = 1"
		args = make([]interface{}, 0)
		if relation.Id > 0 {
			where += " and id = ?"
			args = append(args, relation.Id)
		} else {
			if len(relation.Name) > 0 {
				where += " and name = ?"
				args = append(args, relation.Name)
			}
			if len(relation.Description) > 0 {
				where += " and description = ?"
				args = append(args, relation.Description)
			}
			if len(relation.Status) > 0 {
				where += " and status = ?"
				args = append(args, relation.Status)
			}
		}
		_, err := session.NoAutoCondition(true).Where(where, args...).Delete(&relation)
		if err != nil {
			log.Error(d.Ctx, err.Error())
			return alert, err
		}
	}

	return alert, err
}

func (d *NshClusterDaoImpl) CreateNshClusterByMap(NshCluster *map[string]interface{}) (int64, error) {
	affected, err := d.CreateWithMap(NshCluster)
	if err != nil {
		log.Errorf(d.Ctx, "NshClusterDaoImpl CreateNshClusterByMap [%v] fail,as:%s", NshCluster, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *NshClusterDaoImpl) DeleteNshCluster(NshCluster *model.NshCluster) (int64, error) {
	affected, err := d.DeleteById(NshCluster.Id)
	if err != nil {
		log.Errorf(d.Ctx, "NshClusterDaoImpl DeleteNshCluster [%v] fail,as:%s", NshCluster.Id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *NshClusterDaoImpl) UpdateNshCluster(id uint64, bean *model.NshCluster, fields ...string) (int64, error) {
	if bean == nil {
		return 0, fmt.Errorf("invalid paramter, NshCluster=nil")
	}
	if id == 0 {
		return 0, fmt.Errorf("invalid paramter for zero id")
	}
	affected, err := d.Update(id, bean, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "NshClusterDaoImpl UpdateNshCluster [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *NshClusterDaoImpl) FindAll() (*[]model.NshCluster, error) {
	NshClusters := make([]model.NshCluster, 0)
	err := d.ListAll(&NshClusters)
	if err != nil {
		log.Error(d.Ctx, "NshClusterDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return &NshClusters, nil
}

func (d *NshClusterDaoImpl) CheckNshClusterWithName(name string) (*model.NshCluster, bool, error) {
	var nshCluster model.NshCluster
	exist, err := d.Find(&nshCluster, "name = ?", name)
	return &nshCluster, exist, err
}

func (d *NshClusterDaoImpl) ReadNshClusterWithName(name string) (*model.NshCluster, error) {
	nshCluster, exist, err := d.CheckNshClusterWithName(name)
	if err != nil {
		log.Errorf(d.Ctx, "NshClusterDaoImpl ReadNshCluster [%s] fail,as:%s", name, err.Error())
		return nil, err
	} else if exist {
		return nshCluster, nil
	} else {
		log.Debugf(d.Ctx, "NshClusterDaoImpl the NshCluster id=[%s] not exist", name)
		return nil, fmt.Errorf("the NshCluster [%s] not exist", name)
	}
}

func (d *NshClusterDaoImpl) ReadNshCluster(NshClusterId uint64) (*model.NshCluster, error) {
	var NshCluster model.NshCluster
	exist, err := d.FindById(NshClusterId, &NshCluster)
	if err != nil {
		log.Errorf(d.Ctx, "NshClusterDaoImpl ReadNshCluster [%d] fail,as:%s", NshClusterId, err.Error())
		return nil, err
	} else if exist {
		return &NshCluster, nil
	} else {
		log.Debugf(d.Ctx, "NshClusterDaoImpl the NshCluster id=[%d] not exist", NshClusterId)
		return nil, fmt.Errorf("the NshCluster [%d] not exist", NshClusterId)
	}
}

func (d *NshClusterDaoImpl) ReadNshClusterPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "NshClusterDaoImpl ReadNshClusterPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *NshClusterDaoImpl) ReadByFilter(filter *model.NshClusterFilter) ([]model.NshCluster, error) {
	records := make([]model.NshCluster, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "PlanDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *NshClusterDaoImpl) FindNshClusterPage(filter *model.NshClusterFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "PlanDaoImpl FindPlanPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *NshClusterDaoImpl) ReadNshClusters(query interface{}, args ...interface{}) (*[]model.NshCluster, error) {
	result := make([]model.NshCluster, 0)
	err := d.ListBy(&result, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "NshClusterDaoImpl ReadNshClusterPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return &result, nil
	}
}
