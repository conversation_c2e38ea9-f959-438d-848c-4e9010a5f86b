package database

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/database"
)

func TestTableInstanceVersionDaoImpl_CreateTableInstanceVersion(t *testing.T) {
	tableInstanceVersion := database.TableInstanceVersion{}
	tableInstanceVersionDao, _ := NewTableInstanceVersionDao(nil, context.TODO())
	id, err := tableInstanceVersionDao.CreateTableInstanceVersion(&tableInstanceVersion)
	if err != nil {
		t.Errorf("TableInstanceVersionDaoImpl CreateTableInstanceVersion fail,as[%s]", err.Error())
	} else {
		t.Log("TableInstanceVersionDaoImpl CreateTableInstanceVersion success, id=", id)
	}
}

func TestTableInstanceVersionDaoImpl_UpdateTableInstanceVersion(t *testing.T) {
	tableInstanceVersion := database.TableInstanceVersion{}
	tableInstanceVersionDao, _ := NewTableInstanceVersionDao(nil, context.TODO())
	affected, err := tableInstanceVersionDao.UpdateTableInstanceVersion(&tableInstanceVersion)
	if err != nil {
		t.Errorf("TableInstanceVersionDaoImpl UpdateTableInstanceVersion fail,as[%s]", err.Error())
	} else {
		t.Log("TableInstanceVersionDaoImpl UpdateTableInstanceVersion success, affected=", affected)
	}
}

func TestTableInstanceVersionDaoImpl_ReadTableInstanceVersionPage(t *testing.T) {
	tableInstanceVersionDao, _ := NewTableInstanceVersionDao(nil, context.TODO())
	tableInstanceVersions, err := tableInstanceVersionDao.ReadTableInstanceVersionPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("TableInstanceVersionDaoImpl ReadTableInstanceVersionPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableInstanceVersions)
		t.Log("TableInstanceVersionDaoImpl ReadTableInstanceVersionPage success, data=", string(bytes))
	}
}

func TestTableInstanceVersionDaoImpl_FindAll(t *testing.T) {
	tableInstanceVersionDao, _ := NewTableInstanceVersionDao(nil, context.TODO())
	tableInstanceVersions, err := tableInstanceVersionDao.FindAll()
	if err != nil {
		t.Errorf("TableInstanceVersionDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableInstanceVersions)
		t.Log("TableInstanceVersionDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestTableInstanceVersionDaoImpl_ReadTableInstanceVersion(t *testing.T) {
	tableInstanceVersionDao, _ := NewTableInstanceVersionDao(nil, context.TODO())
	tableInstanceVersion, err := tableInstanceVersionDao.ReadTableInstanceVersion(1)
	if err != nil {
		t.Errorf("TableInstanceVersionDaoImpl ReadTableInstanceVersion fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableInstanceVersion)
		t.Log("TableInstanceVersionDaoImpl ReadTableInstanceVersion success, data=", string(bytes))
	}
}

func TestTableInstanceVersionDaoImpl_DeleteTableInstanceVersion(t *testing.T) {
	tableInstanceVersionDao, _ := NewTableInstanceVersionDao(nil, context.TODO())
	affected, err := tableInstanceVersionDao.DeleteTableInstanceVersion(1)
	if err != nil {
		t.Errorf("TableInstanceVersionDaoImpl DeleteTableInstanceVersion fail,as[%s]", err.Error())
	} else {
		t.Log("TableInstanceVersionDaoImpl DeleteTableInstanceVersion success, affected=", affected)
	}
}
