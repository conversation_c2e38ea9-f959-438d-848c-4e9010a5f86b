package database

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	. "git.platform.io/environment/environment/dao/test"
	"git.platform.io/environment/environment/model/database"
)

func TestDBInstanceReviseDaoImpl_CreateDBInstanceRevise(t *testing.T) {
	dbInstanceRevise := database.DBInstanceRevise{}
	dbInstanceReviseDao, _ := NewDBInstanceReviseDao(nil, context.TODO())
	id, err := dbInstanceReviseDao.CreateDBInstanceRevise(&dbInstanceRevise)
	if err != nil {
		t.Errorf("DBInstanceReviseDaoImpl CreateDBInstanceRevise fail,as[%s]", err.Error())
	} else {
		t.Log("DBInstanceReviseDaoImpl CreateDBInstanceRevise success, id=", id)
	}
}

func TestDBInstanceReviseDaoImpl_UpdateDBInstanceRevise(t *testing.T) {
	dbInstanceRevise := database.DBInstanceRevise{}
	dbInstanceReviseDao, _ := NewDBInstanceReviseDao(nil, context.TODO())
	affected, err := dbInstanceReviseDao.UpdateDBInstanceRevise(&dbInstanceRevise)
	if err != nil {
		t.Errorf("DBInstanceReviseDaoImpl UpdateDBInstanceRevise fail,as[%s]", err.Error())
	} else {
		t.Log("DBInstanceReviseDaoImpl UpdateDBInstanceRevise success, affected=", affected)
	}
}

func TestDBInstanceReviseDaoImpl_ReadDBInstanceRevisePage(t *testing.T) {
	dbInstanceReviseDao, _ := NewDBInstanceReviseDao(nil, context.TODO())
	dbInstanceRevises, err := dbInstanceReviseDao.ReadDBInstanceRevisePage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("DBInstanceReviseDaoImpl ReadDBInstanceRevisePage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbInstanceRevises)
		t.Log("DBInstanceReviseDaoImpl ReadDBInstanceRevisePage success, data=", string(bytes))
	}
}

func TestDBInstanceReviseDaoImpl_FindAll(t *testing.T) {
	dbInstanceReviseDao, _ := NewDBInstanceReviseDao(nil, context.TODO())
	dbInstanceRevises, err := dbInstanceReviseDao.FindAll()
	if err != nil {
		t.Errorf("DBInstanceReviseDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbInstanceRevises)
		t.Log("DBInstanceReviseDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestDBInstanceReviseDaoImpl_ReadDBInstanceRevise(t *testing.T) {
	dbInstanceReviseDao, _ := NewDBInstanceReviseDao(nil, context.TODO())
	dbInstanceRevise, err := dbInstanceReviseDao.ReadDBInstanceRevise(1)
	if err != nil {
		t.Errorf("DBInstanceReviseDaoImpl ReadDBInstanceRevise fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbInstanceRevise)
		t.Log("DBInstanceReviseDaoImpl ReadDBInstanceRevise success, data=", string(bytes))
	}
}

func TestDBInstanceReviseDaoImpl_DeleteDBInstanceRevise(t *testing.T) {
	dbInstanceReviseDao, _ := NewDBInstanceReviseDao(nil, context.TODO())
	affected, err := dbInstanceReviseDao.DeleteDBInstanceRevise(1)
	if err != nil {
		t.Errorf("DBInstanceReviseDaoImpl DeleteDBInstanceRevise fail,as[%s]", err.Error())
	} else {
		t.Log("DBInstanceReviseDaoImpl DeleteDBInstanceRevise success, affected=", affected)
	}
}

func TestMain(m *testing.M) {
	Setup()
	code := m.Run()
	Teardown()
	os.Exit(code)
}
