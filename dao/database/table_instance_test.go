package database

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/database"
)

func TestTableInstanceDaoImpl_CreateTableInstance(t *testing.T) {
	tableInstance := database.TableInstance{}
	tableInstanceDao, _ := NewTableInstanceDao(nil, context.TODO())
	id, err := tableInstanceDao.CreateTableInstance(&tableInstance)
	if err != nil {
		t.Errorf("TableInstanceDaoImpl CreateTableInstance fail,as[%s]", err.Error())
	} else {
		t.Log("TableInstanceDaoImpl CreateTableInstance success, id=", id)
	}
}

func TestTableInstanceDaoImpl_UpdateTableInstance(t *testing.T) {
	tableInstance := database.TableInstance{}
	tableInstanceDao, _ := NewTableInstanceDao(nil, context.TODO())
	affected, err := tableInstanceDao.UpdateTableInstance(&tableInstance)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("TableInstanceDaoImpl UpdateTableInstance fail,as[%s]", err.Error())
	} else {
		t.Log("TableInstanceDaoImpl UpdateTableInstance success, affected=", affected)
	}
}

func TestTableInstanceDaoImpl_ReadTableInstancePage(t *testing.T) {
	tableInstanceDao, _ := NewTableInstanceDao(nil, context.TODO())
	tableInstances, err := tableInstanceDao.ReadTableInstancePage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("TableInstanceDaoImpl ReadTableInstancePage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableInstances)
		t.Log("TableInstanceDaoImpl ReadTableInstancePage success, data=", string(bytes))
	}
}

func TestTableInstanceDaoImpl_FindAll(t *testing.T) {
	tableInstanceDao, _ := NewTableInstanceDao(nil, context.TODO())
	tableInstances, err := tableInstanceDao.FindAll()
	if err != nil {
		t.Errorf("TableInstanceDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableInstances)
		t.Log("TableInstanceDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestTableInstanceDaoImpl_ReadTableInstance(t *testing.T) {
	tableInstanceDao, _ := NewTableInstanceDao(nil, context.TODO())
	tableInstance, err := tableInstanceDao.ReadTableInstance(1)
	if err != nil {
		t.Errorf("TableInstanceDaoImpl ReadTableInstance fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableInstance)
		t.Log("TableInstanceDaoImpl ReadTableInstance success, data=", string(bytes))
	}
}

func TestTableInstanceDaoImpl_DeleteTableInstance(t *testing.T) {
	tableInstanceDao, _ := NewTableInstanceDao(nil, context.TODO())
	affected, err := tableInstanceDao.DeleteTableInstance(1)
	if err != nil {
		t.Errorf("TableInstanceDaoImpl DeleteTableInstance fail,as[%s]", err.Error())
	} else {
		t.Log("TableInstanceDaoImpl DeleteTableInstance success, affected=", affected)
	}
}
