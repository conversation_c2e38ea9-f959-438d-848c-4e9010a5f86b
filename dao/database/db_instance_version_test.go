package database

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/database"
)

func TestDBInstanceVersionDaoImpl_CreateDBInstanceVersion(t *testing.T) {
	dbInstanceVersion := database.DBInstanceVersion{}
	dbInstanceVersionDao, _ := NewDBInstanceVersionDao(nil, context.TODO())
	id, err := dbInstanceVersionDao.CreateDBInstanceVersion(&dbInstanceVersion)
	if err != nil {
		t.Errorf("DBInstanceVersionDaoImpl CreateDBInstanceVersion fail,as[%s]", err.Error())
	} else {
		t.Log("DBInstanceVersionDaoImpl CreateDBInstanceVersion success, id=", id)
	}
}

func TestDBInstanceVersionDaoImpl_UpdateDBInstanceVersion(t *testing.T) {
	dbInstanceVersion := database.DBInstanceVersion{}
	dbInstanceVersionDao, _ := NewDBInstanceVersionDao(nil, context.TODO())
	affected, err := dbInstanceVersionDao.UpdateDBInstanceVersion(&dbInstanceVersion)
	if err != nil {
		t.Errorf("DBInstanceVersionDaoImpl UpdateDBInstanceVersion fail,as[%s]", err.Error())
	} else {
		t.Log("DBInstanceVersionDaoImpl UpdateDBInstanceVersion success, affected=", affected)
	}
}

func TestDBInstanceVersionDaoImpl_ReadDBInstanceVersionPage(t *testing.T) {
	dbInstanceVersionDao, _ := NewDBInstanceVersionDao(nil, context.TODO())
	dbInstanceVersions, err := dbInstanceVersionDao.ReadDBInstanceVersionPage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("DBInstanceVersionDaoImpl ReadDBInstanceVersionPage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbInstanceVersions)
		t.Log("DBInstanceVersionDaoImpl ReadDBInstanceVersionPage success, data=", string(bytes))
	}
}

func TestDBInstanceVersionDaoImpl_FindAll(t *testing.T) {
	dbInstanceVersionDao, _ := NewDBInstanceVersionDao(nil, context.TODO())
	dbInstanceVersions, err := dbInstanceVersionDao.FindAll()
	if err != nil {
		t.Errorf("DBInstanceVersionDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbInstanceVersions)
		t.Log("DBInstanceVersionDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestDBInstanceVersionDaoImpl_ReadDBInstanceVersion(t *testing.T) {
	dbInstanceVersionDao, _ := NewDBInstanceVersionDao(nil, context.TODO())
	dbInstanceVersion, err := dbInstanceVersionDao.ReadDBInstanceVersion(1)
	if err != nil {
		t.Errorf("DBInstanceVersionDaoImpl ReadDBInstanceVersion fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbInstanceVersion)
		t.Log("DBInstanceVersionDaoImpl ReadDBInstanceVersion success, data=", string(bytes))
	}
}

func TestDBInstanceVersionDaoImpl_DeleteDBInstanceVersion(t *testing.T) {
	dbInstanceVersionDao, _ := NewDBInstanceVersionDao(nil, context.TODO())
	affected, err := dbInstanceVersionDao.DeleteDBInstanceVersion(1)
	if err != nil {
		t.Errorf("DBInstanceVersionDaoImpl DeleteDBInstanceVersion fail,as[%s]", err.Error())
	} else {
		t.Log("DBInstanceVersionDaoImpl DeleteDBInstanceVersion success, affected=", affected)
	}
}
