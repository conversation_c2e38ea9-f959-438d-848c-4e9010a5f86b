package database

import (
	"context"
	"fmt"
	"strings"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/database"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for database.DBInstance
type DBInstanceDaoImpl struct {
	db.DaoImpl
}

// new a data access object for dbInstance
func NewDBInstanceDao(session *xorm.Session, ctx context.Context) (*DBInstanceDaoImpl, error) {
	dbInstanceDao := DBInstanceDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(database.DBInstance)
	if err := dbInstanceDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	dbInstanceDao.NewSlice = func() interface{} {
		slice := make([]database.DBInstance, 0)
		return &slice
	}

	return &dbInstanceDao, nil
}

func (d *DBInstanceDaoImpl) CreateDBInstance(dbInstance *database.DBInstance) (int64, error) {
	affected, err := d.Create(dbInstance)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceDaoImpl CreateDBInstance [%v] fail,as:%s", dbInstance, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DBInstanceDaoImpl) DeleteDBInstance(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceDaoImpl DeleteDBInstance [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DBInstanceDaoImpl) UpdateDBInstance(dbInstance *database.DBInstance, dbInstances ...string) (int64, error) {
	if dbInstance == nil {
		return 0, fmt.Errorf("invalid paramter, dbInstance=nil")
	}

	affected, err := d.Update(dbInstance.ID, dbInstance, dbInstances...)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceDaoImpl UpdateDBInstance [%v] fail,as:%s", dbInstance.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DBInstanceDaoImpl) FindAll() ([]database.DBInstance, error) {
	var dbInstances []database.DBInstance
	err := d.SortListBy(&dbInstances, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "DBInstanceDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return dbInstances, nil
}

func (d *DBInstanceDaoImpl) ReadDBInstance(dbInstanceId uint64) (*database.DBInstance, error) {
	var dbInstance database.DBInstance
	exist, err := d.FindById(dbInstanceId, &dbInstance)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceDaoImpl Read [%d] fail,as:%s", dbInstanceId, err.Error())
		return nil, err
	} else if exist {
		return &dbInstance, nil
	} else {
		log.Debugf(d.Ctx, "DBInstanceDaoImpl the dbInstance id=[%d] not exist", dbInstanceId)
		return nil, fmt.Errorf("the dbInstance [%d] not exist", dbInstanceId)
	}
}

func (d *DBInstanceDaoImpl) ReadDBInstancePage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceDaoImpl ReadDBInstancePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *DBInstanceDaoImpl) ReadLastByFilter(filter *database.DBInstanceFilter) (*database.DBInstance, error) {
	records := make([]database.DBInstance, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "DBInstanceDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *DBInstanceDaoImpl) ReadDBInstanceInfo(dbInstanceId uint64) (*database.DBInstanceInfo, error) {
	var dbInstance database.DBInstanceInfo
	filter := database.DBInstanceFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(dbInstanceId, &dbInstance, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceDaoImpl ReadDBInstanceInfo [%d] fail,as:%s", dbInstanceId, err.Error())
		return nil, err
	} else if exist {
		return &dbInstance, nil
	} else {
		log.Debugf(d.Ctx, "DBInstanceDaoImpl the dbInstance id=[%d] not exist", dbInstanceId)
		return nil, fmt.Errorf("the dbInstance [%d] not exist", dbInstanceId)
	}
}

func (d *DBInstanceDaoImpl) ReadLastInfoByFilter(filter *database.DBInstanceFilter) (*database.DBInstanceInfo, error) {
	filter.Sort = 1 // 逆序排列，返回最后一条
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]database.DBInstanceInfo, 0)
	err := d.JoinFirst(&records, conditions, filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "DBInstanceDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *DBInstanceDaoImpl) FindDBInstancePage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DBInstanceDaoImpl FindDBInstancePage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DBInstanceDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]database.DBInstanceInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "DBInstanceDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DBInstanceDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]database.DBInstanceInfo, error) {
	records := make([]database.DBInstanceInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "DBInstanceDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DBInstanceDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*database.DBInstanceInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(database.DBInstanceInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "DBInstanceDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *DBInstanceDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindDBInstancePage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *DBInstanceDaoImpl) ReadByFilter(filter information.IFilter) ([]database.DBInstance, error) {
	records := make([]database.DBInstance, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DBInstanceDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DBInstanceDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *DBInstanceDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *DBInstanceDaoImpl) Entity() information.IRecord {
	return new(database.DBInstance)
}

func (d *DBInstanceDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(database.DBInstanceFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
