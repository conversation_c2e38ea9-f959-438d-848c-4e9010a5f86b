package database

import (
	"context"
	"fmt"
	"strings"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/database"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for database.TableInstanceRevise
type TableInstanceReviseDaoImpl struct {
	db.DaoImpl
}

// new a data access object for tableInstanceRevise
func NewTableInstanceReviseDao(session *xorm.Session, ctx context.Context) (*TableInstanceReviseDaoImpl, error) {
	tableInstanceReviseDao := TableInstanceReviseDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(database.TableInstanceRevise)
	if err := tableInstanceReviseDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	tableInstanceReviseDao.NewSlice = func() interface{} {
		slice := make([]database.TableInstanceRevise, 0)
		return &slice
	}

	return &tableInstanceReviseDao, nil
}

func (d *TableInstanceReviseDaoImpl) CreateTableInstanceRevise(tableInstanceRevise *database.TableInstanceRevise) (int64, error) {
	affected, err := d.Create(tableInstanceRevise)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceReviseDaoImpl CreateTableInstanceRevise [%v] fail,as:%s", tableInstanceRevise, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableInstanceReviseDaoImpl) DeleteTableInstanceRevise(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceReviseDaoImpl DeleteTableInstanceRevise [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableInstanceReviseDaoImpl) UpdateTableInstanceRevise(tableInstanceRevise *database.TableInstanceRevise, tableInstanceRevises ...string) (int64, error) {
	if tableInstanceRevise == nil {
		return 0, fmt.Errorf("invalid paramter, tableInstanceRevise=nil")
	}

	affected, err := d.Update(tableInstanceRevise.ID, tableInstanceRevise, tableInstanceRevises...)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceReviseDaoImpl UpdateTableInstanceRevise [%v] fail,as:%s", tableInstanceRevise.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableInstanceReviseDaoImpl) FindAll() ([]database.TableInstanceRevise, error) {
	var tableInstanceRevises []database.TableInstanceRevise
	err := d.SortListBy(&tableInstanceRevises, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "TableInstanceReviseDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return tableInstanceRevises, nil
}

func (d *TableInstanceReviseDaoImpl) ReadTableInstanceRevise(tableInstanceReviseId uint64) (*database.TableInstanceRevise, error) {
	var tableInstanceRevise database.TableInstanceRevise
	exist, err := d.FindById(tableInstanceReviseId, &tableInstanceRevise)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceReviseDaoImpl Read [%d] fail,as:%s", tableInstanceReviseId, err.Error())
		return nil, err
	} else if exist {
		return &tableInstanceRevise, nil
	} else {
		log.Debugf(d.Ctx, "TableInstanceReviseDaoImpl the tableInstanceRevise id=[%d] not exist", tableInstanceReviseId)
		return nil, fmt.Errorf("the tableInstanceRevise [%d] not exist", tableInstanceReviseId)
	}
}

func (d *TableInstanceReviseDaoImpl) ReadTableInstanceRevisePage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceReviseDaoImpl ReadTableInstanceRevisePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *TableInstanceReviseDaoImpl) ReadTableInstanceReviseInfo(tableInstanceReviseId uint64) (*database.TableInstanceReviseInfo, error) {
	var tableInstanceRevise database.TableInstanceReviseInfo
	filter := database.TableInstanceReviseFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(tableInstanceReviseId, &tableInstanceRevise, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceReviseDaoImpl ReadTableInstanceReviseInfo [%d] fail,as:%s", tableInstanceReviseId, err.Error())
		return nil, err
	} else if exist {
		return &tableInstanceRevise, nil
	} else {
		log.Debugf(d.Ctx, "TableInstanceReviseDaoImpl the tableInstanceRevise id=[%d] not exist", tableInstanceReviseId)
		return nil, fmt.Errorf("the tableInstanceRevise [%d] not exist", tableInstanceReviseId)
	}
}

func (d *TableInstanceReviseDaoImpl) ReadLastByFilter(filter *database.TableInstanceReviseFilter) (*database.TableInstanceRevise, error) {
	records := make([]database.TableInstanceRevise, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortFirstBy(&records, sort, query, args...)
	if log.IfError(err, "TableInstanceReviseDaoImpl SortFirstBy") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *TableInstanceReviseDaoImpl) FindTableInstanceRevisePage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "TableInstanceReviseDaoImpl FindTableInstanceRevisePage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *TableInstanceReviseDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]database.TableInstanceReviseInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "TableInstanceReviseDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *TableInstanceReviseDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]database.TableInstanceReviseInfo, error) {
	records := make([]database.TableInstanceReviseInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "TableInstanceReviseDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *TableInstanceReviseDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*database.TableInstanceReviseInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(database.TableInstanceReviseInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceReviseDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "TableInstanceReviseDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *TableInstanceReviseDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindTableInstanceRevisePage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *TableInstanceReviseDaoImpl) ReadByFilter(filter information.IFilter) ([]database.TableInstanceRevise, error) {
	records := make([]database.TableInstanceRevise, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "TableInstanceReviseDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *TableInstanceReviseDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *TableInstanceReviseDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *TableInstanceReviseDaoImpl) Entity() information.IRecord {
	return new(database.TableInstanceRevise)
}

func (d *TableInstanceReviseDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(database.TableInstanceReviseFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
