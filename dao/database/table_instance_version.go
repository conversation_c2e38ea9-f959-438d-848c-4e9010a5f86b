package database

import (
	"context"
	"fmt"
	"strings"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/database"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for database.TableInstanceVersion
type TableInstanceVersionDaoImpl struct {
	db.DaoImpl
}

// new a data access object for tableInstanceVersion
func NewTableInstanceVersionDao(session *xorm.Session, ctx context.Context) (*TableInstanceVersionDaoImpl, error) {
	tableInstanceVersionDao := TableInstanceVersionDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(database.TableInstanceVersion)
	if err := tableInstanceVersionDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	tableInstanceVersionDao.NewSlice = func() interface{} {
		slice := make([]database.TableInstanceVersion, 0)
		return &slice
	}

	return &tableInstanceVersionDao, nil
}

func (d *TableInstanceVersionDaoImpl) CreateTableInstanceVersion(tableInstanceVersion *database.TableInstanceVersion) (int64, error) {
	affected, err := d.Create(tableInstanceVersion)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceVersionDaoImpl CreateTableInstanceVersion [%v] fail,as:%s", tableInstanceVersion, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableInstanceVersionDaoImpl) DeleteTableInstanceVersion(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceVersionDaoImpl DeleteTableInstanceVersion [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableInstanceVersionDaoImpl) UpdateTableInstanceVersion(tableInstanceVersion *database.TableInstanceVersion, tableInstanceVersions ...string) (int64, error) {
	if tableInstanceVersion == nil {
		return 0, fmt.Errorf("invalid paramter, tableInstanceVersion=nil")
	}

	affected, err := d.Update(tableInstanceVersion.ID, tableInstanceVersion, tableInstanceVersions...)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceVersionDaoImpl UpdateTableInstanceVersion [%v] fail,as:%s", tableInstanceVersion.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableInstanceVersionDaoImpl) FindAll() ([]database.TableInstanceVersion, error) {
	var tableInstanceVersions []database.TableInstanceVersion
	err := d.SortListBy(&tableInstanceVersions, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "TableInstanceVersionDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return tableInstanceVersions, nil
}

func (d *TableInstanceVersionDaoImpl) ReadTableInstanceVersion(tableInstanceVersionId uint64) (*database.TableInstanceVersion, error) {
	var tableInstanceVersion database.TableInstanceVersion
	exist, err := d.FindById(tableInstanceVersionId, &tableInstanceVersion)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceVersionDaoImpl Read [%d] fail,as:%s", tableInstanceVersionId, err.Error())
		return nil, err
	} else if exist {
		return &tableInstanceVersion, nil
	} else {
		log.Debugf(d.Ctx, "TableInstanceVersionDaoImpl the tableInstanceVersion id=[%d] not exist", tableInstanceVersionId)
		return nil, fmt.Errorf("the tableInstanceVersion [%d] not exist", tableInstanceVersionId)
	}
}

func (d *TableInstanceVersionDaoImpl) ReadTableInstanceVersionPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceVersionDaoImpl ReadTableInstanceVersionPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *TableInstanceVersionDaoImpl) ReadLastByFilter(filter *database.TableInstanceVersionFilter) (*database.TableInstanceVersion, error) {
	records := make([]database.TableInstanceVersion, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "TableInstanceVersionDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *TableInstanceVersionDaoImpl) ReadTableInstanceVersionInfo(tableInstanceVersionId uint64) (*database.TableInstanceVersionInfo, error) {
	var tableInstanceVersion database.TableInstanceVersionInfo
	filter := database.TableInstanceVersionFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(tableInstanceVersionId, &tableInstanceVersion, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceVersionDaoImpl ReadTableInstanceVersionInfo [%d] fail,as:%s", tableInstanceVersionId, err.Error())
		return nil, err
	} else if exist {
		return &tableInstanceVersion, nil
	} else {
		log.Debugf(d.Ctx, "TableInstanceVersionDaoImpl the tableInstanceVersion id=[%d] not exist", tableInstanceVersionId)
		return nil, fmt.Errorf("the tableInstanceVersion [%d] not exist", tableInstanceVersionId)
	}
}

func (d *TableInstanceVersionDaoImpl) ReadLastInfoByFilter(filter *database.TableInstanceVersionFilter) (*database.TableInstanceVersionInfo, error) {
	filter.Sort = 1 // 逆序排列，返回最后一条
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]database.TableInstanceVersionInfo, 0)
	err := d.JoinFirst(&records, conditions, filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "TableInstanceVersionDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *TableInstanceVersionDaoImpl) FindTableInstanceVersionPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "TableInstanceVersionDaoImpl FindTableInstanceVersionPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *TableInstanceVersionDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]database.TableInstanceVersionInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "TableInstanceVersionDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *TableInstanceVersionDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]database.TableInstanceVersionInfo, error) {
	records := make([]database.TableInstanceVersionInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "TableInstanceVersionDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *TableInstanceVersionDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*database.TableInstanceVersionInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(database.TableInstanceVersionInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceVersionDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "TableInstanceVersionDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *TableInstanceVersionDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindTableInstanceVersionPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *TableInstanceVersionDaoImpl) ReadByFilter(filter information.IFilter) ([]database.TableInstanceVersion, error) {
	records := make([]database.TableInstanceVersion, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "TableInstanceVersionDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *TableInstanceVersionDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *TableInstanceVersionDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *TableInstanceVersionDaoImpl) Entity() information.IRecord {
	return new(database.TableInstanceVersion)
}

func (d *TableInstanceVersionDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(database.TableInstanceVersionFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
