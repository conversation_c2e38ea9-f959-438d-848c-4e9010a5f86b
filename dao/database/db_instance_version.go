package database

import (
	"context"
	"fmt"
	"strings"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/database"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for database.DBInstanceVersion
type DBInstanceVersionDaoImpl struct {
	db.DaoImpl
}

// new a data access object for dbInstanceVersion
func NewDBInstanceVersionDao(session *xorm.Session, ctx context.Context) (*DBInstanceVersionDaoImpl, error) {
	dbInstanceVersionDao := DBInstanceVersionDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(database.DBInstanceVersion)
	if err := dbInstanceVersionDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	dbInstanceVersionDao.NewSlice = func() interface{} {
		slice := make([]database.DBInstanceVersion, 0)
		return &slice
	}

	return &dbInstanceVersionDao, nil
}

func (d *DBInstanceVersionDaoImpl) CreateDBInstanceVersion(dbInstanceVersion *database.DBInstanceVersion) (int64, error) {
	affected, err := d.Create(dbInstanceVersion)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceVersionDaoImpl CreateDBInstanceVersion [%v] fail,as:%s", dbInstanceVersion, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DBInstanceVersionDaoImpl) DeleteDBInstanceVersion(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceVersionDaoImpl DeleteDBInstanceVersion [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DBInstanceVersionDaoImpl) UpdateDBInstanceVersion(dbInstanceVersion *database.DBInstanceVersion, dbInstanceVersions ...string) (int64, error) {
	if dbInstanceVersion == nil {
		return 0, fmt.Errorf("invalid paramter, dbInstanceVersion=nil")
	}

	affected, err := d.Update(dbInstanceVersion.ID, dbInstanceVersion, dbInstanceVersions...)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceVersionDaoImpl UpdateDBInstanceVersion [%v] fail,as:%s", dbInstanceVersion.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DBInstanceVersionDaoImpl) FindAll() ([]database.DBInstanceVersion, error) {
	var dbInstanceVersions []database.DBInstanceVersion
	err := d.SortListBy(&dbInstanceVersions, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "DBInstanceVersionDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return dbInstanceVersions, nil
}

func (d *DBInstanceVersionDaoImpl) ReadDBInstanceVersion(dbInstanceVersionId uint64) (*database.DBInstanceVersion, error) {
	var dbInstanceVersion database.DBInstanceVersion
	exist, err := d.FindById(dbInstanceVersionId, &dbInstanceVersion)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceVersionDaoImpl Read [%d] fail,as:%s", dbInstanceVersionId, err.Error())
		return nil, err
	} else if exist {
		return &dbInstanceVersion, nil
	} else {
		log.Debugf(d.Ctx, "DBInstanceVersionDaoImpl the dbInstanceVersion id=[%d] not exist", dbInstanceVersionId)
		return nil, fmt.Errorf("the dbInstanceVersion [%d] not exist", dbInstanceVersionId)
	}
}

func (d *DBInstanceVersionDaoImpl) ReadDBInstanceVersionPage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceVersionDaoImpl ReadDBInstanceVersionPage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *DBInstanceVersionDaoImpl) ReadLastByFilter(filter *database.DBInstanceVersionFilter) (*database.DBInstanceVersion, error) {
	records := make([]database.DBInstanceVersion, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "DBInstanceVersionDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *DBInstanceVersionDaoImpl) ReadDBInstanceVersionInfo(dbInstanceVersionId uint64) (*database.DBInstanceVersionInfo, error) {
	var dbInstanceVersion database.DBInstanceVersionInfo
	filter := database.DBInstanceVersionFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(dbInstanceVersionId, &dbInstanceVersion, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceVersionDaoImpl ReadDBInstanceVersionInfo [%d] fail,as:%s", dbInstanceVersionId, err.Error())
		return nil, err
	} else if exist {
		return &dbInstanceVersion, nil
	} else {
		log.Debugf(d.Ctx, "DBInstanceVersionDaoImpl the dbInstanceVersion id=[%d] not exist", dbInstanceVersionId)
		return nil, fmt.Errorf("the dbInstanceVersion [%d] not exist", dbInstanceVersionId)
	}
}

func (d *DBInstanceVersionDaoImpl) ReadLastInfoByFilter(filter *database.DBInstanceVersionFilter) (*database.DBInstanceVersionInfo, error) {
	filter.Sort = 1 // 逆序排列，返回最后一条
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]database.DBInstanceVersionInfo, 0)
	err := d.JoinFirst(&records, conditions, filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "DBInstanceVersionDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *DBInstanceVersionDaoImpl) FindDBInstanceVersionPage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DBInstanceVersionDaoImpl FindDBInstanceVersionPage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DBInstanceVersionDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]database.DBInstanceVersionInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "DBInstanceVersionDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DBInstanceVersionDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]database.DBInstanceVersionInfo, error) {
	records := make([]database.DBInstanceVersionInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "DBInstanceVersionDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DBInstanceVersionDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*database.DBInstanceVersionInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(database.DBInstanceVersionInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceVersionDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "DBInstanceVersionDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *DBInstanceVersionDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindDBInstanceVersionPage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *DBInstanceVersionDaoImpl) ReadByFilter(filter information.IFilter) ([]database.DBInstanceVersion, error) {
	records := make([]database.DBInstanceVersion, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DBInstanceVersionDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DBInstanceVersionDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *DBInstanceVersionDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *DBInstanceVersionDaoImpl) Entity() information.IRecord {
	return new(database.DBInstanceVersion)
}

func (d *DBInstanceVersionDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(database.DBInstanceVersionFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
