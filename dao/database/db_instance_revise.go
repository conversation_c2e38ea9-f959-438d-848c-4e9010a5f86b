package database

import (
	"context"
	"fmt"
	"strings"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/database"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for database.DBInstanceRevise
type DBInstanceReviseDaoImpl struct {
	db.DaoImpl
}

// new a data access object for dbInstanceRevise
func NewDBInstanceReviseDao(session *xorm.Session, ctx context.Context) (*DBInstanceReviseDaoImpl, error) {
	dbInstanceReviseDao := DBInstanceReviseDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(database.DBInstanceRevise)
	if err := dbInstanceReviseDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	dbInstanceReviseDao.NewSlice = func() interface{} {
		slice := make([]database.DBInstanceRevise, 0)
		return &slice
	}

	return &dbInstanceReviseDao, nil
}

func (d *DBInstanceReviseDaoImpl) CreateDBInstanceRevise(dbInstanceRevise *database.DBInstanceRevise) (int64, error) {
	affected, err := d.Create(dbInstanceRevise)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceReviseDaoImpl CreateDBInstanceRevise [%v] fail,as:%s", dbInstanceRevise, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DBInstanceReviseDaoImpl) DeleteDBInstanceRevise(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceReviseDaoImpl DeleteDBInstanceRevise [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DBInstanceReviseDaoImpl) UpdateDBInstanceRevise(dbInstanceRevise *database.DBInstanceRevise, dbInstanceRevises ...string) (int64, error) {
	if dbInstanceRevise == nil {
		return 0, fmt.Errorf("invalid paramter, dbInstanceRevise=nil")
	}

	affected, err := d.Update(dbInstanceRevise.ID, dbInstanceRevise, dbInstanceRevises...)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceReviseDaoImpl UpdateDBInstanceRevise [%v] fail,as:%s", dbInstanceRevise.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *DBInstanceReviseDaoImpl) FindAll() ([]database.DBInstanceRevise, error) {
	var dbInstanceRevises []database.DBInstanceRevise
	err := d.SortListBy(&dbInstanceRevises, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "DBInstanceReviseDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return dbInstanceRevises, nil
}

func (d *DBInstanceReviseDaoImpl) ReadDBInstanceRevise(dbInstanceReviseId uint64) (*database.DBInstanceRevise, error) {
	var dbInstanceRevise database.DBInstanceRevise
	exist, err := d.FindById(dbInstanceReviseId, &dbInstanceRevise)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceReviseDaoImpl Read [%d] fail,as:%s", dbInstanceReviseId, err.Error())
		return nil, err
	} else if exist {
		return &dbInstanceRevise, nil
	} else {
		log.Debugf(d.Ctx, "DBInstanceReviseDaoImpl the dbInstanceRevise id=[%d] not exist", dbInstanceReviseId)
		return nil, fmt.Errorf("the dbInstanceRevise [%d] not exist", dbInstanceReviseId)
	}
}

func (d *DBInstanceReviseDaoImpl) ReadDBInstanceRevisePage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceReviseDaoImpl ReadDBInstanceRevisePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *DBInstanceReviseDaoImpl) ReadDBInstanceReviseInfo(dbInstanceReviseId uint64) (*database.DBInstanceReviseInfo, error) {
	var dbInstanceRevise database.DBInstanceReviseInfo
	filter := database.DBInstanceReviseFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(dbInstanceReviseId, &dbInstanceRevise, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceReviseDaoImpl ReadDBInstanceReviseInfo [%d] fail,as:%s", dbInstanceReviseId, err.Error())
		return nil, err
	} else if exist {
		return &dbInstanceRevise, nil
	} else {
		log.Debugf(d.Ctx, "DBInstanceReviseDaoImpl the dbInstanceRevise id=[%d] not exist", dbInstanceReviseId)
		return nil, fmt.Errorf("the dbInstanceRevise [%d] not exist", dbInstanceReviseId)
	}
}

func (d *DBInstanceReviseDaoImpl) ReadLastByFilter(filter *database.DBInstanceReviseFilter) (*database.DBInstanceRevise, error) {
	records := make([]database.DBInstanceRevise, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortFirstBy(&records, sort, query, args...)
	if log.IfError(err, "DBInstanceReviseDaoImpl SortFirstBy") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *DBInstanceReviseDaoImpl) FindDBInstanceRevisePage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DBInstanceReviseDaoImpl FindDBInstanceRevisePage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DBInstanceReviseDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]database.DBInstanceReviseInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "DBInstanceReviseDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *DBInstanceReviseDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]database.DBInstanceReviseInfo, error) {
	records := make([]database.DBInstanceReviseInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "DBInstanceReviseDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DBInstanceReviseDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*database.DBInstanceReviseInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(database.DBInstanceReviseInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "DBInstanceReviseDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "DBInstanceReviseDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *DBInstanceReviseDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindDBInstanceRevisePage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *DBInstanceReviseDaoImpl) ReadByFilter(filter information.IFilter) ([]database.DBInstanceRevise, error) {
	records := make([]database.DBInstanceRevise, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "DBInstanceReviseDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *DBInstanceReviseDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *DBInstanceReviseDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *DBInstanceReviseDaoImpl) Entity() information.IRecord {
	return new(database.DBInstanceRevise)
}

func (d *DBInstanceReviseDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(database.DBInstanceReviseFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
