package database

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/database"
)

func TestDBInstanceDaoImpl_CreateDBInstance(t *testing.T) {
	dbInstance := database.DBInstance{}
	dbInstanceDao, _ := NewDBInstanceDao(nil, context.TODO())
	id, err := dbInstanceDao.CreateDBInstance(&dbInstance)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("DBInstanceDaoImpl CreateDBInstance fail,as[%s]", err.<PERSON>rror())
	} else {
		t.Log("DBInstanceDaoImpl CreateDBInstance success, id=", id)
	}
}

func TestDBInstanceDaoImpl_UpdateDBInstance(t *testing.T) {
	dbInstance := database.DBInstance{}
	dbInstanceDao, _ := NewDBInstanceDao(nil, context.TODO())
	affected, err := dbInstanceDao.UpdateDBInstance(&dbInstance)
	if err != nil {
		t.<PERSON><PERSON>("DBInstanceDaoImpl UpdateDBInstance fail,as[%s]", err.Error())
	} else {
		t.Log("DBInstanceDaoImpl UpdateDBInstance success, affected=", affected)
	}
}

func TestDBInstanceDaoImpl_ReadDBInstancePage(t *testing.T) {
	dbInstanceDao, _ := NewDBInstanceDao(nil, context.TODO())
	dbInstances, err := dbInstanceDao.ReadDBInstancePage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("DBInstanceDaoImpl ReadDBInstancePage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbInstances)
		t.Log("DBInstanceDaoImpl ReadDBInstancePage success, data=", string(bytes))
	}
}

func TestDBInstanceDaoImpl_FindAll(t *testing.T) {
	dbInstanceDao, _ := NewDBInstanceDao(nil, context.TODO())
	dbInstances, err := dbInstanceDao.FindAll()
	if err != nil {
		t.Errorf("DBInstanceDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbInstances)
		t.Log("DBInstanceDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestDBInstanceDaoImpl_ReadDBInstance(t *testing.T) {
	dbInstanceDao, _ := NewDBInstanceDao(nil, context.TODO())
	dbInstance, err := dbInstanceDao.ReadDBInstance(1)
	if err != nil {
		t.Errorf("DBInstanceDaoImpl ReadDBInstance fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(dbInstance)
		t.Log("DBInstanceDaoImpl ReadDBInstance success, data=", string(bytes))
	}
}

func TestDBInstanceDaoImpl_DeleteDBInstance(t *testing.T) {
	dbInstanceDao, _ := NewDBInstanceDao(nil, context.TODO())
	affected, err := dbInstanceDao.DeleteDBInstance(1)
	if err != nil {
		t.Errorf("DBInstanceDaoImpl DeleteDBInstance fail,as[%s]", err.Error())
	} else {
		t.Log("DBInstanceDaoImpl DeleteDBInstance success, affected=", affected)
	}
}
