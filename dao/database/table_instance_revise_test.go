package database

import (
	"context"
	"encoding/json"
	"testing"

	"git.platform.io/environment/environment/model/database"
)

func TestTableInstanceReviseDaoImpl_CreateTableInstanceRevise(t *testing.T) {
	tableInstanceRevise := database.TableInstanceRevise{}
	tableInstanceReviseDao, _ := NewTableInstanceReviseDao(nil, context.TODO())
	id, err := tableInstanceReviseDao.CreateTableInstanceRevise(&tableInstanceRevise)
	if err != nil {
		t.Errorf("TableInstanceReviseDaoImpl CreateTableInstanceRevise fail,as[%s]", err.Error())
	} else {
		t.Log("TableInstanceReviseDaoImpl CreateTableInstanceRevise success, id=", id)
	}
}

func TestTableInstanceReviseDaoImpl_UpdateTableInstanceRevise(t *testing.T) {
	tableInstanceRevise := database.TableInstanceRevise{}
	tableInstanceReviseDao, _ := NewTableInstanceReviseDao(nil, context.TODO())
	affected, err := tableInstanceReviseDao.UpdateTableInstanceRevise(&tableInstanceRevise)
	if err != nil {
		t.Errorf("TableInstanceReviseDaoImpl UpdateTableInstanceRevise fail,as[%s]", err.Error())
	} else {
		t.Log("TableInstanceReviseDaoImpl UpdateTableInstanceRevise success, affected=", affected)
	}
}

func TestTableInstanceReviseDaoImpl_ReadTableInstanceRevisePage(t *testing.T) {
	tableInstanceReviseDao, _ := NewTableInstanceReviseDao(nil, context.TODO())
	tableInstanceRevises, err := tableInstanceReviseDao.ReadTableInstanceRevisePage(1, 10, "id ASC", "id > ?", "0")
	if err != nil {
		t.Errorf("TableInstanceReviseDaoImpl ReadTableInstanceRevisePage fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableInstanceRevises)
		t.Log("TableInstanceReviseDaoImpl ReadTableInstanceRevisePage success, data=", string(bytes))
	}
}

func TestTableInstanceReviseDaoImpl_FindAll(t *testing.T) {
	tableInstanceReviseDao, _ := NewTableInstanceReviseDao(nil, context.TODO())
	tableInstanceRevises, err := tableInstanceReviseDao.FindAll()
	if err != nil {
		t.Errorf("TableInstanceReviseDaoImpl FindAll fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableInstanceRevises)
		t.Log("TableInstanceReviseDaoImpl FindAll success, data=", string(bytes))
	}
}

func TestTableInstanceReviseDaoImpl_ReadTableInstanceRevise(t *testing.T) {
	tableInstanceReviseDao, _ := NewTableInstanceReviseDao(nil, context.TODO())
	tableInstanceRevise, err := tableInstanceReviseDao.ReadTableInstanceRevise(1)
	if err != nil {
		t.Errorf("TableInstanceReviseDaoImpl ReadTableInstanceRevise fail,as[%s]", err.Error())
	} else {
		bytes, _ := json.Marshal(tableInstanceRevise)
		t.Log("TableInstanceReviseDaoImpl ReadTableInstanceRevise success, data=", string(bytes))
	}
}

func TestTableInstanceReviseDaoImpl_DeleteTableInstanceRevise(t *testing.T) {
	tableInstanceReviseDao, _ := NewTableInstanceReviseDao(nil, context.TODO())
	affected, err := tableInstanceReviseDao.DeleteTableInstanceRevise(1)
	if err != nil {
		t.Errorf("TableInstanceReviseDaoImpl DeleteTableInstanceRevise fail,as[%s]", err.Error())
	} else {
		t.Log("TableInstanceReviseDaoImpl DeleteTableInstanceRevise success, affected=", affected)
	}
}
