package database

import (
	"context"
	"fmt"
	"strings"

	db "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/model/database"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
)

// Data Access Object for database.TableInstance
type TableInstanceDaoImpl struct {
	db.DaoImpl
}

// new a data access object for tableInstance
func NewTableInstanceDao(session *xorm.Session, ctx context.Context) (*TableInstanceDaoImpl, error) {
	tableInstanceDao := TableInstanceDaoImpl{
		DaoImpl: db.DaoImpl{
			Ctx: ctx,
		},
	}
	m := new(database.TableInstance)
	if err := tableInstanceDao.Init(m.TableName(), m, session); err != nil {
		return nil, err
	}
	tableInstanceDao.NewSlice = func() interface{} {
		slice := make([]database.TableInstance, 0)
		return &slice
	}

	return &tableInstanceDao, nil
}

func (d *TableInstanceDaoImpl) CreateTableInstance(tableInstance *database.TableInstance) (int64, error) {
	affected, err := d.Create(tableInstance)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceDaoImpl CreateTableInstance [%v] fail,as:%s", tableInstance, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableInstanceDaoImpl) DeleteTableInstance(id uint64) (int64, error) {
	affected, err := d.HardDeleteById(id)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceDaoImpl DeleteTableInstance [%v] fail,as:%s", id, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableInstanceDaoImpl) UpdateTableInstance(tableInstance *database.TableInstance, fields ...string) (int64, error) {
	if tableInstance == nil {
		return 0, fmt.Errorf("invalid paramter, tableInstance=nil")
	}

	affected, err := d.Update(tableInstance.ID, tableInstance, fields...)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceDaoImpl UpdateTableInstance [%v] fail,as:%s", tableInstance.ID, err.Error())
		return 0, err
	} else {
		return affected, nil
	}
}

func (d *TableInstanceDaoImpl) FindAll() ([]database.TableInstance, error) {
	var tableInstances []database.TableInstance
	err := d.SortListBy(&tableInstances, "id ASC", "1=1")
	if err != nil {
		log.Error(d.Ctx, "TableInstanceDaoImpl FindAll fail,as:", err.Error())
		return nil, err
	}
	return tableInstances, nil
}

func (d *TableInstanceDaoImpl) ReadTableInstance(tableInstanceId uint64) (*database.TableInstance, error) {
	var tableInstance database.TableInstance
	exist, err := d.FindById(tableInstanceId, &tableInstance)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceDaoImpl Read [%d] fail,as:%s", tableInstanceId, err.Error())
		return nil, err
	} else if exist {
		return &tableInstance, nil
	} else {
		log.Debugf(d.Ctx, "TableInstanceDaoImpl the tableInstance id=[%d] not exist", tableInstanceId)
		return nil, fmt.Errorf("the tableInstance [%d] not exist", tableInstanceId)
	}
}

func (d *TableInstanceDaoImpl) ReadTableInstancePage(page, pageSize int, orderBy string, query interface{}, args ...interface{}) (*information.PageData, error) {
	rlt, err := d.ListPage(page, pageSize, orderBy, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceDaoImpl ReadTableInstancePage fail,as:%s", err.Error())
		return nil, err
	} else {
		return rlt, nil
	}
}

func (d *TableInstanceDaoImpl) ReadLastByFilter(filter *database.TableInstanceFilter) (*database.TableInstance, error) {
	records := make([]database.TableInstance, 0)
	query, args := filter.ToSql()
	sort := "id desc"
	err := d.SortListBy(&records, sort, query, args...)
	if log.IfError(err, "TableInstanceDaoImpl ReadByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *TableInstanceDaoImpl) ReadTableInstanceInfo(tableInstanceId uint64) (*database.TableInstanceInfo, error) {
	var tableInstance database.TableInstanceInfo
	filter := database.TableInstanceFilter{}
	conditions := filter.GetJoinCondition()
	exist, err := d.JoinFindById(tableInstanceId, &tableInstance, conditions)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceDaoImpl ReadTableInstanceInfo [%d] fail,as:%s", tableInstanceId, err.Error())
		return nil, err
	} else if exist {
		return &tableInstance, nil
	} else {
		log.Debugf(d.Ctx, "TableInstanceDaoImpl the tableInstance id=[%d] not exist", tableInstanceId)
		return nil, fmt.Errorf("the tableInstance [%d] not exist", tableInstanceId)
	}
}

func (d *TableInstanceDaoImpl) ReadLastInfoByFilter(filter *database.TableInstanceFilter) (*database.TableInstanceInfo, error) {
	filter.Sort = 1 // 逆序排列，返回最后一条
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]database.TableInstanceInfo, 0)
	err := d.JoinFirst(&records, conditions, filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "TableInstanceDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else if len(records) > 0 {
		return &records[0], err
	} else {
		return nil, nil
	}
}

func (d *TableInstanceDaoImpl) FindTableInstancePage(filter information.IFilter) (*information.PageData, error) {
	query, args := filter.ToSql()
	page, err := d.ListPage(filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "TableInstanceDaoImpl FindTableInstancePage") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *TableInstanceDaoImpl) ReadInfoPageByFilter(filter information.IJoinFilter) (*information.PageData, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	records := make([]database.TableInstanceInfo, 0)
	page, err := d.JoinListColPage(&records, conditions, filter.GetPageIndex(), filter.GetPageSize(), filter.GetOrderBy(true), query, args...)
	if log.IfError(err, "TableInstanceDaoImpl ReadInfoPageByFilter") {
		return nil, err
	} else {
		return page, err
	}
}

func (d *TableInstanceDaoImpl) ReadInfoListByFilter(filter information.IJoinFilter) ([]database.TableInstanceInfo, error) {
	records := make([]database.TableInstanceInfo, 0)
	err := d.JoinQueryByFilter(&records, filter)
	if log.IfError(err, "TableInstanceDaoImpl ReadInfoListByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *TableInstanceDaoImpl) ReadInfoByFilter(filter information.IJoinFilter) (*database.TableInstanceInfo, error) {
	conditions := filter.GetJoinCondition()
	query, args := filter.ToAliasSql()

	record := new(database.TableInstanceInfo)
	exist, err := d.JoinFind(record, conditions, query, args...)
	if err != nil {
		log.Errorf(d.Ctx, "TableInstanceDaoImpl ReadInfoByFilter [%++v] fail,as:%s", filter, err.Error())
		return nil, err
	} else if exist {
		return record, nil
	} else {
		log.Debugf(d.Ctx, "TableInstanceDaoImpl the record [%++v] not exist", filter)
		return nil, fmt.Errorf("the record [%++v] not exist", filter)
	}
}

func (d *TableInstanceDaoImpl) ReadRecordPageByFilter(f information.IFilter) (*information.PageData, error) {
	filter, ok := f.(information.IJoinFilter)
	if !ok {
		return d.FindTableInstancePage(f)
	} else {
		return d.ReadInfoPageByFilter(filter)
	}
}

func (d *TableInstanceDaoImpl) ReadByFilter(filter information.IFilter) ([]database.TableInstance, error) {
	records := make([]database.TableInstance, 0)
	query, args := filter.ToSql()

	err := d.SortListBy(&records, filter.GetOrderBy(false), query, args...)
	if log.IfError(err, "TableInstanceDaoImpl ReadByFilter") {
		return nil, err
	} else {
		return records, err
	}
}

func (d *TableInstanceDaoImpl) ReadRecordListByFilter(filter information.IFilter) ([]information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}
	records := make([]information.IRecord, 0)
	for _, record := range recordList {
		records = append(records, record)
	}
	return records, err
}

func (d *TableInstanceDaoImpl) ReadRecordByFilter(filter information.IFilter) (information.IRecord, error) {
	recordList, err := d.ReadByFilter(filter)
	if err != nil {
		return nil, err
	}

	if len(recordList) >= 1 {
		return recordList[0], nil
	} else {
		return nil, fmt.Errorf("record not exist")
	}
}

func (d *TableInstanceDaoImpl) Entity() information.IRecord {
	return new(database.TableInstance)
}

func (d *TableInstanceDaoImpl) GenerateFilter(op *app.Operator, data map[string]interface{}, orderBy []string) (information.IFilter, error) {
	filter := new(database.TableInstanceFilter)
	err := utils.ConvertToStruck(data, filter)
	if err != nil {
		return nil, err
	}

	if len(orderBy) > 0 {
		filter.OrderBy = orderBy[0]
	}
	if len(orderBy) > 1 {
		if strings.ToLower(orderBy[1]) == "asc" {
			filter.Sort = 1
		}
	}

	filter.TenantID = op.TenantID

	return filter, err
}
