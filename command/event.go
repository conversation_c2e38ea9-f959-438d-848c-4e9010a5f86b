package command

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"git.multiverse.io/eventkit/kit/handler/config/factory"
	"git.multiverse.io/eventkit/kit/handler/heartbeat"
	"git.multiverse.io/framework/common/errors"
	"git.platform.io/resource/common/log"
	"github.com/spf13/cobra"
)

var (
	// AppName represents the name of the service, which can be modified by main.AppName when compiling
	AppName = "environment"

	// Version represents the version number of the service, which can be modified by main.Version when compiling
	Version = "v0.0.1"

	// AppConfPath represents the config path of the service, which can be modified by main.AppConfPath when compiling
	AppConfPath = "conf/app.toml"

	DB_KEY = "default"
)

var eventCmd = &cobra.Command{
	Use:   "event",
	Short: "as solace client work",
	Run: func(cmd *cobra.Command, args []string) {
		if err := Init(); nil != err {
			panic(fmt.Sprintf("failed to start %s[version=%s], error=%s", AppName, Version, errors.ErrorToString(err)))
		}

		signals := make(chan os.Signal, 1)
		signal.Notify(signals, syscall.SIGINT, syscall.SIGTERM)
		s := <-signals
		log.Infof(context.TODO(), "Received s %s and exit", s.String())
	},
}

// Init is used to initialize the service
//
// @return error if the initialization fails, the error will be returned
func Init() error {
	// init config
	configs, err := factory.InitHandlerConfig(AppConfPath)
	if nil != err {
		return err
	}

	// init log
	log.InitLog()

	// start handler
	// if err := handler.Start(router.InitHandlerRouters, configs.GenCallbackOptions()...); nil != err {
	// 	return err
	// }

	// start heartbeat
	if err := heartbeat.StartHeartbeat(configs.Heartbeat.TopicName, configs.Heartbeat.IntervalSeconds); nil != err {
		return err
	}
	// if defaultDB, ok := configs.Db[DB_KEY]; ok {
	// 	if err := mysqldao.Init(&defaultDB); err != nil {
	// 		return err
	// 	}

	// } else {
	// 	return fmt.Errorf("%s cannot found db[%s] config", constant.SystemInternalError, DB_KEY)
	// }

	// if err := mysqldao.InitDbtables(); err != nil {
	// 	log.Error("Failed to init db tables.")
	// }

	// if err = instmodels.InitTopicMethod(); nil != err {
	// 	return err
	// }
	// e := gitlabdao.Init(configs)
	// if e != nil {
	// 	return fmt.Errorf("gitlab initialization failed, please check gitlab.url or gitlab.token. as: %s", e.Error())
	// }

	log.Infof(context.TODO(), "Successfully to start `%s` service[version=%s]", AppName, Version)

	return nil
}
