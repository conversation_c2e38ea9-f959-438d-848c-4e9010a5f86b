package command

import (
	"context"
	"fmt"
	"os"

	handler "git.platform.io/environment/environment/handler/stack"
	"git.platform.io/environment/environment/model/stack"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"github.com/spf13/cobra"
)

type RepairParam struct {
	Operate       string `json:"operate"`
	TenantID      string `json:"tenantID"`
	EnvironmentID uint64 `json:"environmentId"`
	ServiceCode   string `json:"serviceCode"`
	Category      string `json:"category"`
}

var Param RepairParam

func init() {
	repairCmd.Flags().StringVar(&Param.Operate, "operate", "check", "check/update")
	repairCmd.Flags().StringVar(&Param.TenantID, "tenantId", "", "tenant id")
	repairCmd.Flags().Uint64Var(&Param.EnvironmentID, "environment", 0, "Environment ID")
	repairCmd.Flags().StringVar(&Param.Category, "category", "", "service type")
	repairCmd.Flags().StringVar(&Param.ServiceCode, "serviceCode", "", "service code")
}

// ./environment repair --environment=1 --category=BOS --operate=check serviceVersion serviceImage

var repairCmd = &cobra.Command{
	Use:   "repair",
	Short: "repair runtime config",
	Run: func(cmd *cobra.Command, args []string) {
		// 初始化日志
		log.InitLog()
		os.Setenv("DEBUG", "1")

		// filter := stack.RuntimeConfigFilter{
		// 	EnvironmentID: Param.EnvironmentID,
		// 	Category:      Param.Category,
		// 	Entity:        Param.ServiceCode,
		// 	PathFilter:    args,
		// }

		// records, err := handler.GetRuntimeConfigByFilter(nil, &filter)
		// if err != nil {
		// 	log.Errorf("GetRuntimeConfigByFilter fail,as[%s]", err.Error())
		// 	return
		// }

		// for _, record := range records {
		// 	CheckRuntimeConfig2(record, Param.Operate, Param.TenantID)
		// }

		filter2 := stack.ServiceInstanceFilter{
			TenantID:      Param.TenantID,
			EnvironmentID: Param.EnvironmentID,
			ServiceType:   Param.Category,
			Code:          Param.ServiceCode,
		}
		filter2.Ctx = context.TODO()
		services, err := handler.GetServiceInstanceInfoList(&filter2)
		if err != nil {
			log.Errorf(context.TODO(), "GetLastServiceInstance for [%s][%s]fail,as[%s]", Param.Category, Param.ServiceCode, err.Error())
			return
		}

		if services == nil {
			log.Infof(context.TODO(), "the service[%s] no service instance exist", Param.ServiceCode)
			return
		}

		sm := make(map[string]stack.ServiceInstanceInfo, 0)
		for _, service := range services {
			s, ok := sm[service.Code]
			if ok {
				if s.ID < service.ID {
					sm[service.Code] = service
				}
			} else {
				sm[service.Code] = service
			}
		}

		if len(args) == 0 {
			args = []string{
				"serviceLogLevel",
				"podReplicas",
				"serviceImage",
				"serviceVersion",
				"configContent",
			}
		}
		types := map[string]string{"DAS": "DAS", "BTS": "BTS", "BOS": "BOS", "MAS": "MAS"}
		for _, service := range sm {
			_, ok := types[service.ServiceType]
			if service.CurrentVersionID > 0 && ok {
				CheckRuntimeConfig(service, args, Param.Operate)
			}
		}
	},
}

func CheckRuntimeConfig(service stack.ServiceInstanceInfo, paths []string, operate string) {
	ctx := context.TODO()
	versionData, err := handler.GetServiceInstanceVersion(service.CurrentVersionID, ctx)
	if err != nil {
		log.Errorf(context.TODO(), "GetServiceInstanceVersion for [%s][%s]fail,as[%s]", service.ServiceType, service.ServiceId, err.Error())
		return
	}

	// get runtime configure
	filter := stack.RuntimeConfigFilter{
		EnvironmentID: service.EnvironmentID,
		Category:      service.ServiceType,
		Entity:        service.ServiceId,
		EntityType:    "Service",
		PathList:      paths,
	}

	records, err := handler.GetRuntimeConfigByFilter(nil, &filter)
	if err != nil {
		log.Errorf(context.TODO(), "GetRuntimeConfigByFilter fail,as[%s]", err.Error())
		return
	}
	rdm := make(map[string]stack.RuntimeConfig, 0)
	for _, r := range records {
		rdm[r.Path] = r
	}

	for _, arg := range paths {
		value, ok := versionData.Data[arg]
		if !ok {
			continue
		}
		if len(fmt.Sprint(value)) == 0 {
			continue
		}
		record, ok := rdm[arg]
		upd := 0
		if !ok {
			log.Errorf(context.TODO(), "the service[%s][%s] property[%s] value[%s]is not exist as the service instance[%s]", service.ServiceType, service.ServiceId, arg, arg, value)
			upd = 1
		} else {
			if value == record.Value {
				// log.Infof("the property[%s] value[%s]is the same as the service instance", record.Path, value)
				continue
			} else {
				log.Errorf(context.TODO(), "the service[%s][%s] property[%s] value[%s]is not the same as the service instance[%s]", service.ServiceType, service.ServiceId, record.Path, record.Path, value)
				upd = 2
			}
		}

		if operate == "update" && upd > 0 {
			record.Value = fmt.Sprint(value)
			mgr := handler.NewRuntimeConfigMgr(&record, ctx)
			if upd == 2 {
				err = mgr.Update("value")
			} else if upd == 1 {
				record.Entity = service.ServiceId
				record.EntityType = "Service"
				record.Category = service.ServiceType
				record.EntityName = service.ServiceId
				record.EnvironmentID = service.EnvironmentID
				record.SuTypeCode = service.SuTypeCode
				record.Path = arg
				record.Label = arg
				record.TenantID = service.TenantID
				err = mgr.Insert()
			}
			if err != nil {
				log.Errorf(context.TODO(), "the runtime config [%s][%s][%s] value is update fail,as[%s]", record.Entity, record.Category, record.Path, err.Error())
			} else {
				// log.Infof("the runtime config [%s][%s][%s] value is update to [%s]", record.Entity, record.Category, record.Path, value)
			}
		}
	}
}

func CheckRuntimeConfig2(record stack.RuntimeConfig, operate, tenantID string) {
	filter := stack.ServiceInstanceFilter{
		TenantID:      tenantID,
		EnvironmentID: record.EnvironmentID,
		ServiceType:   record.Category,
		Code:          record.Entity,
	}
	ctx := context.TODO()
	filter.Ctx = ctx
	instance, err := handler.GetLastServiceInstance(&filter)
	if err != nil {
		log.Errorf(ctx, "GetLastServiceInstance for [%s][%s]fail,as[%s]", record.Category, record.Entity, err.Error())
		return
	}

	if instance == nil {
		log.Infof(ctx, "the service[%s] no service instance exist", record.Entity)
		return
	}

	versionData, err := handler.GetServiceInstanceVersion(instance.CurrentVersionID, ctx)
	if err != nil {
		log.Errorf(ctx, "GetServiceInstanceVersion for [%s][%s]fail,as[%s]", record.Category, record.Entity, err.Error())
		return
	}

	value, exist := utils.Configure(versionData.Data).GetString(record.Path)
	if !exist {
		log.Errorf(ctx, "the property [%s] not exist", record.Path)
		return
	}

	if value == record.Value {
		log.Infof(ctx, "the property[%s] value[%s]is the same as the service instance", record.Path, value)
		return
	} else {
		log.Errorf(ctx, "the property[%s] value[%s]is not the same as the service instance[%s]", record.Path, record.Path, value)
	}

	if operate == "update" {
		record.Value = value
		mgr := handler.NewRuntimeConfigMgr(&record, ctx)
		err = mgr.Update("value")
		if err != nil {
			log.Errorf(ctx, "the runtime config [%s][%s][%s] value is update fail,as[%s]", record.Entity, record.Category, record.Path, err.Error())
		} else {
			log.Infof(ctx, "the runtime config [%s][%s][%s] value is update to [%s]", record.Entity, record.Category, record.Path, value)
		}
	}
}
