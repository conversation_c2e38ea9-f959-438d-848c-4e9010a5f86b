package command

import (
	"fmt"
	"os"

	"github.com/mitchellh/go-homedir"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var (
	cfgFile string
)

var RootCmd = &cobra.Command{
	Use: "env",
}

func init() {
	cobra.OnInitialize(initConfig)
	// Use config.yaml configuration file by default
	RootCmd.PersistentFlags().StringVar(&cfgFile, "config", "./conf/config.yaml", "config file")
}

func Execute() {
	RootCmd.AddCommand(versionCmd) // show version
	RootCmd.AddCommand(apiCmd)     //
	RootCmd.AddCommand(solaceCmd)  //
	RootCmd.AddCommand(eventCmd)   //
	RootCmd.AddCommand(repairCmd)  //
	if err := RootCmd.Execute(); err != nil {
		fmt.Printf("root command execute fail,as: %s", err.Error())
	}
}

func initConfig() {
	if cfgFile != "" {
		viper.SetConfigFile(cfgFile)
	} else {
		// Find home directory.
		home, err := homedir.Dir()
		if err != nil {
			fmt.Println(err)
			os.Exit(1)
		}

		viper.AddConfigPath(home + "./conf")
		viper.SetConfigName("config")
	}

	// Try to read the configuration, if the read fails,
	// exit directly to avoid various errors
	if err := viper.ReadInConfig(); err != nil {
		fmt.Println("Can't read config:", err)
		os.Exit(1)
	}
}
