package command

import (
	"fmt"
	"os"
	"os/signal"

	"git.multiverse.io/eventkit/kit/handler/config/factory"
	"git.multiverse.io/framework/common/util"

	"git.platform.io/environment/environment/cache"
	"git.platform.io/environment/environment/database"

	// "git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/event"
	"git.platform.io/environment/environment/handler"
	"git.platform.io/resource/common/log"
	"github.com/spf13/cobra"
)

// launcher service without swagger doc

var solaceCmd = &cobra.Command{
	Use:   "sed",
	Short: "as solace client work",
	Run: func(cmd *cobra.Command, args []string) {
		// 初始化日志
		log.InitLog()
		os.Setenv("DEBUG", "1")
		// Database Initialization Check
		if err := database.InitDatabase(); err != nil {
			fmt.Printf("InitDatabase fail,as: %s", err.Error())
			return
		}

		// Initialize handler
		if err := handler.InitAllHandler(); err != nil {
			fmt.Printf("InitAllHandler fail,as: %s", err.Error())
			return
		}

		// Initialize gitlab
		// env gitlab init config
		// configs, err := factory.InitHandlerConfig("./conf/config.yaml")
		_, err := factory.InitHandlerConfig("./conf/config.yaml")
		if nil != err {
			return
		}
		// init redis
		err = cache.InitRedis()
		if err != nil {
			fmt.Printf("InitRedis fail: %s", err)
			return
		}
		// e := gitlabdao.Init(configs)
		// if e != nil {
		// 	log.Error(e)
		// 	fmt.Printf("Gitlab initialization failed, please check gitlab.url or gitlab.token. as: %s", e.Error())
		// 	return
		// }

		processor := event.NewMessageProcessor()

		// start event loop
		if err := event.Start(processor); err != nil {
			fmt.Printf("InitDatabase fail,as: %s", err.Error())
			return
		}

		// rapm心跳
		// go event.SendHBMsg()

		if util.EnvDefaultInt("DEBUG", 0) == 0 {
			if err := os.Remove("./conf/config.yaml"); err != nil {
				fmt.Printf("delete secret fail fail,as: %s", err.Error())
			}
		}

		//  hold the main process
		quit := make(chan os.Signal, 1)
		signal.Notify(quit, os.Interrupt)
		<-quit
		fmt.Printf("Shutdown Server ...")
	},
}
