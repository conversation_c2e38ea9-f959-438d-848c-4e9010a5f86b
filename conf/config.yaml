service:
  port: 8001
  mode: release
  version: 182-dev
log:
  path: ./data/logs
  file: environment.log
  level: debug
  maxSize: 200
  maxDays: 7
mysql:
  username: opsroot
  password: Mdn-y3vZ-iZ2un9
  address: ************
  port: 3306
  db: environment
  maxOpenCount: 20
  idleOpenCount: 10
  charset: UTF8
  logger: false
  debug: true
db:
  type: mysql
sed:
  timeout: 60
  address: http://127.0.0.1:16000
  port: 18091
  version: 0.2.8
gitlab:
  private_token: ********************
  url: **********
  group: ""
  timeout: 3
  listorg_topic: "ORG00001"
  listcicdtool_topic: "ORG00001"
redis:
  type: standalone
  addr: **********:37000
  password: SiriUs007
template:
  systemTmpName: "system-template"
  k8sVersions: ["1.24", "1.29", "1.30"]
route:
  aig:
    wso2IncludeFields:  "enableApiKeyAuthentication,trafficControl,maxAPIRequest,downstreamType,downstreamEndpoint"
    muExcludeFields:  "remoteTypeForHttp,httpEndpoint,enableApiKeyAuthentication"
    supportRemoteHttpStack: "155,152"
  aog:
    muExcludeFields: "remoteTypeForEvent"
