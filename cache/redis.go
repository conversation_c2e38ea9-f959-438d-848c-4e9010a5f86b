package cache

import (
	"context"
	"fmt"
	"strings"

	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
)

var (
	Client            redis.UniversalClient
	deployTypeSingle  = "standalone"
	deployTypeCluster = "cluster"
)

func InitRedis() error {
	redisType := viper.GetString("redis.type")
	addr := viper.GetString("redis.addr")
	password := viper.GetString("redis.password")
	if redisType == deployTypeSingle {
		Client = redis.NewClient(&redis.Options{
			Addr:     addr,
			Password: password,
		})
	} else if redisType == deployTypeCluster {
		addrs := strings.Split(addr, ",")
		Client = redis.NewClusterClient(&redis.ClusterOptions{
			Addrs:    addrs,
			Password: password,
		})
	} else {
		return fmt.Errorf("invalid deploy type[%s]", redisType)
	}

	return Client.Ping(context.Background()).Err()
}
