
# **function - errors - API**

## URL: /environment/v1/function/errors ##

## Method : Post ##

## Event ID : EnvFunctionErrors ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| idList | array[uint64] |  | N | gt=0,dive,gt=0 |

**Range of values**

request example:
```
{
    "idList": [
    ]
}
```

**struct example:**

```
type QueryRouteErrorsRequest struct {
    IdList []uint64 `json:"idList" validate:"gt=0,dive,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| routeId | integer | N |  |
| name | string | N |  |
| code | string | N |  |
| path | string | N |  |
| services | array[ServiceInfo] | N |  |
| ServiceInfo.serviceUUID | string | N |  |
| ServiceInfo.serviceName | string | N |  |
| ServiceInfo.serviceCode | string | N |  |
| ServiceInfo.serviceType | string | N |  |
| ServiceInfo.serviceInstanceId | integer | N |  |
| ServiceInfo.order | integer | N |  |
| ServiceInfo.planId | integer | N |  |
| ServiceInfo.suTypeCode | string | N |  |
| ServiceInfo.suInstanceId | integer | N |  |
| ServiceInfo.networkZoneId | integer | N |  |
| ServiceInfo.runtimeConfig | string | N |  |
| ServiceInfo.runningMode | string | N |  |
| ServiceInfo.journal | string | N |  |
| ServiceInfo.description | string | N |  |

**Range of values**

response example:

```
{
    "routeId": 0,
    "name": "name",
    "code": "code",
    "path": "path",
    "services": [
        {
            "serviceUUID": "serviceUUID",
            "serviceName": "serviceName",
            "serviceCode": "serviceCode",
            "serviceType": "serviceType",
            "serviceInstanceId": 0,
            "order": 0,
            "planId": 0,
            "suTypeCode": "suTypeCode",
            "suInstanceId": 0,
            "networkZoneId": 0,
            "runtimeConfig": "runtimeConfig",
            "runningMode": "runningMode",
            "journal": "journal",
            "description": "description"
        }
    ]
}
```



# **function - login - API**

## URL: /environment/v1/function/login ##

## Method : Post ##

## Event ID : EnvFunctionLogin ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| gatewayId | integer |  | N | gt=0 |
| routeIdList | array[uint64] |  | N | gt=0,dive,gt=0 |

**Range of values**

request example:
```
{
    "gatewayId": 0,
    "routeIdList": [
    ]
}
```

**struct example:**

```
type UpdateFunctionRequest struct {
    GatewayId uint64 `json:"gatewayId" validate:"gt=0"`
    RouteIdList []uint64 `json:"routeIdList" validate:"gt=0,dive,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **function - offline - API**

## URL: /environment/v1/function/offline ##

## Method : Post ##

## Event ID : EnvFunctionOffline ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| clusterCode | string |  | N | name,max=64 |
| event | string |  | N | name,max=64 |
| parameters | map[string]interface |  | Y | gte=0,dive,required |

**Range of values**

request example:
```
{
    "clusterCode": "clusterCode",
    "event": "event",
    "parameters": {
    }
}
```

**struct example:**

```
type OfflineFunctionRequest struct {
    ClusterCode string `json:"clusterCode" validate:"name,max=64"`
    Event string `json:"event" validate:"name,max=64"`
    Parameters map[string]interface{} `json:"parameters" validate:"gte=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **function - online - API**

## URL: /environment/v1/function/online ##

## Method : Post ##

## Event ID : EnvFunctionOnline ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| clusterCode | string |  | N | name,max=64 |
| event | string |  | N | name,max=64 |
| parameters | map[string]interface |  | Y | gte=0,dive,required |

**Range of values**

request example:
```
{
    "clusterCode": "clusterCode",
    "event": "event",
    "parameters": {
    }
}
```

**struct example:**

```
type OnlineFunctionRequest struct {
    ClusterCode string `json:"clusterCode" validate:"name,max=64"`
    Event string `json:"event" validate:"name,max=64"`
    Parameters map[string]interface{} `json:"parameters" validate:"gte=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **function - process - API**

## URL: /environment/v1/function/process ##

## Method : Post ##

## Event ID : EnvFunctionProcess ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | gt=0,required |
| envId | integer |  | Y | gt=0,required |
| action | string |  | Y | gt=0,required |
| routeId | integer |  | Y | gt=0,required |

**Range of values**

request example:
```
{
    "id": 0,
    "envId": 0,
    "action": "action",
    "routeId": 0
}
```

**struct example:**

```
type FunctionProcessParameter struct {
    Id uint64 `json:"id" validate:"gt=0,required"`
    EnvId uint64 `json:"envId" validate:"gt=0,required"`
    Action string `json:"action" validate:"gt=0,required"`
    RouteId uint64 `json:"routeId" validate:"gt=0,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **function - services - API**

## URL: /environment/v1/function/services ##

## Method : Post ##

## Event ID : EnvFunctionServices ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| routeId | integer |  | N | gt=0 |
| runningMode | string |  | N | omitempty,name,max=64 |
| serviceType | string |  | N | omitempty,name,max=64 |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "routeId": 0,
    "runningMode": "runningMode",
    "serviceType": "serviceType"
}
```

**struct example:**

```
type QueryRouteServicesRequest struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    RouteId uint64 `json:"routeId" validate:"gt=0"`
    RunningMode string `json:"runningMode" validate:"omitempty,name,max=64"`
    ServiceType string `json:"serviceType" validate:"omitempty,name,max=64"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **function - set-mode - API**

## URL: /environment/v1/function/set-mode ##

## Method : Post ##

## Event ID : EnvFunctionSetRunningMode ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| routeId | integer |  | N | gt=0 |
| services | map[string]string |  | Y | gt=0,dive,keys,required,endkeys,required |

**Range of values**

request example:
```
{
    "routeId": 0,
    "services": {
    }
}
```

**struct example:**

```
type SetRunningModeRequest struct {
    RouteId uint64 `json:"routeId" validate:"gt=0"`
    Services map[string]string `json:"services" validate:"gt=0,dive,keys,required,endkeys,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **function - update - API**

## URL: /environment/v1/function/update ##

## Method : Post ##

## Event ID : EnvFunctionUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| gatewayId | integer |  | N | gt=0 |
| routeIdList | array[uint64] |  | N | gt=0,dive,gt=0 |

**Range of values**

request example:
```
{
    "gatewayId": 0,
    "routeIdList": [
    ]
}
```

**struct example:**

```
type UpdateFunctionRequest struct {
    GatewayId uint64 `json:"gatewayId" validate:"gt=0"`
    RouteIdList []uint64 `json:"routeIdList" validate:"gt=0,dive,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


