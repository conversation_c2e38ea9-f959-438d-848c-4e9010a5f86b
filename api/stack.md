
# **stack - create - API**

## URL: /environment/v1/stack/create ##

## Method : Post ##

## Event ID : EnvStackCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| packageId | integer |  | N | omitempty,gte=0 |
| name | string |  | N | omitempty,max=64 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "packageId": 0,
    "name": "name",
    "description": "description"
}
```

**struct example:**

```
type CreateStackRequest struct {
    PackageId uint64 `json:"packageId" validate:"omitempty,gte=0"`
    Name string `json:"name" validate:"omitempty,max=64"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **stack - delete - API**

## URL: /environment/v1/stack/delete ##

## Method : Post ##

## Event ID : EnvStackDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type stackIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **stack - detail - API**

## URL: /environment/v1/stack/detail ##

## Method : Post ##

## Event ID : EnvStackDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type stackIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| workspaceId | string | N |  |
| packageId | integer | N |  |
| code | string | N |  |
| name | string | N |  |
| version | string | N |  |
| status | string | N |  |
| description | string | N |  |
| tenantId | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "workspaceId": "workspaceId",
    "packageId": 0,
    "code": "code",
    "name": "name",
    "version": "version",
    "status": "status",
    "description": "description",
    "tenantId": "tenantId",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **stack - list - API**

## URL: /environment/v1/stack/list ##

## Method : Post ##

## Event ID : EnvStackList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| code | string |  | N |  |
| tenantId | string |  | N |  |
| workspaceId | string |  | N |  |
| packageId | integer |  | N |  |
| stackName | string |  | N |  |
| packageName | string |  | N |  |
| environmentId | string |  | N |  |
| withoutPackage | boolean |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "code": "code",
    "tenantId": "tenantId",
    "workspaceId": "workspaceId",
    "packageId": 0,
    "stackName": "stackName",
    "packageName": "packageName",
    "environmentId": "environmentId",
    "withoutPackage": false
}
```

**struct example:**

```
type StackFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    Code string `json:"code"`
    TenantId string `json:"tenantId"`
    WorkspaceId string `json:"workspaceId"`
    PackageId uint64 `json:"packageId"`
    StackName string `json:"stackName"`
    PackageName string `json:"packageName"`
    EnvironmentId string `json:"environmentId"`
    WithoutPackage bool `json:"withoutPackage"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **stack - page - API**

## URL: /environment/v1/stack/page ##

## Method : Post ##

## Event ID : EnvStackPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| code | string |  | N |  |
| tenantId | string |  | N |  |
| workspaceId | string |  | N |  |
| packageId | integer |  | N |  |
| stackName | string |  | N |  |
| packageName | string |  | N |  |
| environmentId | string |  | N |  |
| withoutPackage | boolean |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "code": "code",
    "tenantId": "tenantId",
    "workspaceId": "workspaceId",
    "packageId": 0,
    "stackName": "stackName",
    "packageName": "packageName",
    "environmentId": "environmentId",
    "withoutPackage": false
}
```

**struct example:**

```
type StackFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    Code string `json:"code"`
    TenantId string `json:"tenantId"`
    WorkspaceId string `json:"workspaceId"`
    PackageId uint64 `json:"packageId"`
    StackName string `json:"stackName"`
    PackageName string `json:"packageName"`
    EnvironmentId string `json:"environmentId"`
    WithoutPackage bool `json:"withoutPackage"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **stack - update - API**

## URL: /environment/v1/stack/update ##

## Method : Post ##

## Event ID : EnvStackUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| stackId | integer |  | Y | required,gt=0 |
| name | string |  | N | omitempty,max=64 |
| status | string |  | N | name,max=64 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "stackId": 0,
    "name": "name",
    "status": "status",
    "description": "description"
}
```

**struct example:**

```
type UpdateStackRequest struct {
    StackId uint64 `json:"stackId" validate:"required,gt=0"`
    Name string `json:"name" validate:"omitempty,max=64"`
    Status string `json:"status" validate:"name,max=64"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


