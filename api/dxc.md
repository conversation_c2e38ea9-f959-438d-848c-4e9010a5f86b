
# **dxc - cluster deploy - API**

## URL: /environment/v1/dxc/cluster/deploy ##

## Method : Post ##

## Event ID : EnvDXCDeploy ##

**Description:**

create dxc cluster, create plan and deploy task

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| name | string |  | N | phrase,max=64 |
| batchId | integer |  | N | omitempty,gte=0 |
| versionId | integer |  | N | omitempty,gte=0 |
| version | string |  | N | omitempty,name,max=64 |
| stackId | integer |  | N | omitempty,gte=0 |
| environmentId | integer |  | Y | required,gt=0 |
| suId | integer |  | N | omitempty,gte=0 |
| suInstanceId | integer |  | Y | required,gt=0 |
| description | string |  | N | omitempty,max=255 |
| DeployDXCParameter | object |  | N |  |
| DeployDXCParameter.parameters | map[string]interface |  | Y | gte=0,dive,required |
| DeployDXCParameter.brokerVpnIdList | array[uint64] |  | Y | gt=0,dive,required |
| DeployDXCParameter.k8sClusterId | integer |  | N | omitempty,gt=0 |
| DeployDXCParameter.kvcClusterId | integer |  | N | omitempty,gte=0 |
| DeployDXCParameter.cacheType | string |  | N | name,max=64 |

**Range of values**

request example:
```
{
    "name": "name",
    "batchId": 0,
    "versionId": 0,
    "version": "version",
    "stackId": 0,
    "environmentId": 0,
    "suId": 0,
    "suInstanceId": 0,
    "description": "description",
    "DeployDXCParameter": {
        "parameters": {
        },
        "brokerVpnIdList": [
        ],
        "k8sClusterId": 0,
        "kvcClusterId": 0,
        "cacheType": "cacheType"
    }
}
```

**struct example:**

```
type DeployDXCRequest struct {
    Name string `json:"name" validate:"phrase,max=64"`
    BatchId uint64 `json:"batchId" validate:"omitempty,gte=0"`
    VersionId uint64 `json:"versionId" validate:"omitempty,gte=0"`
    Version string `json:"version" validate:"omitempty,name,max=64"`
    StackId uint64 `json:"stackId" validate:"omitempty,gte=0"`
    EnvironmentId uint64 `json:"environmentId" validate:"required,gt=0"`
    SuId uint64 `json:"suId" validate:"omitempty,gte=0"`
    SuInstanceId uint64 `json:"suInstanceId" validate:"required,gt=0"`
    Description string `json:"description" validate:"omitempty,max=255"`
    DeployDXCParameter struct {
        Parameters map[string]interface{} `json:"parameters" validate:"gte=0,dive,required"`
        BrokerVpnIdList []uint64 `json:"brokerVpnIdList" validate:"gt=0,dive,required"`
        K8sClusterId uint64 `json:"k8sClusterId" validate:"omitempty,gt=0"`
        KvcClusterId uint64 `json:"kvcClusterId" validate:"omitempty,gte=0"`
        CacheType string `json:"cacheType" validate:"name,max=64"`
    }
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **dxc - cluster destroy - API**

## URL: /environment/v1/dxc/cluster/destroy ##

## Method : Post ##

## Event ID : EnvDXCDestroy ##

**Description:**

destroy dxc cluster

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |

**Range of values**

request example:
```
{
}
```

**struct example:**

```
type Void struct {
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **dxc - cluster upgrade - API**

## URL: /environment/v1/dxc/cluster/upgrade ##

## Method : Post ##

## Event ID : EnvDXCUpgrade ##

**Description:**

upgrade dxc service instance

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |

**Range of values**

request example:
```
{
}
```

**struct example:**

```
type Void struct {
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


