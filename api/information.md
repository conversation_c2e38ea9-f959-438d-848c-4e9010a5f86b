
# **information - delete - API**

## URL: /environment/v1/information/delete ##

## Method : Post ##

## Event ID : EnvInfoDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| ciType | string |  | N |  |
| filter | map[string]interface |  | N |  |

**Range of values**

request example:
```
{
    "ciType": "ciType",
    "filter": {
    }
}
```

**struct example:**

```
type DeleteSpecDataParam struct {
    CiType string `json:"ciType"`
    Filter map[string]interface{} `json:"filter"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **information - insert - API**

## URL: /environment/v1/information/insert ##

## Method : Post ##

## Event ID : EnvInfoInsert ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| ciType | string |  | N |  |
| parameters | array[information] |  | N |  |

**Range of values**

request example:
```
{
    "ciType": "ciType",
    "parameters": [
        {
        }
    ]
}
```

**struct example:**

```
type InsertSpecDataParam struct {
    CiType string `json:"ciType"`
    Parameters []struct {
        } `json:"parameters"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **information - query - API**

## URL: /environment/v1/information/query ##

## Method : Post ##

## Event ID : EnvInfoQuery ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| ciType | string |  | N |  |
| tarCiType | array[string] |  | N |  |
| filter | map[string]interface |  | N |  |
| orderBy | array[string] |  | N |  |
| refDataMode | string |  | N |  |
| advFilter | array[AdvFilter] |  | N |  |
| AdvFilter.name | string |  | N |  |
| AdvFilter.values | array[string] |  | N |  |

**Range of values**

request example:
```
{
    "ciType": "ciType",
    "tarCiType": [
    ],
    "filter": {
    },
    "orderBy": [
    ],
    "refDataMode": "refDataMode",
    "advFilter": [
        {
            "name": "name",
            "values": [
            ]
        }
    ]
}
```

**struct example:**

```
type RefSpecDataQueryParam struct {
    CiType string `json:"ciType"`
    TarCiType []string `json:"tarCiType"`
    Filter map[string]interface{} `json:"filter"`
    OrderBy []string `json:"orderBy"`
    RefDataMode string `json:"refDataMode"`
    AdvFilter []struct {
            Name string `json:"name"`
            Values []string `json:"values"`
        } `json:"advFilter"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **information - update - API**

## URL: /environment/v1/information/update ##

## Method : Post ##

## Event ID : EnvInfoUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| ciType | string |  | N |  |
| parameters | map[string]interface |  | N |  |
| filter | map[string]interface |  | N |  |

**Range of values**

request example:
```
{
    "ciType": "ciType",
    "parameters": {
    },
    "filter": {
    }
}
```

**struct example:**

```
type UpdateSpecDataParam struct {
    CiType string `json:"ciType"`
    Parameters map[string]interface{} `json:"parameters"`
    Filter map[string]interface{} `json:"filter"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


