
# **job - delete - API**

## URL: /environment/v1/job/delete ##

## Method : Post ##

## Event ID : EnvJobDelete ##

**Description:**

delete a statistic job

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type idFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **job - list - API**

## URL: /environment/v1/job/list ##

## Method : Post ##

## Event ID : EnvJobList ##

**Description:**

query statistic job list

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| date | integer |  | N |  |
| dateRange | object |  | N |  |
| NumberRange.min | integer |  | N |  |
| NumberRange.max | integer |  | N |  |
| tenantId | string |  | N |  |
| status | string |  | N |  |
| statusList | array[string] |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "date": 0,
    "dateRange": {
        "min": 0,
        "max": 0
    },
    "tenantId": "tenantId",
    "status": "status",
    "statusList": [
    ]
}
```

**struct example:**

```
type JobFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    Date uint64 `json:"date"`
    DateRange struct {
        Min int64 `json:"min"`
        Max int64 `json:"max"`
    } `json:"dateRange"`
    TenantId string `json:"tenantId"`
    Status string `json:"status"`
    StatusList []string `json:"statusList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| jobId | integer | N |  |
| jobKey | string | N |  |
| type | string | N |  |
| startTime | integer | N |  |
| costTime | integer | N |  |
| finishTime | integer | N |  |
| progress | integer | N |  |
| currentStep | string | N |  |
| currentStatus | string | N |  |
| status | string | N |  |
| mode | string | N |  |
| journal | string | N |  |
| description | string | N |  |
| date | integer | N |  |
| tenantId | string | N |  |
| tenantCode | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "jobId": 0,
    "jobKey": "jobKey",
    "type": "type",
    "startTime": 0,
    "costTime": 0,
    "finishTime": 0,
    "progress": 0,
    "currentStep": "currentStep",
    "currentStatus": "currentStatus",
    "status": "status",
    "mode": "mode",
    "journal": "journal",
    "description": "description",
    "date": 0,
    "tenantId": "tenantId",
    "tenantCode": "tenantCode",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **job - page - API**

## URL: /environment/v1/job/page ##

## Method : Post ##

## Event ID : EnvJobPage ##

**Description:**

query statistic job page

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| date | integer |  | N |  |
| dateRange | object |  | N |  |
| NumberRange.min | integer |  | N |  |
| NumberRange.max | integer |  | N |  |
| tenantId | string |  | N |  |
| status | string |  | N |  |
| statusList | array[string] |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "date": 0,
    "dateRange": {
        "min": 0,
        "max": 0
    },
    "tenantId": "tenantId",
    "status": "status",
    "statusList": [
    ]
}
```

**struct example:**

```
type JobFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    Date uint64 `json:"date"`
    DateRange struct {
        Min int64 `json:"min"`
        Max int64 `json:"max"`
    } `json:"dateRange"`
    TenantId string `json:"tenantId"`
    Status string `json:"status"`
    StatusList []string `json:"statusList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```


