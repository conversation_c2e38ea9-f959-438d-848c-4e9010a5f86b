
# **su - create - API**

## URL: /environment/v1/su/create ##

## Method : Post ##

## Event ID : EnvSuCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| CreateSu | object |  | N |  |
| CreateSu.stackId | integer |  | N |  |
| CreateSu.envId | integer |  | N |  |
| CreateSu.suTypeId | integer |  | N |  |
| CreateSu.suTypeCode | string |  | N |  |
| CreateSu.name | string |  | N |  |
| CreateSu.code | string |  | N |  |
| CreateSu.description | string |  | N |  |
| CreateSu.weight | integer |  | N |  |
| CreateSu.shareQuota | integer |  | N |  |
| suList | array[CreateSu] |  | N |  |

**Range of values**

request example:
```
{
    "CreateSu": {
        "stackId": 0,
        "envId": 0,
        "suTypeId": 0,
        "suTypeCode": "suTypeCode",
        "name": "name",
        "code": "code",
        "description": "description",
        "weight": 0,
        "shareQuota": 0
    },
    "suList": [
        {
        }
    ]
}
```

**struct example:**

```
type CreateSuRequest struct {
    CreateSu struct {
        StackId uint64 `json:"stackId"`
        EnvId uint64 `json:"envId"`
        SuTypeId uint64 `json:"suTypeId"`
        SuTypeCode string `json:"suTypeCode"`
        Name string `json:"name"`
        Code string `json:"code"`
        Description string `json:"description"`
        Weight int `json:"weight"`
        ShareQuota int `json:"shareQuota"`
    }
    SuList []struct {
        } `json:"suList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **su - delete - API**

## URL: /environment/v1/su/delete ##

## Method : Post ##

## Event ID : EnvSuDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type suIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **su - detail - API**

## URL: /environment/v1/su/detail ##

## Method : Post ##

## Event ID : EnvSuDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type suIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| stackId | integer | N |  |
| envId | integer | N |  |
| suTypeId | integer | N |  |
| suTypeCode | string | N |  |
| name | string | N |  |
| code | string | N |  |
| weight | integer | N |  |
| shareQuota | integer | N |  |
| description | string | N |  |
| tenantId | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "stackId": 0,
    "envId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "name": "name",
    "code": "code",
    "weight": 0,
    "shareQuota": 0,
    "description": "description",
    "tenantId": "tenantId",
    "creatorId": "creatorId",
    "creator": "creator",
    "updaterId": "updaterId",
    "updater": "updater",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **su - list - API**

## URL: /environment/v1/su/list ##

## Method : Post ##

## Event ID : EnvSuList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| specId | integer |  | N |  |
| tenantId | string |  | N |  |
| environmentId | integer |  | N |  |
| suTypeId | integer |  | N |  |
| suTypeCode | string |  | N |  |
| suTypeIdList | array[uint64] |  | N |  |
| code | string |  | N |  |
| codeList | array[string] |  | N |  |
| name | string |  | N |  |
| suIdList | array[uint64] |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "specId": 0,
    "tenantId": "tenantId",
    "environmentId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "suTypeIdList": [
    ],
    "code": "code",
    "codeList": [
    ],
    "name": "name",
    "suIdList": [
    ]
}
```

**struct example:**

```
type SuFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    SpecId uint64 `json:"specId"`
    TenantId string `json:"tenantId"`
    EnvironmentId uint64 `json:"environmentId"`
    SuTypeId uint64 `json:"suTypeId"`
    SuTypeCode string `json:"suTypeCode"`
    SuTypeIdList []uint64 `json:"suTypeIdList"`
    Code string `json:"code"`
    CodeList []string `json:"codeList"`
    Name string `json:"name"`
    SuIdList []uint64 `json:"suIdList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **su - page - API**

## URL: /environment/v1/su/page ##

## Method : Post ##

## Event ID : EnvSuPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| specId | integer |  | N |  |
| tenantId | string |  | N |  |
| environmentId | integer |  | N |  |
| suTypeId | integer |  | N |  |
| suTypeCode | string |  | N |  |
| suTypeIdList | array[uint64] |  | N |  |
| code | string |  | N |  |
| codeList | array[string] |  | N |  |
| name | string |  | N |  |
| suIdList | array[uint64] |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "specId": 0,
    "tenantId": "tenantId",
    "environmentId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "suTypeIdList": [
    ],
    "code": "code",
    "codeList": [
    ],
    "name": "name",
    "suIdList": [
    ]
}
```

**struct example:**

```
type SuFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    SpecId uint64 `json:"specId"`
    TenantId string `json:"tenantId"`
    EnvironmentId uint64 `json:"environmentId"`
    SuTypeId uint64 `json:"suTypeId"`
    SuTypeCode string `json:"suTypeCode"`
    SuTypeIdList []uint64 `json:"suTypeIdList"`
    Code string `json:"code"`
    CodeList []string `json:"codeList"`
    Name string `json:"name"`
    SuIdList []uint64 `json:"suIdList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **su - update - API**

## URL: /environment/v1/su/update ##

## Method : Post ##

## Event ID : EnvSuUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |
| stackId | integer |  | N |  |
| suTypeId | integer |  | N |  |
| suTypeCode | string |  | N |  |
| name | string |  | N |  |
| code | string |  | N |  |
| description | string |  | N |  |
| weight | integer |  | N |  |
| shareQuota | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0,
    "stackId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "name": "name",
    "code": "code",
    "description": "description",
    "weight": 0,
    "shareQuota": 0
}
```

**struct example:**

```
type UpdateSuRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
    StackId uint64 `json:"stackId"`
    SuTypeId uint64 `json:"suTypeId"`
    SuTypeCode string `json:"suTypeCode"`
    Name string `json:"name"`
    Code string `json:"code"`
    Description string `json:"description"`
    Weight int `json:"weight"`
    ShareQuota int `json:"shareQuota"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


