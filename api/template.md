
# **template - content - API**

## URL: /environment/v1/template/content ##

## Method : Post ##

## Event ID : EnvTemplateContent ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type templateIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **template - detail - API**

## URL: /environment/v1/template/detail ##

## Method : Post ##

## Event ID : EnvTemplateDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type templateIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| envId | integer | N |  |
| name | string | N |  |
| description | string | N |  |
| tag | string | N |  |
| version | string | N |  |
| versions | array[string] | N |  |
| namespace | string | N |  |
| harborDomain | string | N |  |
| type | string | N |  |
| userName | string | N |  |
| tenantCode | string | N |  |
| tenantId | string | N |  |
| permission | string | N |  |
| applications | array[ApplicationInfo] | N |  |
| ApplicationInfo.ApplicationBaseInfo | object | N |  |
| ApplicationBaseInfo.id | integer | N |  |
| ApplicationBaseInfo.envId | integer | N |  |
| ApplicationBaseInfo.packageId | integer | N |  |
| ApplicationBaseInfo.index | integer | N |  |
| ApplicationBaseInfo.userName | string | N |  |
| ApplicationBaseInfo.tenantCode | string | N |  |
| ApplicationBaseInfo.tenantId | string | N |  |
| ApplicationBaseInfo.name | string | N |  |
| ApplicationBaseInfo.releaseName | string | N |  |
| ApplicationBaseInfo.releaseNames | array[string] | N |  |
| ApplicationBaseInfo.deployType | string | N |  |
| ApplicationBaseInfo.instanceId | integer | N |  |
| ApplicationBaseInfo.dbName | array[string] | N |  |
| ApplicationBaseInfo.dbAccount | string | N |  |
| ApplicationBaseInfo.dbPassword | string | N |  |
| ApplicationBaseInfo.meshRole | string | N |  |
| ApplicationBaseInfo.svrType | string | N |  |
| ApplicationBaseInfo.necessary | boolean | N |  |
| ApplicationBaseInfo.skipMessage | string | N |  |
| ApplicationBaseInfo.labels | array[LabelInfo] | N |  |
| LabelInfo.id | integer | N |  |
| LabelInfo.appId | integer | N |  |
| LabelInfo.label | string | N |  |
| LabelInfo.value | string | N |  |
| LabelInfo.rule | string | N |  |
| LabelInfo.defaultNode | string | N |  |
| ApplicationBaseInfo.version | string | N |  |
| ApplicationBaseInfo.apolloAppId | string | N |  |
| ApplicationBaseInfo.description | string | N |  |
| ApplicationInfo.k8sParams | string | N |  |
| ApplicationInfo.apolloParams | string | N |  |
| ApplicationInfo.metadata | object | N |  |
| Metadata.name | string | N |  |
| Metadata.home | string | N |  |
| Metadata.sources | array[string] | N |  |
| Metadata.version | string | N |  |
| Metadata.description | string | N |  |
| Metadata.keywords | array[string] | N |  |
| Metadata.maintainers | array[Maintainer] | N |  |
| Maintainer.name | string | N |  |
| Maintainer.email | string | N |  |
| Maintainer.url | string | N |  |
| Metadata.engine | string | N |  |
| Metadata.icon | string | N |  |
| Metadata.apiVersion | string | N |  |
| Metadata.condition | string | N |  |
| Metadata.tags | string | N |  |
| Metadata.appVersion | string | N |  |
| Metadata.deprecated | boolean | N |  |
| Metadata.tillerVersion | string | N |  |
| Metadata.annotations | map[string]string | N |  |
| Metadata.kubeVersion | string | N |  |
| ApplicationInfo.templates | array[Template] | N |  |
| Template.name | string | N |  |
| Template.data | string | N |  |
| ApplicationInfo.values | object | N |  |
| Config.raw | string | N |  |
| Config.values | map[string]string | N |  |
| ApplicationInfo.files | array[Any] | N |  |
| Any.type_url | string | N |  |
| Any.value | array[uint8] | N |  |
| ApplicationInfo.dependencies | array[uint64] | N |  |
| dependencies | array[ServiceInfo] | N |  |
| ServiceInfo.name | string | N |  |
| ServiceInfo.address | string | N |  |
| ServiceInfo.description | string | N |  |
| ports | array[uint64] | N |  |
| images | array[string] | N |  |
| resource | object | N |  |
| ResourceQuata.cpu | integer | N |  |
| ResourceQuata.memory | string | N |  |
| ResourceQuata.disk | string | N |  |
| summary | map[string]string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "envId": 0,
    "name": "name",
    "description": "description",
    "tag": "tag",
    "version": "version",
    "versions": [
    ],
    "namespace": "namespace",
    "harborDomain": "harborDomain",
    "type": "type",
    "userName": "userName",
    "tenantCode": "tenantCode",
    "tenantId": "tenantId",
    "permission": "permission",
    "applications": [
        {
            "ApplicationBaseInfo": {
                "id": 0,
                "envId": 0,
                "packageId": 0,
                "index": 0,
                "userName": "userName",
                "tenantCode": "tenantCode",
                "tenantId": "tenantId",
                "name": "name",
                "releaseName": "releaseName",
                "releaseNames": [
                ],
                "deployType": "deployType",
                "instanceId": 0,
                "dbName": [
                ],
                "dbAccount": "dbAccount",
                "dbPassword": "dbPassword",
                "meshRole": "meshRole",
                "svrType": "svrType",
                "necessary": false,
                "skipMessage": "skipMessage",
                "labels": [
                    {
                        "id": 0,
                        "appId": 0,
                        "label": "label",
                        "value": "value",
                        "rule": "rule",
                        "defaultNode": "defaultNode"
                    }
                ],
                "version": "version",
                "apolloAppId": "apolloAppId",
                "description": "description"
            },
            "k8sParams": "k8sParams",
            "apolloParams": "apolloParams",
            "metadata": {
                "name": "name",
                "home": "home",
                "sources": [
                ],
                "version": "version",
                "description": "description",
                "keywords": [
                ],
                "maintainers": [
                    {
                        "name": "name",
                        "email": "email",
                        "url": "url"
                    }
                ],
                "engine": "engine",
                "icon": "icon",
                "apiVersion": "apiVersion",
                "condition": "condition",
                "tags": "tags",
                "appVersion": "appVersion",
                "deprecated": false,
                "tillerVersion": "tillerVersion",
                "annotations": {
                },
                "kubeVersion": "kubeVersion"
            },
            "templates": [
                {
                    "name": "name",
                    "data": "data"
                }
            ],
            "values": {
                "raw": "raw",
                "values": {
                }
            },
            "files": [
                {
                    "type_url": "type_url",
                    "value": [
                    ]
                }
            ],
            "dependencies": [
            ]
        }
    ],
    "dependencies": [
        {
            "name": "name",
            "address": "address",
            "description": "description"
        }
    ],
    "ports": [
    ],
    "images": [
    ],
    "resource": {
        "cpu": 0,
        "memory": "memory",
        "disk": "disk"
    },
    "summary": {
    }
}
```



# **template - download - API**

## URL: /environment/v1/template/download ##

## Method : Post ##

## Event ID : EnvTemplateDownload ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type templateIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **template - headerlist - API**

## URL: /environment/v1/template/headerlist ##

## Method : Post ##

## Event ID : EnvTemplateHeaderList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type templateIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **template - list - API**

## URL: /environment/v1/template/list ##

## Method : Post ##

## Event ID : EnvTemplateList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| name | string |  | N | omitempty,max=64 |

**Range of values**

request example:
```
{
    "name": "name"
}
```

**struct example:**

```
type QueryStackListRequest struct {
    Name string `json:"name" validate:"omitempty,max=64"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| envId | integer | N |  |
| userName | string | N |  |
| userGroup | string | N |  |
| groupId | integer | N |  |
| tenantCode | string | N |  |
| tenantId | string | N |  |
| permission | string | N |  |
| name | string | N |  |
| description | string | N |  |
| version | string | N |  |
| imageVersion | string | N |  |
| versions | array[string] | N |  |
| type | string | N |  |
| namespace | string | N |  |
| harborDomain | string | N |  |
| cpu | integer | N |  |
| memory | integer | N |  |
| disk | integer | N |  |
| summary | map[string]string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "envId": 0,
    "userName": "userName",
    "userGroup": "userGroup",
    "groupId": 0,
    "tenantCode": "tenantCode",
    "tenantId": "tenantId",
    "permission": "permission",
    "name": "name",
    "description": "description",
    "version": "version",
    "imageVersion": "imageVersion",
    "versions": [
    ],
    "type": "type",
    "namespace": "namespace",
    "harborDomain": "harborDomain",
    "cpu": 0,
    "memory": 0,
    "disk": 0,
    "summary": {
    },
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **template - package - API**

## URL: /environment/v1/template/package ##

## Method : Post ##

## Event ID : EnvTemplatePackage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| envId | integer |  | Y | required |
| type | string |  | Y | required |
| version | string |  | N |  |

**Range of values**

request example:
```
{
    "envId": 0,
    "type": "type",
    "version": "version"
}
```

**struct example:**

```
type PackageRequest struct {
    EnvId uint64 `json:"envId" validate:"required"`
    Type string `json:"type" validate:"required"`
    Version string `json:"version"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| envId | integer | N |  |
| userName | string | N |  |
| userGroup | string | N |  |
| groupId | integer | N |  |
| tenantCode | string | N |  |
| tenantId | string | N |  |
| permission | string | N |  |
| name | string | N |  |
| description | string | N |  |
| version | string | N |  |
| imageVersion | string | N |  |
| harborDomain | string | N |  |
| versions | array[string] | N |  |
| type | string | N |  |
| namespace | string | N |  |
| cpu | integer | N |  |
| memory | integer | N |  |
| disk | integer | N |  |
| summary | map[string]string | N |  |
| services | array[Application] | N |  |
| Application.id | integer | N |  |
| Application.envId | integer | N |  |
| Application.packageId | integer | N |  |
| Application.userName | string | N |  |
| Application.userGroup | string | N |  |
| Application.groupId | integer | N |  |
| Application.tenantCode | string | N |  |
| Application.tenantId | string | N |  |
| Application.index | integer | N |  |
| Application.name | string | N |  |
| Application.releaseName | string | N |  |
| Application.releaseNames | array[string] | N |  |
| Application.deployType | string | N |  |
| Application.dbName | array[string] | N |  |
| Application.dbAccount | string | N |  |
| Application.dbPassword | string | N |  |
| Application.MeshRole | string | N |  |
| Application.svrType | string | N |  |
| Application.description | string | N |  |
| Application.necessary | integer | N |  |
| Application.skipMessage | string | N |  |
| Application.version | string | N |  |
| Application.apolloAppId | string | N |  |
| Application.k8sParams | string | N |  |
| Application.apolloParams | string | N |  |
| Application.metadata | string | N |  |
| Application.templates | string | N |  |
| Application.values | string | N |  |
| Application.files | string | N |  |
| Application.dependencies | string | N |  |
| Application.createAt | string | N |  |
| Application.updatedAt | string | N |  |
| Application.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "envId": 0,
    "userName": "userName",
    "userGroup": "userGroup",
    "groupId": 0,
    "tenantCode": "tenantCode",
    "tenantId": "tenantId",
    "permission": "permission",
    "name": "name",
    "description": "description",
    "version": "version",
    "imageVersion": "imageVersion",
    "harborDomain": "harborDomain",
    "versions": [
    ],
    "type": "type",
    "namespace": "namespace",
    "cpu": 0,
    "memory": 0,
    "disk": 0,
    "summary": {
    },
    "services": [
        {
            "id": 0,
            "envId": 0,
            "packageId": 0,
            "userName": "userName",
            "userGroup": "userGroup",
            "groupId": 0,
            "tenantCode": "tenantCode",
            "tenantId": "tenantId",
            "index": 0,
            "name": "name",
            "releaseName": "releaseName",
            "releaseNames": [
            ],
            "deployType": "deployType",
            "dbName": [
            ],
            "dbAccount": "dbAccount",
            "dbPassword": "dbPassword",
            "MeshRole": "MeshRole",
            "svrType": "svrType",
            "description": "description",
            "necessary": 0,
            "skipMessage": "skipMessage",
            "version": "version",
            "apolloAppId": "apolloAppId",
            "k8sParams": "k8sParams",
            "apolloParams": "apolloParams",
            "metadata": "metadata",
            "templates": "templates",
            "values": "values",
            "files": "files",
            "dependencies": "dependencies",
            "createAt": "createAt",
            "updatedAt": "updatedAt",
            "deletedAt": "deletedAt"
        }
    ]
}
```



# **template - refresh - API**

## URL: /environment/v1/template/refresh ##

## Method : Post ##

## Event ID : EnvTemplateRefresh ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type templateIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **template - services - API**

## URL: /environment/v1/template/services ##

## Method : Post ##

## Event ID : EnvTemplateServices ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N | omitempty |
| packageId | integer |  | N | gt=0 |
| tenantId | string |  | N | omitempty,max=64 |
| name | string |  | N | omitempty,max=64 |
| version | string |  | N | omitempty,max=64 |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "packageId": 0,
    "tenantId": "tenantId",
    "name": "name",
    "version": "version"
}
```

**struct example:**

```
type ApplicationFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id" validate:"omitempty"`
    PackageId uint64 `json:"packageId" validate:"gt=0"`
    TenantId string `json:"tenantId" validate:"omitempty,max=64"`
    Name string `json:"name" validate:"omitempty,max=64"`
    Version string `json:"version" validate:"omitempty,max=64"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **template - sql - API**

## URL: /environment/v1/template/sql ##

## Method : Post ##

## Event ID : EnvTemplateSql ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type templateIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| envId | integer | N |  |
| name | string | N |  |
| description | string | N |  |
| tag | string | N |  |
| version | string | N |  |
| versions | array[string] | N |  |
| namespace | string | N |  |
| harborDomain | string | N |  |
| type | string | N |  |
| userName | string | N |  |
| tenantCode | string | N |  |
| tenantId | string | N |  |
| permission | string | N |  |
| applications | array[ApplicationInfo] | N |  |
| ApplicationInfo.ApplicationBaseInfo | object | N |  |
| ApplicationBaseInfo.id | integer | N |  |
| ApplicationBaseInfo.envId | integer | N |  |
| ApplicationBaseInfo.packageId | integer | N |  |
| ApplicationBaseInfo.index | integer | N |  |
| ApplicationBaseInfo.userName | string | N |  |
| ApplicationBaseInfo.tenantCode | string | N |  |
| ApplicationBaseInfo.tenantId | string | N |  |
| ApplicationBaseInfo.name | string | N |  |
| ApplicationBaseInfo.releaseName | string | N |  |
| ApplicationBaseInfo.releaseNames | array[string] | N |  |
| ApplicationBaseInfo.deployType | string | N |  |
| ApplicationBaseInfo.instanceId | integer | N |  |
| ApplicationBaseInfo.dbName | array[string] | N |  |
| ApplicationBaseInfo.dbAccount | string | N |  |
| ApplicationBaseInfo.dbPassword | string | N |  |
| ApplicationBaseInfo.meshRole | string | N |  |
| ApplicationBaseInfo.svrType | string | N |  |
| ApplicationBaseInfo.necessary | boolean | N |  |
| ApplicationBaseInfo.skipMessage | string | N |  |
| ApplicationBaseInfo.labels | array[LabelInfo] | N |  |
| LabelInfo.id | integer | N |  |
| LabelInfo.appId | integer | N |  |
| LabelInfo.label | string | N |  |
| LabelInfo.value | string | N |  |
| LabelInfo.rule | string | N |  |
| LabelInfo.defaultNode | string | N |  |
| ApplicationBaseInfo.version | string | N |  |
| ApplicationBaseInfo.apolloAppId | string | N |  |
| ApplicationBaseInfo.description | string | N |  |
| ApplicationInfo.k8sParams | string | N |  |
| ApplicationInfo.apolloParams | string | N |  |
| ApplicationInfo.metadata | object | N |  |
| Metadata.name | string | N |  |
| Metadata.home | string | N |  |
| Metadata.sources | array[string] | N |  |
| Metadata.version | string | N |  |
| Metadata.description | string | N |  |
| Metadata.keywords | array[string] | N |  |
| Metadata.maintainers | array[Maintainer] | N |  |
| Maintainer.name | string | N |  |
| Maintainer.email | string | N |  |
| Maintainer.url | string | N |  |
| Metadata.engine | string | N |  |
| Metadata.icon | string | N |  |
| Metadata.apiVersion | string | N |  |
| Metadata.condition | string | N |  |
| Metadata.tags | string | N |  |
| Metadata.appVersion | string | N |  |
| Metadata.deprecated | boolean | N |  |
| Metadata.tillerVersion | string | N |  |
| Metadata.annotations | map[string]string | N |  |
| Metadata.kubeVersion | string | N |  |
| ApplicationInfo.templates | array[Template] | N |  |
| Template.name | string | N |  |
| Template.data | string | N |  |
| ApplicationInfo.values | object | N |  |
| Config.raw | string | N |  |
| Config.values | map[string]string | N |  |
| ApplicationInfo.files | array[Any] | N |  |
| Any.type_url | string | N |  |
| Any.value | array[uint8] | N |  |
| ApplicationInfo.dependencies | array[uint64] | N |  |
| dependencies | array[ServiceInfo] | N |  |
| ServiceInfo.name | string | N |  |
| ServiceInfo.address | string | N |  |
| ServiceInfo.description | string | N |  |
| ports | array[uint64] | N |  |
| images | array[string] | N |  |
| resource | object | N |  |
| ResourceQuata.cpu | integer | N |  |
| ResourceQuata.memory | string | N |  |
| ResourceQuata.disk | string | N |  |
| summary | map[string]string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "envId": 0,
    "name": "name",
    "description": "description",
    "tag": "tag",
    "version": "version",
    "versions": [
    ],
    "namespace": "namespace",
    "harborDomain": "harborDomain",
    "type": "type",
    "userName": "userName",
    "tenantCode": "tenantCode",
    "tenantId": "tenantId",
    "permission": "permission",
    "applications": [
        {
            "ApplicationBaseInfo": {
                "id": 0,
                "envId": 0,
                "packageId": 0,
                "index": 0,
                "userName": "userName",
                "tenantCode": "tenantCode",
                "tenantId": "tenantId",
                "name": "name",
                "releaseName": "releaseName",
                "releaseNames": [
                ],
                "deployType": "deployType",
                "instanceId": 0,
                "dbName": [
                ],
                "dbAccount": "dbAccount",
                "dbPassword": "dbPassword",
                "meshRole": "meshRole",
                "svrType": "svrType",
                "necessary": false,
                "skipMessage": "skipMessage",
                "labels": [
                    {
                        "id": 0,
                        "appId": 0,
                        "label": "label",
                        "value": "value",
                        "rule": "rule",
                        "defaultNode": "defaultNode"
                    }
                ],
                "version": "version",
                "apolloAppId": "apolloAppId",
                "description": "description"
            },
            "k8sParams": "k8sParams",
            "apolloParams": "apolloParams",
            "metadata": {
                "name": "name",
                "home": "home",
                "sources": [
                ],
                "version": "version",
                "description": "description",
                "keywords": [
                ],
                "maintainers": [
                    {
                        "name": "name",
                        "email": "email",
                        "url": "url"
                    }
                ],
                "engine": "engine",
                "icon": "icon",
                "apiVersion": "apiVersion",
                "condition": "condition",
                "tags": "tags",
                "appVersion": "appVersion",
                "deprecated": false,
                "tillerVersion": "tillerVersion",
                "annotations": {
                },
                "kubeVersion": "kubeVersion"
            },
            "templates": [
                {
                    "name": "name",
                    "data": "data"
                }
            ],
            "values": {
                "raw": "raw",
                "values": {
                }
            },
            "files": [
                {
                    "type_url": "type_url",
                    "value": [
                    ]
                }
            ],
            "dependencies": [
            ]
        }
    ],
    "dependencies": [
        {
            "name": "name",
            "address": "address",
            "description": "description"
        }
    ],
    "ports": [
    ],
    "images": [
    ],
    "resource": {
        "cpu": 0,
        "memory": "memory",
        "disk": "disk"
    },
    "summary": {
    }
}
```



# **template - stacks - API**

## URL: /environment/v1/template/stacks ##

## Method : Post ##

## Event ID : EnvTemplateStacks ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N | omitempty |
| groupId | integer |  | N | omitempty |
| tenant_id | string |  | N | omitempty |
| environmentID | integer |  | N | omitempty |
| name | string |  | N | omitempty,name,max=64 |
| type | string |  | N | omitempty,name,max=64 |
| version | string |  | N | omitempty,name,max=64 |
| createdAt | object |  | Y | required |
| TimeRange.from | string |  | N |  |
| TimeRange.to | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "groupId": 0,
    "tenant_id": "tenant_id",
    "environmentID": 0,
    "name": "name",
    "type": "type",
    "version": "version",
    "createdAt": {
        "from": "from",
        "to": "to"
    }
}
```

**struct example:**

```
type PackageFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id" validate:"omitempty"`
    GroupId uint64 `json:"groupId" validate:"omitempty"`
    Tenant_id string `json:"tenant_id" validate:"omitempty"`
    EnvironmentID uint64 `json:"environmentID" validate:"omitempty"`
    Name string `json:"name" validate:"omitempty,name,max=64"`
    Type string `json:"type" validate:"omitempty,name,max=64"`
    Version string `json:"version" validate:"omitempty,name,max=64"`
    CreatedAt struct {
        From string `json:"from"`
        To string `json:"to"`
    } `json:"createdAt" validate:"required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **template - template - API**

## URL: /environment/v1/template/template ##

## Method : Post ##

## Event ID : EnvTemplateTemplate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type templateIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| envId | integer | N |  |
| name | string | N |  |
| description | string | N |  |
| tag | string | N |  |
| version | string | N |  |
| versions | array[string] | N |  |
| namespace | string | N |  |
| harborDomain | string | N |  |
| type | string | N |  |
| userName | string | N |  |
| tenantCode | string | N |  |
| tenantId | string | N |  |
| permission | string | N |  |
| applications | array[ApplicationInfo] | N |  |
| ApplicationInfo.ApplicationBaseInfo | object | N |  |
| ApplicationBaseInfo.id | integer | N |  |
| ApplicationBaseInfo.envId | integer | N |  |
| ApplicationBaseInfo.packageId | integer | N |  |
| ApplicationBaseInfo.index | integer | N |  |
| ApplicationBaseInfo.userName | string | N |  |
| ApplicationBaseInfo.tenantCode | string | N |  |
| ApplicationBaseInfo.tenantId | string | N |  |
| ApplicationBaseInfo.name | string | N |  |
| ApplicationBaseInfo.releaseName | string | N |  |
| ApplicationBaseInfo.releaseNames | array[string] | N |  |
| ApplicationBaseInfo.deployType | string | N |  |
| ApplicationBaseInfo.instanceId | integer | N |  |
| ApplicationBaseInfo.dbName | array[string] | N |  |
| ApplicationBaseInfo.dbAccount | string | N |  |
| ApplicationBaseInfo.dbPassword | string | N |  |
| ApplicationBaseInfo.meshRole | string | N |  |
| ApplicationBaseInfo.svrType | string | N |  |
| ApplicationBaseInfo.necessary | boolean | N |  |
| ApplicationBaseInfo.skipMessage | string | N |  |
| ApplicationBaseInfo.labels | array[LabelInfo] | N |  |
| LabelInfo.id | integer | N |  |
| LabelInfo.appId | integer | N |  |
| LabelInfo.label | string | N |  |
| LabelInfo.value | string | N |  |
| LabelInfo.rule | string | N |  |
| LabelInfo.defaultNode | string | N |  |
| ApplicationBaseInfo.version | string | N |  |
| ApplicationBaseInfo.apolloAppId | string | N |  |
| ApplicationBaseInfo.description | string | N |  |
| ApplicationInfo.k8sParams | string | N |  |
| ApplicationInfo.apolloParams | string | N |  |
| ApplicationInfo.metadata | object | N |  |
| Metadata.name | string | N |  |
| Metadata.home | string | N |  |
| Metadata.sources | array[string] | N |  |
| Metadata.version | string | N |  |
| Metadata.description | string | N |  |
| Metadata.keywords | array[string] | N |  |
| Metadata.maintainers | array[Maintainer] | N |  |
| Maintainer.name | string | N |  |
| Maintainer.email | string | N |  |
| Maintainer.url | string | N |  |
| Metadata.engine | string | N |  |
| Metadata.icon | string | N |  |
| Metadata.apiVersion | string | N |  |
| Metadata.condition | string | N |  |
| Metadata.tags | string | N |  |
| Metadata.appVersion | string | N |  |
| Metadata.deprecated | boolean | N |  |
| Metadata.tillerVersion | string | N |  |
| Metadata.annotations | map[string]string | N |  |
| Metadata.kubeVersion | string | N |  |
| ApplicationInfo.templates | array[Template] | N |  |
| Template.name | string | N |  |
| Template.data | string | N |  |
| ApplicationInfo.values | object | N |  |
| Config.raw | string | N |  |
| Config.values | map[string]string | N |  |
| ApplicationInfo.files | array[Any] | N |  |
| Any.type_url | string | N |  |
| Any.value | array[uint8] | N |  |
| ApplicationInfo.dependencies | array[uint64] | N |  |
| dependencies | array[ServiceInfo] | N |  |
| ServiceInfo.name | string | N |  |
| ServiceInfo.address | string | N |  |
| ServiceInfo.description | string | N |  |
| ports | array[uint64] | N |  |
| images | array[string] | N |  |
| resource | object | N |  |
| ResourceQuata.cpu | integer | N |  |
| ResourceQuata.memory | string | N |  |
| ResourceQuata.disk | string | N |  |
| summary | map[string]string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "envId": 0,
    "name": "name",
    "description": "description",
    "tag": "tag",
    "version": "version",
    "versions": [
    ],
    "namespace": "namespace",
    "harborDomain": "harborDomain",
    "type": "type",
    "userName": "userName",
    "tenantCode": "tenantCode",
    "tenantId": "tenantId",
    "permission": "permission",
    "applications": [
        {
            "ApplicationBaseInfo": {
                "id": 0,
                "envId": 0,
                "packageId": 0,
                "index": 0,
                "userName": "userName",
                "tenantCode": "tenantCode",
                "tenantId": "tenantId",
                "name": "name",
                "releaseName": "releaseName",
                "releaseNames": [
                ],
                "deployType": "deployType",
                "instanceId": 0,
                "dbName": [
                ],
                "dbAccount": "dbAccount",
                "dbPassword": "dbPassword",
                "meshRole": "meshRole",
                "svrType": "svrType",
                "necessary": false,
                "skipMessage": "skipMessage",
                "labels": [
                    {
                        "id": 0,
                        "appId": 0,
                        "label": "label",
                        "value": "value",
                        "rule": "rule",
                        "defaultNode": "defaultNode"
                    }
                ],
                "version": "version",
                "apolloAppId": "apolloAppId",
                "description": "description"
            },
            "k8sParams": "k8sParams",
            "apolloParams": "apolloParams",
            "metadata": {
                "name": "name",
                "home": "home",
                "sources": [
                ],
                "version": "version",
                "description": "description",
                "keywords": [
                ],
                "maintainers": [
                    {
                        "name": "name",
                        "email": "email",
                        "url": "url"
                    }
                ],
                "engine": "engine",
                "icon": "icon",
                "apiVersion": "apiVersion",
                "condition": "condition",
                "tags": "tags",
                "appVersion": "appVersion",
                "deprecated": false,
                "tillerVersion": "tillerVersion",
                "annotations": {
                },
                "kubeVersion": "kubeVersion"
            },
            "templates": [
                {
                    "name": "name",
                    "data": "data"
                }
            ],
            "values": {
                "raw": "raw",
                "values": {
                }
            },
            "files": [
                {
                    "type_url": "type_url",
                    "value": [
                    ]
                }
            ],
            "dependencies": [
            ]
        }
    ],
    "dependencies": [
        {
            "name": "name",
            "address": "address",
            "description": "description"
        }
    ],
    "ports": [
    ],
    "images": [
    ],
    "resource": {
        "cpu": 0,
        "memory": "memory",
        "disk": "disk"
    },
    "summary": {
    }
}
```



# **template - upload - API**

## URL: /environment/v1/template/upload ##

## Method : Post ##

## Event ID : EnvTemplateUpload ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type templateIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **template - versions - API**

## URL: /environment/v1/template/versions ##

## Method : Post ##

## Event ID : EnvTemplateVersions ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type templateIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


