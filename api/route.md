
# **route - add - API**

## URL: /environment/v1/route/add ##

## Method : Post ##

## Event ID : EnvRouteAdd ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| PlanBase | object |  | N |  |
| PlanBase.RequestBase | object |  | N |  |
| RequestBase.ws | string |  | N | omitempty,name,max=32 |
| RequestBase.envId | integer |  | N | omitempty |
| RequestBase.env | string |  | N | omitempty,name,max=32 |
| PlanBase.batchId | integer |  | N | omitempty,gte=0 |
| PlanBase.versionId | integer |  | N | omitempty,gte=0 |
| PlanBase.stackId | integer |  | N | omitempty,gte=0 |
| gatewayId | integer |  | Y | required,gt=0 |
| upgrade | boolean |  | N | omitempty |
| apiUuidList | array[string] |  | Y | gte=0,dive,required |
| configure | map[string]interface |  | N | gte=0 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "PlanBase": {
        "RequestBase": {
            "ws": "ws",
            "envId": 0,
            "env": "env"
        },
        "batchId": 0,
        "versionId": 0,
        "stackId": 0
    },
    "gatewayId": 0,
    "upgrade": false,
    "apiUuidList": [
    ],
    "configure": {
    },
    "description": "description"
}
```

**struct example:**

```
type BatchOnlineRouteRequest struct {
    PlanBase struct {
        RequestBase struct {
            Ws string `json:"ws" validate:"omitempty,name,max=32"`
            EnvId  `json:"envId" validate:"omitempty"`
            Env string `json:"env" validate:"omitempty,name,max=32"`
        }
        BatchId uint64 `json:"batchId" validate:"omitempty,gte=0"`
        VersionId uint64 `json:"versionId" validate:"omitempty,gte=0"`
        StackId uint64 `json:"stackId" validate:"omitempty,gte=0"`
    }
    GatewayId uint64 `json:"gatewayId" validate:"required,gt=0"`
    Upgrade bool `json:"upgrade" validate:"omitempty"`
    ApiUuidList []string `json:"apiUuidList" validate:"gte=0,dive,required"`
    Configure map[string]interface{} `json:"configure" validate:"gte=0"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **route - delete - API**

## URL: /environment/v1/route/delete ##

## Method : Post ##

## Event ID : EnvRouteDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type routeIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **route - list - API**

## URL: /environment/v1/route/list ##

## Method : Post ##

## Event ID : EnvRouteList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| serviceInstanceId | integer |  | N | gt=0 |

**Range of values**

request example:
```
{
    "serviceInstanceId": 0
}
```

**struct example:**

```
type QueryRouteListRequest struct {
    ServiceInstanceId uint64 `json:"serviceInstanceId" validate:"gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| lastPatchId | integer | N |  |
| baseVersionId | integer | N |  |
| linkVersionId | integer | N |  |
| status | string | N |  |
| journal | string | N |  |
| data | map[string]interface | N |  |
| Route | object | N |  |
| Route.id | integer | N |  |
| Route.stackId | integer | N |  |
| Route.apiId | integer | N |  |
| Route.apiUuid | string | N |  |
| Route.eventUuid | string | N |  |
| Route.eventCode | string | N |  |
| Route.eventType | string | N |  |
| Route.proxyBy | integer | N |  |
| Route.currentVersionId | integer | N |  |
| Route.targetVersionId | integer | N |  |
| Route.latestVersionId | integer | N |  |
| Route.name | string | N |  |
| Route.code | string | N |  |
| Route.routeType | string | N |  |
| Route.category | string | N |  |
| Route.version | string | N |  |
| Route.path | string | N |  |
| Route.method | string | N |  |
| Route.configure | map[string]interface | N |  |
| Route.status | string | N |  |
| Route.targetStatus | string | N |  |
| Route.description | string | N |  |
| Route.tenantId | string | N |  |
| Route.creatorId | string | N |  |
| Route.creator | string | N |  |
| Route.updaterId | string | N |  |
| Route.updater | string | N |  |
| Route.createAt | string | N |  |
| Route.updatedAt | string | N |  |
| Route.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "lastPatchId": 0,
    "baseVersionId": 0,
    "linkVersionId": 0,
    "status": "status",
    "journal": "journal",
    "data": {
    },
    "Route": {
        "id": 0,
        "stackId": 0,
        "apiId": 0,
        "apiUuid": "apiUuid",
        "eventUuid": "eventUuid",
        "eventCode": "eventCode",
        "eventType": "eventType",
        "proxyBy": 0,
        "currentVersionId": 0,
        "targetVersionId": 0,
        "latestVersionId": 0,
        "name": "name",
        "code": "code",
        "routeType": "routeType",
        "category": "category",
        "version": "version",
        "path": "path",
        "method": "method",
        "configure": {
        },
        "status": "status",
        "targetStatus": "targetStatus",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **route - maintain - API**

## URL: /environment/v1/route/maintain ##

## Method : Post ##

## Event ID : EnvRouteMaintain ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| upgrade | boolean |  | N | omitempty |
| idList | array[uint64] |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "upgrade": false,
    "idList": [
    ]
}
```

**struct example:**

```
type BatchOperateRouteRequest struct {
    Upgrade bool `json:"upgrade" validate:"omitempty"`
    IdList []uint64 `json:"idList" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **route - offline - API**

## URL: /environment/v1/route/offline ##

## Method : Post ##

## Event ID : EnvRouteOffline ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| upgrade | boolean |  | N | omitempty |
| idList | array[uint64] |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "upgrade": false,
    "idList": [
    ]
}
```

**struct example:**

```
type BatchOperateRouteRequest struct {
    Upgrade bool `json:"upgrade" validate:"omitempty"`
    IdList []uint64 `json:"idList" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **route - online - API**

## URL: /environment/v1/route/online ##

## Method : Post ##

## Event ID : EnvRouteOnline ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| upgrade | boolean |  | N | omitempty |
| idList | array[uint64] |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "upgrade": false,
    "idList": [
    ]
}
```

**struct example:**

```
type BatchOperateRouteRequest struct {
    Upgrade bool `json:"upgrade" validate:"omitempty"`
    IdList []uint64 `json:"idList" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **route - page - API**

## URL: /environment/v1/route/page ##

## Method : Post ##

## Event ID : EnvRoutePage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| idList | array[uint64] |  | N |  |
| stackId | integer |  | N |  |
| workspaceId | integer |  | N |  |
| apiUuid | string |  | N |  |
| eventCode | string |  | N |  |
| gatewayId | integer |  | N |  |
| serviceInstanceId | integer |  | N |  |
| tenantId | string |  | N |  |
| name | string |  | N |  |
| code | string |  | N |  |
| category | string |  | N |  |
| status | string |  | N |  |
| targetStatus | string |  | N |  |
| targetStatusList | array[string] |  | N |  |
| statusList | array[string] |  | N |  |
| upgradeRequired | boolean |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "idList": [
    ],
    "stackId": 0,
    "workspaceId": 0,
    "apiUuid": "apiUuid",
    "eventCode": "eventCode",
    "gatewayId": 0,
    "serviceInstanceId": 0,
    "tenantId": "tenantId",
    "name": "name",
    "code": "code",
    "category": "category",
    "status": "status",
    "targetStatus": "targetStatus",
    "targetStatusList": [
    ],
    "statusList": [
    ],
    "upgradeRequired": false
}
```

**struct example:**

```
type RouteFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    IdList []uint64 `json:"idList"`
    StackId uint64 `json:"stackId"`
    WorkspaceId uint64 `json:"workspaceId"`
    ApiUuid string `json:"apiUuid"`
    EventCode string `json:"eventCode"`
    GatewayId uint64 `json:"gatewayId"`
    ServiceInstanceId uint64 `json:"serviceInstanceId"`
    TenantId string `json:"tenantId"`
    Name string `json:"name"`
    Code string `json:"code"`
    Category string `json:"category"`
    Status string `json:"status"`
    TargetStatus string `json:"targetStatus"`
    TargetStatusList []string `json:"targetStatusList"`
    StatusList []string `json:"statusList"`
    UpgradeRequired bool `json:"upgradeRequired"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| PageData | object | N |  |
| PageData.data | object | N |  |
| PageData.page | integer | N |  |
| PageData.pageSize | integer | N |  |
| PageData.totalCount | integer | N |  |
| needUpgradeNumber | integer | N |  |
| statusChangeNumber | integer | N |  |

**Range of values**

response example:

```
{
    "PageData": {
        "data": {
        },
        "page": 0,
        "pageSize": 0,
        "totalCount": 0
    },
    "needUpgradeNumber": 0,
    "statusChangeNumber": 0
}
```



# **route - patch - API**

## URL: /environment/v1/route/patch ##

## Method : Post ##

## Event ID : EnvRouteVersionPatch ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| version | string |  | N | omitempty,max=64 |
| note | string |  | N | omitempty,max=255 |
| services | array[uint64] |  | Y | gt=0,dive,required |
| patches | array[uint64] |  | Y | gt=0,dive,required |

**Range of values**

request example:
```
{
    "version": "version",
    "note": "note",
    "services": [
    ],
    "patches": [
    ]
}
```

**struct example:**

```
type PatchRouteRequest struct {
    Version string `json:"version" validate:"omitempty,max=64"`
    Note string `json:"note" validate:"omitempty,max=255"`
    Services []uint64 `json:"services" validate:"gt=0,dive,required"`
    Patches []uint64 `json:"patches" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **route - refresh - API**

## URL: /environment/v1/route/refresh ##

## Method : Post ##

## Event ID : EnvRouteRefresh ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| gatewayId | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "gatewayId": 0
}
```

**struct example:**

```
type OperateGatewayRequest struct {
    GatewayId uint64 `json:"gatewayId" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **route - update - API**

## URL: /environment/v1/route/update ##

## Method : Post ##

## Event ID : EnvRouteUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |
| upgrade | boolean |  | N | omitempty |
| configure | map[string]interface |  | N | gte=0,dive,keys,name |
| setting | map[string]interface |  | N | gte=0,dive,keys,name |

**Range of values**

request example:
```
{
    "id": 0,
    "upgrade": false,
    "configure": {
    },
    "setting": {
    }
}
```

**struct example:**

```
type UpdateRouteRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
    Upgrade bool `json:"upgrade" validate:"omitempty"`
    Configure map[string]interface{} `json:"configure" validate:"gte=0,dive,keys,name"`
    Setting map[string]interface{} `json:"setting" validate:"gte=0,dive,keys,name"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **route - update version - API**

## URL: /environment/v1/route/update/version ##

## Method : Post ##

## Event ID : EnvRouteUpdateVersion ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type routeIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **route - version delete - API**

## URL: /environment/v1/route/version/delete ##

## Method : Post ##

## Event ID : EnvRouteVersionDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type routeVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **route - version detail - API**

## URL: /environment/v1/route/version/detail ##

## Method : Post ##

## Event ID : EnvRouteVersionDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type routeVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| routeId | integer | N |  |
| lastPatchId | integer | N |  |
| baseVersionId | integer | N |  |
| linkVersionId | integer | N |  |
| status | string | N |  |
| journal | string | N |  |
| data | map[string]interface | N |  |
| version | string | N |  |
| description | string | N |  |
| tenantId | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "routeId": 0,
    "lastPatchId": 0,
    "baseVersionId": 0,
    "linkVersionId": 0,
    "status": "status",
    "journal": "journal",
    "data": {
    },
    "version": "version",
    "description": "description",
    "tenantId": "tenantId",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **route - version fields - API**

## URL: /environment/v1/route/version/fields ##

## Method : Post ##

## Event ID : EnvRouteVersionFields ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| instanceVersionId | integer |  | Y | required,gt=0 |
| sectionId | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "instanceVersionId": 0,
    "sectionId": 0
}
```

**struct example:**

```
type QueryRouteFieldRequest struct {
    InstanceVersionId uint64 `json:"instanceVersionId" validate:"required,gt=0"`
    SectionId uint64 `json:"sectionId" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| requestId | string | N |  |
| fields | array[FieldInfo] | N |  |
| FieldInfo.domain | string | N |  |
| FieldInfo.group | string | N |  |
| FieldInfo.label | string | N |  |
| FieldInfo.columnNote | string | N |  |
| FieldInfo.placeholder | string | N |  |
| FieldInfo.unit | string | N |  |
| FieldInfo.necessary | boolean | N |  |
| FieldInfo.columnPath | string | N |  |
| FieldInfo.type | string | N |  |
| FieldInfo.dataType | string | N |  |
| FieldInfo.columnValue | string | N |  |
| FieldInfo.min | number | N |  |
| FieldInfo.max | number | N |  |
| FieldInfo.readOnly | boolean | N |  |
| FieldInfo.hidden | boolean | N |  |
| FieldInfo.validate | string | N |  |
| FieldInfo.items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |
| FieldInfo.Field | object | N |  |
| Field.id | integer | N |  |
| Field.stackId | integer | N |  |
| Field.sectionId | integer | N |  |
| Field.columnId | string | N |  |
| Field.scope | integer | N |  |
| Field.note | string | N |  |
| Field.priority | integer | N |  |
| Field.path | string | N |  |
| Field.value | string | N |  |
| Field.tenantId | string | N |  |
| Field.updaterId | string | N |  |
| Field.updater | string | N |  |
| Field.creatorId | string | N |  |
| Field.creator | string | N |  |
| Field.createAt | string | N |  |
| Field.updatedAt | string | N |  |
| Field.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "requestId": "requestId",
    "fields": [
        {
            "domain": "domain",
            "group": "group",
            "label": "label",
            "columnNote": "columnNote",
            "placeholder": "placeholder",
            "unit": "unit",
            "necessary": false,
            "columnPath": "columnPath",
            "type": "type",
            "dataType": "dataType",
            "columnValue": "columnValue",
            "min": "min",
            "max": "max",
            "readOnly": false,
            "hidden": false,
            "validate": "validate",
            "items": [
                {
                    "value": "value",
                    "text": "text"
                }
            ],
            "Field": {
                "id": 0,
                "stackId": 0,
                "sectionId": 0,
                "columnId": "columnId",
                "scope": 0,
                "note": "note",
                "priority": 0,
                "path": "path",
                "value": "value",
                "tenantId": "tenantId",
                "updaterId": "updaterId",
                "updater": "updater",
                "creatorId": "creatorId",
                "creator": "creator",
                "createAt": "createAt",
                "updatedAt": "updatedAt",
                "deletedAt": "deletedAt"
            }
        }
    ]
}
```



# **route - version list - API**

## URL: /environment/v1/route/version/list ##

## Method : Post ##

## Event ID : EnvRouteVersionList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| idList | array[uint64] |  | N |  |
| stackId | integer |  | N |  |
| dbInstanceId | integer |  | N |  |
| tenantId | string |  | N |  |
| latestFlag | boolean |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "idList": [
    ],
    "stackId": 0,
    "dbInstanceId": 0,
    "tenantId": "tenantId",
    "latestFlag": false
}
```

**struct example:**

```
type RouteVersionFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    IdList []uint64 `json:"idList"`
    StackId uint64 `json:"stackId"`
    DbInstanceId uint64 `json:"dbInstanceId"`
    TenantId string `json:"tenantId"`
    LatestFlag bool `json:"latestFlag"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| routeId | integer | N |  |
| lastPatchId | integer | N |  |
| baseVersionId | integer | N |  |
| linkVersionId | integer | N |  |
| status | string | N |  |
| journal | string | N |  |
| data | map[string]interface | N |  |
| version | string | N |  |
| description | string | N |  |
| tenantId | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "routeId": 0,
    "lastPatchId": 0,
    "baseVersionId": 0,
    "linkVersionId": 0,
    "status": "status",
    "journal": "journal",
    "data": {
    },
    "version": "version",
    "description": "description",
    "tenantId": "tenantId",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **route - version page - API**

## URL: /environment/v1/route/version/page ##

## Method : Post ##

## Event ID : EnvRouteVersionPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| idList | array[uint64] |  | N |  |
| stackId | integer |  | N |  |
| dbInstanceId | integer |  | N |  |
| tenantId | string |  | N |  |
| latestFlag | boolean |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "idList": [
    ],
    "stackId": 0,
    "dbInstanceId": 0,
    "tenantId": "tenantId",
    "latestFlag": false
}
```

**struct example:**

```
type RouteVersionFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    IdList []uint64 `json:"idList"`
    StackId uint64 `json:"stackId"`
    DbInstanceId uint64 `json:"dbInstanceId"`
    TenantId string `json:"tenantId"`
    LatestFlag bool `json:"latestFlag"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **route - version revises - API**

## URL: /environment/v1/route/version/revises ##

## Method : Post ##

## Event ID : EnvRouteVersionRevises ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type routeVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| patchId | integer | N |  |
| fieldId | integer | N |  |
| operate | string | N |  |
| oldValue | string | N |  |
| newValue | string | N |  |
| version | string | N |  |
| reason | string | N |  |
| tenantId | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "patchId": 0,
    "fieldId": 0,
    "operate": "operate",
    "oldValue": "oldValue",
    "newValue": "newValue",
    "version": "version",
    "reason": "reason",
    "tenantId": "tenantId",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **route - version update - API**

## URL: /environment/v1/route/version/update ##

## Method : Post ##

## Event ID : EnvRouteVersionUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| versionId | integer |  | Y | gt=0,required |
| note | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "versionId": 0,
    "note": "note"
}
```

**struct example:**

```
type UpdateRouteVersionRequest struct {
    VersionId uint64 `json:"versionId" validate:"gt=0,required"`
    Note string `json:"note" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


