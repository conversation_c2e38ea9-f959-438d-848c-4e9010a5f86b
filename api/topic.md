
# **topic - add - API**

## URL: /environment/v1/topic/add ##

## Method : Post ##

## Event ID : EnvTopicAdd ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| queueId | integer |  | Y | required,gt=0 |
| topicName | string |  | Y | required,max=255 |
| eventId | integer |  | Y | gt=0,required |
| eventCode | string |  | N | name,max=32 |
| eventUuid | string |  | N | name,max=64 |
| topicType | string |  | N | name,max=32 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "queueId": 0,
    "topicName": "topicName",
    "eventId": 0,
    "eventCode": "eventCode",
    "eventUuid": "eventUuid",
    "topicType": "topicType",
    "description": "description"
}
```

**struct example:**

```
type AddTopicRequest struct {
    QueueId uint64 `json:"queueId" validate:"required,gt=0"`
    TopicName string `json:"topicName" validate:"required,max=255"`
    EventId uint64 `json:"eventId" validate:"gt=0,required"`
    EventCode string `json:"eventCode" validate:"name,max=32"`
    EventUuid string `json:"eventUuid" validate:"name,max=64"`
    TopicType string `json:"topicType" validate:"name,max=32"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **topic - delete - API**

## URL: /environment/v1/topic/delete ##

## Method : Post ##

## Event ID : EnvTopicDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type stackIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **topic - detail - API**

## URL: /environment/v1/topic/detail ##

## Method : Post ##

## Event ID : EnvTopicDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type stackIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| instanceId | integer | N |  |
| vpnCode | string | N |  |
| meshId | integer | N |  |
| meshCode | string | N |  |
| username | string | N |  |
| password | string | N |  |
| ttl | integer | N |  |
| depth | integer | N |  |
| maxUnAckMsg | integer | N |  |
| queueName | string | N |  |
| Topic | object | N |  |
| Topic.id | integer | N |  |
| Topic.queueId | integer | N |  |
| Topic.eventId | integer | N |  |
| Topic.eventUuid | string | N |  |
| Topic.eventCode | string | N |  |
| Topic.topicName | string | N |  |
| Topic.topicType | string | N |  |
| Topic.status | string | N |  |
| Topic.subscribeStatus | string | N |  |
| Topic.description | string | N |  |
| Topic.tenantId | string | N |  |
| Topic.creatorId | string | N |  |
| Topic.creator | string | N |  |
| Topic.updaterId | string | N |  |
| Topic.updater | string | N |  |
| Topic.createAt | string | N |  |
| Topic.updatedAt | string | N |  |
| Topic.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "instanceId": 0,
    "vpnCode": "vpnCode",
    "meshId": 0,
    "meshCode": "meshCode",
    "username": "username",
    "password": "password",
    "ttl": 0,
    "depth": 0,
    "maxUnAckMsg": 0,
    "queueName": "queueName",
    "Topic": {
        "id": 0,
        "queueId": 0,
        "eventId": 0,
        "eventUuid": "eventUuid",
        "eventCode": "eventCode",
        "topicName": "topicName",
        "topicType": "topicType",
        "status": "status",
        "subscribeStatus": "subscribeStatus",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **topic - list - API**

## URL: /environment/v1/topic/list ##

## Method : Post ##

## Event ID : EnvTopicList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| queueId | integer |  | N |  |
| instanceId | integer |  | N |  |
| meshId | integer |  | N |  |
| eventCode | string |  | N |  |
| tenantId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "queueId": 0,
    "instanceId": 0,
    "meshId": 0,
    "eventCode": "eventCode",
    "tenantId": "tenantId"
}
```

**struct example:**

```
type TopicFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    QueueId uint64 `json:"queueId"`
    InstanceId uint64 `json:"instanceId"`
    MeshId uint64 `json:"meshId"`
    EventCode string `json:"eventCode"`
    TenantId string `json:"tenantId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| instanceId | integer | N |  |
| vpnCode | string | N |  |
| meshId | integer | N |  |
| meshCode | string | N |  |
| username | string | N |  |
| password | string | N |  |
| ttl | integer | N |  |
| depth | integer | N |  |
| maxUnAckMsg | integer | N |  |
| queueName | string | N |  |
| Topic | object | N |  |
| Topic.id | integer | N |  |
| Topic.queueId | integer | N |  |
| Topic.eventId | integer | N |  |
| Topic.eventUuid | string | N |  |
| Topic.eventCode | string | N |  |
| Topic.topicName | string | N |  |
| Topic.topicType | string | N |  |
| Topic.status | string | N |  |
| Topic.subscribeStatus | string | N |  |
| Topic.description | string | N |  |
| Topic.tenantId | string | N |  |
| Topic.creatorId | string | N |  |
| Topic.creator | string | N |  |
| Topic.updaterId | string | N |  |
| Topic.updater | string | N |  |
| Topic.createAt | string | N |  |
| Topic.updatedAt | string | N |  |
| Topic.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "instanceId": 0,
    "vpnCode": "vpnCode",
    "meshId": 0,
    "meshCode": "meshCode",
    "username": "username",
    "password": "password",
    "ttl": 0,
    "depth": 0,
    "maxUnAckMsg": 0,
    "queueName": "queueName",
    "Topic": {
        "id": 0,
        "queueId": 0,
        "eventId": 0,
        "eventUuid": "eventUuid",
        "eventCode": "eventCode",
        "topicName": "topicName",
        "topicType": "topicType",
        "status": "status",
        "subscribeStatus": "subscribeStatus",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **topic - page - API**

## URL: /environment/v1/topic/page ##

## Method : Post ##

## Event ID : EnvTopicPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| queueId | integer |  | N |  |
| instanceId | integer |  | N |  |
| meshId | integer |  | N |  |
| eventCode | string |  | N |  |
| tenantId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "queueId": 0,
    "instanceId": 0,
    "meshId": 0,
    "eventCode": "eventCode",
    "tenantId": "tenantId"
}
```

**struct example:**

```
type TopicFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    QueueId uint64 `json:"queueId"`
    InstanceId uint64 `json:"instanceId"`
    MeshId uint64 `json:"meshId"`
    EventCode string `json:"eventCode"`
    TenantId string `json:"tenantId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **topic - update - API**

## URL: /environment/v1/topic/update ##

## Method : Post ##

## Event ID : EnvTopicUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | gt=0,required |
| label | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "id": 0,
    "label": "label"
}
```

**struct example:**

```
type UpdateTopicRequest struct {
    Id uint64 `json:"id" validate:"gt=0,required"`
    Label string `json:"label" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


