
# **environment - check - API**

## URL: /environment/v1/environment/check ##

## Method : Post ##

## Event ID : EnvEnvironmentCheck ##

**Description:**

check environment

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| environmentId | integer |  | N |  |
| environmentCode | string |  | N |  |

**Range of values**

request example:
```
{
    "environmentId": 0,
    "environmentCode": "environmentCode"
}
```

**struct example:**

```
type ExportEnvironmentConfigureRequest struct {
    EnvironmentId uint64 `json:"environmentId"`
    EnvironmentCode string `json:"environmentCode"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| environmentId | integer | Y | required |
| environmentCode | string | Y | required |
| suList | array[SuInfo] | N | omitempty |
| SuInfo.suTypeId | string | Y | required |
| SuInfo.suId | string | Y | required |
| SuInfo.suName | string | N | omitempty |
| SuInfo.weight | uint | Y | required |
| SuInfo.shardingQuota | integer | N | omitempty |
| SuInfo.suInstanceList | array[SuInstanceInfo] | N | omitempty |
| SuInstanceInfo.suInstanceId | string | Y | required |
| SuInstanceInfo.suInstanceName | string | N | omitempty |
| serviceList | array[ServiceInfo] | N |  |
| ServiceInfo.serviceId | string | Y | required |
| ServiceInfo.serviceType | string | Y | required |
| ServiceInfo.name | string | N | omitempty |
| ServiceInfo.configs | map[string]interface | N | omitempty |
| shardingList | array[GlsShardingInfo] | N |  |
| GlsShardingInfo.suTypeId | string | Y | required |
| GlsShardingInfo.primaryId | string | Y | required |
| GlsShardingInfo.secondaryId | string | Y | required |
| GlsShardingInfo.key | string | Y | required |
| GlsShardingInfo.value | string | Y | required |

**Range of values**

response example:

```
{
    "environmentId": 0,
    "environmentCode": "environmentCode",
    "suList": [
        {
            "suTypeId": "suTypeId",
            "suId": "suId",
            "suName": "suName",
            "weight": "weight",
            "shardingQuota": 0,
            "suInstanceList": [
                {
                    "suInstanceId": "suInstanceId",
                    "suInstanceName": "suInstanceName"
                }
            ]
        }
    ],
    "serviceList": [
        {
            "serviceId": "serviceId",
            "serviceType": "serviceType",
            "name": "name",
            "configs": {
            }
        }
    ],
    "shardingList": [
        {
            "suTypeId": "suTypeId",
            "primaryId": "primaryId",
            "secondaryId": "secondaryId",
            "key": "key",
            "value": "value"
        }
    ]
}
```



# **environment - check-for-deploy - API**

## URL: /environment/v1/environment/check-for-deploy ##

## Method : Post ##

## Event ID : EnvEnvironmentCheckForDeploy ##

**Description:**

check for environment deploy

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| sync | boolean |  | N | omitempty |
| environmentId | integer |  | N | gt=0 |
| componentType | string |  | N | name,max=64 |
| services | object |  | Y | required |

**Range of values**

request example:
```
{
    "sync": false,
    "environmentId": 0,
    "componentType": "componentType",
    "services": {
    }
}
```

**struct example:**

```
type CheckForDeployRequest struct {
    Sync bool `json:"sync" validate:"omitempty"`
    EnvironmentId uint64 `json:"environmentId" validate:"gt=0"`
    ComponentType string `json:"componentType" validate:"name,max=64"`
    Services struct {
    } `json:"services" validate:"required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| environment | string | N |  |
| pass | boolean | N |  |
| negligible | boolean | N |  |
| detail | object | N |  |
| finishTime | string | N |  |
| reason | string | N |  |
| progress | integer | N |  |

**Range of values**

response example:

```
{
    "environment": "environment",
    "pass": false,
    "negligible": false,
    "detail": {
    },
    "finishTime": "finishTime",
    "reason": "reason",
    "progress": 0
}
```



# **environment - check-result - API**

## URL: /environment/v1/environment/check-result ##

## Method : Post ##

## Event ID : EnvEnvironmentCheckResult ##

**Description:**

check environment result

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| requestId | string |  | N | name,max=64 |

**Range of values**

request example:
```
{
    "requestId": "requestId"
}
```

**struct example:**

```
type GetCheckResultRequest struct {
    RequestId string `json:"requestId" validate:"name,max=64"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| environment | string | N |  |
| pass | boolean | N |  |
| negligible | boolean | N |  |
| detail | object | N |  |
| finishTime | string | N |  |
| reason | string | N |  |
| progress | integer | N |  |

**Range of values**

response example:

```
{
    "environment": "environment",
    "pass": false,
    "negligible": false,
    "detail": {
    },
    "finishTime": "finishTime",
    "reason": "reason",
    "progress": 0
}
```



# **environment - compare - API**

## URL: /environment/v1/environment/compare ##

## Method : Post ##

## Event ID : EnvEnvironmentCompare ##

**Description:**

compare environment configuration

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| importCode | string |  | Y | required |
| environmentCode | string |  | Y | required |
| suList | array[SuData] |  | N | omitempty |
| SuData.action | string |  | N | omitempty |
| SuData.suTypeId | string |  | Y | required |
| SuData.suId | string |  | Y | required |
| SuData.suName | string |  | N | omitempty |
| SuData.weight | uint |  | Y | required |
| SuData.shardingQuota | integer |  | N | omitempty |
| SuData.suInstanceList | array[SuInstanceData] |  | N | omitempty |
| SuInstanceData.action | string |  | N | omitempty |
| SuInstanceData.SuInstanceInfo | object |  | N |  |
| SuInstanceInfo.suInstanceId | string |  | Y | required |
| SuInstanceInfo.suInstanceName | string |  | N | omitempty |
| serviceList | array[ServiceData] |  | N |  |
| ServiceData.action | string |  | N | omitempty |
| ServiceData.ServiceInfo | object |  | N |  |
| ServiceInfo.serviceId | string |  | Y | required |
| ServiceInfo.serviceType | string |  | Y | required |
| ServiceInfo.name | string |  | N | omitempty |
| ServiceInfo.configs | map[string]interface |  | N | omitempty |
| shardingList | array[ShardingData] |  | N |  |
| ShardingData.action | string |  | N | omitempty |
| ShardingData.GlsShardingInfo | object |  | N |  |
| GlsShardingInfo.suTypeId | string |  | Y | required |
| GlsShardingInfo.primaryId | string |  | Y | required |
| GlsShardingInfo.secondaryId | string |  | Y | required |
| GlsShardingInfo.key | string |  | Y | required |
| GlsShardingInfo.value | string |  | Y | required |

**Range of values**

request example:
```
{
    "importCode": "importCode",
    "environmentCode": "environmentCode",
    "suList": [
        {
            "action": "action",
            "suTypeId": "suTypeId",
            "suId": "suId",
            "suName": "suName",
            "weight": "weight",
            "shardingQuota": 0,
            "suInstanceList": [
                {
                    "action": "action",
                    "SuInstanceInfo": {
                        "suInstanceId": "suInstanceId",
                        "suInstanceName": "suInstanceName"
                    }
                }
            ]
        }
    ],
    "serviceList": [
        {
            "action": "action",
            "ServiceInfo": {
                "serviceId": "serviceId",
                "serviceType": "serviceType",
                "name": "name",
                "configs": {
                }
            }
        }
    ],
    "shardingList": [
        {
            "action": "action",
            "GlsShardingInfo": {
                "suTypeId": "suTypeId",
                "primaryId": "primaryId",
                "secondaryId": "secondaryId",
                "key": "key",
                "value": "value"
            }
        }
    ]
}
```

**struct example:**

```
type EnvironmentConfigureRequest struct {
    ImportCode string `json:"importCode" validate:"required"`
    EnvironmentCode string `json:"environmentCode" validate:"required"`
    SuList []struct {
            Action string `json:"action" validate:"omitempty"`
            SuTypeId string `json:"suTypeId" validate:"required"`
            SuId string `json:"suId" validate:"required"`
            SuName string `json:"suName" validate:"omitempty"`
            Weight uint `json:"weight" validate:"required"`
            ShardingQuota uint64 `json:"shardingQuota" validate:"omitempty"`
            SuInstanceList []struct {
                    Action string `json:"action" validate:"omitempty"`
                    SuInstanceInfo struct {
                        SuInstanceId string `json:"suInstanceId" validate:"required"`
                        SuInstanceName string `json:"suInstanceName" validate:"omitempty"`
                    }
                } `json:"suInstanceList" validate:"omitempty"`
        } `json:"suList" validate:"omitempty"`
    ServiceList []struct {
            Action string `json:"action" validate:"omitempty"`
            ServiceInfo struct {
                ServiceId string `json:"serviceId" validate:"required"`
                ServiceType string `json:"serviceType" validate:"required"`
                Name string `json:"name" validate:"omitempty"`
                Configs map[string]interface{} `json:"configs" validate:"omitempty"`
            }
        } `json:"serviceList"`
    ShardingList []struct {
            Action string `json:"action" validate:"omitempty"`
            GlsShardingInfo struct {
                SuTypeId string `json:"suTypeId" validate:"required"`
                PrimaryId string `json:"primaryId" validate:"required"`
                SecondaryId string `json:"secondaryId" validate:"required"`
                Key string `json:"key" validate:"required"`
                Value string `json:"value" validate:"required"`
            }
        } `json:"shardingList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | Y | required |
| importCode | string | Y | required |
| environmentCode | string | Y | required |
| result | integer | Y | required |
| suList | array[SuResult] | N | omitempty |
| SuResult.result | integer | Y | required |
| SuResult.suId | string | N | omitempty |
| SuResult.suTypeId | string | N | omitempty |
| SuResult.currentName | string | N | omitempty |
| SuResult.runtimeName | string | N | omitempty |
| SuResult.currentWeight | uint | Y | required |
| SuResult.runtimeWeight | uint | N | omitempty |
| SuResult.currentShardingQuota | integer | N | omitempty |
| SuResult.runtimeShardingQuota | integer | N | omitempty |
| SuResult.suInstanceList | array[SuInstanceResult] | N | omitempty |
| SuInstanceResult.result | integer | Y | required |
| SuInstanceResult.suInstanceId | string | Y | required |
| SuInstanceResult.currentSuInstanceName | string | N | omitempty |
| SuInstanceResult.runtimeSuInstanceName | string | N | omitempty |
| serviceList | array[ServiceResult] | N |  |
| ServiceResult.result | integer | Y | required |
| ServiceResult.serviceId | string | N | omitempty |
| ServiceResult.serviceType | string | N | omitempty |
| ServiceResult.currentName | string | N | omitempty |
| ServiceResult.runtimeName | string | N | omitempty |
| ServiceResult.configsResult | array[ServiceConfigResult] | N | omitempty |
| ServiceConfigResult.result | integer | Y | required |
| ServiceConfigResult.key | string | N | omitempty |
| ServiceConfigResult.currentValue | object | N | omitempty |
| ServiceConfigResult.runtimeValue | object | N | omitempty |
| shardingList | array[ShardingResult] | N |  |
| ShardingResult.result | integer | Y | required |
| ShardingResult.suTypeId | string | N | omitempty |
| ShardingResult.primaryId | string | N | omitempty |
| ShardingResult.secondaryId | string | N | omitempty |
| ShardingResult.key | string | N | omitempty |
| ShardingResult.currentValue | string | N | omitempty |
| ShardingResult.runtimeValue | string | N | omitempty |

**Range of values**

response example:

```
{
    "id": 0,
    "importCode": "importCode",
    "environmentCode": "environmentCode",
    "result": 0,
    "suList": [
        {
            "result": 0,
            "suId": "suId",
            "suTypeId": "suTypeId",
            "currentName": "currentName",
            "runtimeName": "runtimeName",
            "currentWeight": "currentWeight",
            "runtimeWeight": "runtimeWeight",
            "currentShardingQuota": 0,
            "runtimeShardingQuota": 0,
            "suInstanceList": [
                {
                    "result": 0,
                    "suInstanceId": "suInstanceId",
                    "currentSuInstanceName": "currentSuInstanceName",
                    "runtimeSuInstanceName": "runtimeSuInstanceName"
                }
            ]
        }
    ],
    "serviceList": [
        {
            "result": 0,
            "serviceId": "serviceId",
            "serviceType": "serviceType",
            "currentName": "currentName",
            "runtimeName": "runtimeName",
            "configsResult": [
                {
                    "result": 0,
                    "key": "key",
                    "currentValue": {
                    },
                    "runtimeValue": {
                    }
                }
            ]
        }
    ],
    "shardingList": [
        {
            "result": 0,
            "suTypeId": "suTypeId",
            "primaryId": "primaryId",
            "secondaryId": "secondaryId",
            "key": "key",
            "currentValue": "currentValue",
            "runtimeValue": "runtimeValue"
        }
    ]
}
```



# **environment - create - API**

## URL: /environment/v1/environment/create ##

## Method : Post ##

## Event ID : EnvEnvironmentCreate ##

**Description:**

create environment

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| name | string |  | N | name,max=32 |
| resourceGroupId | integer |  | N |  |
| workspaceId | string |  | Y | name,required |
| workspaceGitPath | string |  | Y | required |
| serverless | string |  | N | omitempty,oneof=Disabled Enabled |
| phase | string |  | N | name,max=32 |
| preset | string |  | N |  |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "name": "name",
    "resourceGroupId": 0,
    "workspaceId": "workspaceId",
    "workspaceGitPath": "workspaceGitPath",
    "serverless": "serverless",
    "phase": "phase",
    "preset": "preset",
    "description": "description"
}
```

**struct example:**

```
type CreateEnvironmentRequest struct {
    Name string `json:"name" validate:"name,max=32"`
    ResourceGroupId uint64 `json:"resourceGroupId"`
    WorkspaceId string `json:"workspaceId" validate:"name,required"`
    WorkspaceGitPath string `json:"workspaceGitPath" validate:"required"`
    Serverless string `json:"serverless" validate:"omitempty,oneof=Disabled Enabled"`
    Phase string `json:"phase" validate:"name,max=32"`
    Preset string `json:"preset"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| workspaceId | string | N |  |
| resourceGroupId | integer | N |  |
| resourceGroupName | string | N |  |
| name | string | N |  |
| code | string | N |  |
| phase | string | N |  |
| preset | string | N |  |
| serverless | string | N |  |
| wsGitPath | string | N |  |
| repoId | integer | N |  |
| repodbId | integer | N |  |
| templateGroupId | integer | N |  |
| configProjID | integer | N |  |
| upmDataId | integer | N |  |
| description | string | N |  |
| status | string | N |  |
| tenantId | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "workspaceId": "workspaceId",
    "resourceGroupId": 0,
    "resourceGroupName": "resourceGroupName",
    "name": "name",
    "code": "code",
    "phase": "phase",
    "preset": "preset",
    "serverless": "serverless",
    "wsGitPath": "wsGitPath",
    "repoId": 0,
    "repodbId": 0,
    "templateGroupId": 0,
    "configProjID": 0,
    "upmDataId": 0,
    "description": "description",
    "status": "status",
    "tenantId": "tenantId",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **environment - delete - API**

## URL: /environment/v1/environment/delete ##

## Method : Post ##

## Event ID : EnvEnvironmentDelete ##

**Description:**

delete environment

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type environmentIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **environment - detail - API**

## URL: /environment/v1/environment/detail ##

## Method : Post ##

## Event ID : EnvEnvironmentDetail ##

**Description:**

get environment detail

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type environmentIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| workspaceId | string | N |  |
| resourceGroupId | integer | N |  |
| resourceGroupName | string | N |  |
| name | string | N |  |
| code | string | N |  |
| phase | string | N |  |
| preset | string | N |  |
| serverless | string | N |  |
| wsGitPath | string | N |  |
| repoId | integer | N |  |
| repodbId | integer | N |  |
| templateGroupId | integer | N |  |
| configProjID | integer | N |  |
| upmDataId | integer | N |  |
| description | string | N |  |
| status | string | N |  |
| tenantId | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "workspaceId": "workspaceId",
    "resourceGroupId": 0,
    "resourceGroupName": "resourceGroupName",
    "name": "name",
    "code": "code",
    "phase": "phase",
    "preset": "preset",
    "serverless": "serverless",
    "wsGitPath": "wsGitPath",
    "repoId": 0,
    "repodbId": 0,
    "templateGroupId": 0,
    "configProjID": 0,
    "upmDataId": 0,
    "description": "description",
    "status": "status",
    "tenantId": "tenantId",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **environment - export - API**

## URL: /environment/v1/environment/export ##

## Method : Post ##

## Event ID : EnvEnvironmentExport ##

**Description:**

export environment configuration

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| environmentId | integer |  | N |  |
| environmentCode | string |  | N |  |

**Range of values**

request example:
```
{
    "environmentId": 0,
    "environmentCode": "environmentCode"
}
```

**struct example:**

```
type ExportEnvironmentConfigureRequest struct {
    EnvironmentId uint64 `json:"environmentId"`
    EnvironmentCode string `json:"environmentCode"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| environmentId | integer | Y | required |
| environmentCode | string | Y | required |
| suList | array[SuInfo] | N | omitempty |
| SuInfo.suTypeId | string | Y | required |
| SuInfo.suId | string | Y | required |
| SuInfo.suName | string | N | omitempty |
| SuInfo.weight | uint | Y | required |
| SuInfo.shardingQuota | integer | N | omitempty |
| SuInfo.suInstanceList | array[SuInstanceInfo] | N | omitempty |
| SuInstanceInfo.suInstanceId | string | Y | required |
| SuInstanceInfo.suInstanceName | string | N | omitempty |
| serviceList | array[ServiceInfo] | N |  |
| ServiceInfo.serviceId | string | Y | required |
| ServiceInfo.serviceType | string | Y | required |
| ServiceInfo.name | string | N | omitempty |
| ServiceInfo.configs | map[string]interface | N | omitempty |
| shardingList | array[GlsShardingInfo] | N |  |
| GlsShardingInfo.suTypeId | string | Y | required |
| GlsShardingInfo.primaryId | string | Y | required |
| GlsShardingInfo.secondaryId | string | Y | required |
| GlsShardingInfo.key | string | Y | required |
| GlsShardingInfo.value | string | Y | required |

**Range of values**

response example:

```
{
    "environmentId": 0,
    "environmentCode": "environmentCode",
    "suList": [
        {
            "suTypeId": "suTypeId",
            "suId": "suId",
            "suName": "suName",
            "weight": "weight",
            "shardingQuota": 0,
            "suInstanceList": [
                {
                    "suInstanceId": "suInstanceId",
                    "suInstanceName": "suInstanceName"
                }
            ]
        }
    ],
    "serviceList": [
        {
            "serviceId": "serviceId",
            "serviceType": "serviceType",
            "name": "name",
            "configs": {
            }
        }
    ],
    "shardingList": [
        {
            "suTypeId": "suTypeId",
            "primaryId": "primaryId",
            "secondaryId": "secondaryId",
            "key": "key",
            "value": "value"
        }
    ]
}
```



# **environment - import - API**

## URL: /environment/v1/environment/import ##

## Method : Post ##

## Event ID : EnvEnvironmentImport ##

**Description:**

import environment configuration

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type Request struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **environment - list - API**

## URL: /environment/v1/environment/list ##

## Method : Post ##

## Event ID : EnvEnvironmentList ##

**Description:**

list environment

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| templateGroupId | integer |  | N |  |
| code | string |  | N |  |
| workspaceId | string |  | N |  |
| repoId | integer |  | N |  |
| tenantId | string |  | N |  |
| name | string |  | N |  |
| configProjectId | integer |  | N |  |
| resourceGroupId | integer |  | N |  |
| workspaceList | array[string] |  | N |  |
| envCodeList | array[string] |  | N |  |
| creatorId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "templateGroupId": 0,
    "code": "code",
    "workspaceId": "workspaceId",
    "repoId": 0,
    "tenantId": "tenantId",
    "name": "name",
    "configProjectId": 0,
    "resourceGroupId": 0,
    "workspaceList": [
    ],
    "envCodeList": [
    ],
    "creatorId": "creatorId"
}
```

**struct example:**

```
type EnvironmentFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    TemplateGroupId uint64 `json:"templateGroupId"`
    Code string `json:"code"`
    WorkspaceId string `json:"workspaceId"`
    RepoId uint64 `json:"repoId"`
    TenantId string `json:"tenantId"`
    Name string `json:"name"`
    ConfigProjectId int `json:"configProjectId"`
    ResourceGroupId uint64 `json:"resourceGroupId"`
    WorkspaceList []string `json:"workspaceList"`
    EnvCodeList []string `json:"envCodeList"`
    CreatorId string `json:"creatorId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| workspaceId | string | N |  |
| resourceGroupId | integer | N |  |
| resourceGroupName | string | N |  |
| name | string | N |  |
| code | string | N |  |
| phase | string | N |  |
| preset | string | N |  |
| serverless | string | N |  |
| wsGitPath | string | N |  |
| repoId | integer | N |  |
| repodbId | integer | N |  |
| templateGroupId | integer | N |  |
| configProjID | integer | N |  |
| upmDataId | integer | N |  |
| description | string | N |  |
| status | string | N |  |
| tenantId | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "workspaceId": "workspaceId",
    "resourceGroupId": 0,
    "resourceGroupName": "resourceGroupName",
    "name": "name",
    "code": "code",
    "phase": "phase",
    "preset": "preset",
    "serverless": "serverless",
    "wsGitPath": "wsGitPath",
    "repoId": 0,
    "repodbId": 0,
    "templateGroupId": 0,
    "configProjID": 0,
    "upmDataId": 0,
    "description": "description",
    "status": "status",
    "tenantId": "tenantId",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **environment - precheck - API**

## URL: /environment/v1/environment/precheck ##

## Method : Post ##

## Event ID : EnvEnvironmentPrecheck ##

**Description:**

precheck environment

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| importCode | string |  | Y | required |
| environmentCode | string |  | Y | required |
| suList | array[SuData] |  | N | omitempty |
| SuData.action | string |  | N | omitempty |
| SuData.suTypeId | string |  | Y | required |
| SuData.suId | string |  | Y | required |
| SuData.suName | string |  | N | omitempty |
| SuData.weight | uint |  | Y | required |
| SuData.shardingQuota | integer |  | N | omitempty |
| SuData.suInstanceList | array[SuInstanceData] |  | N | omitempty |
| SuInstanceData.action | string |  | N | omitempty |
| SuInstanceData.SuInstanceInfo | object |  | N |  |
| SuInstanceInfo.suInstanceId | string |  | Y | required |
| SuInstanceInfo.suInstanceName | string |  | N | omitempty |
| serviceList | array[ServiceData] |  | N |  |
| ServiceData.action | string |  | N | omitempty |
| ServiceData.ServiceInfo | object |  | N |  |
| ServiceInfo.serviceId | string |  | Y | required |
| ServiceInfo.serviceType | string |  | Y | required |
| ServiceInfo.name | string |  | N | omitempty |
| ServiceInfo.configs | map[string]interface |  | N | omitempty |
| shardingList | array[ShardingData] |  | N |  |
| ShardingData.action | string |  | N | omitempty |
| ShardingData.GlsShardingInfo | object |  | N |  |
| GlsShardingInfo.suTypeId | string |  | Y | required |
| GlsShardingInfo.primaryId | string |  | Y | required |
| GlsShardingInfo.secondaryId | string |  | Y | required |
| GlsShardingInfo.key | string |  | Y | required |
| GlsShardingInfo.value | string |  | Y | required |

**Range of values**

request example:
```
{
    "importCode": "importCode",
    "environmentCode": "environmentCode",
    "suList": [
        {
            "action": "action",
            "suTypeId": "suTypeId",
            "suId": "suId",
            "suName": "suName",
            "weight": "weight",
            "shardingQuota": 0,
            "suInstanceList": [
                {
                    "action": "action",
                    "SuInstanceInfo": {
                        "suInstanceId": "suInstanceId",
                        "suInstanceName": "suInstanceName"
                    }
                }
            ]
        }
    ],
    "serviceList": [
        {
            "action": "action",
            "ServiceInfo": {
                "serviceId": "serviceId",
                "serviceType": "serviceType",
                "name": "name",
                "configs": {
                }
            }
        }
    ],
    "shardingList": [
        {
            "action": "action",
            "GlsShardingInfo": {
                "suTypeId": "suTypeId",
                "primaryId": "primaryId",
                "secondaryId": "secondaryId",
                "key": "key",
                "value": "value"
            }
        }
    ]
}
```

**struct example:**

```
type EnvironmentConfigureRequest struct {
    ImportCode string `json:"importCode" validate:"required"`
    EnvironmentCode string `json:"environmentCode" validate:"required"`
    SuList []struct {
            Action string `json:"action" validate:"omitempty"`
            SuTypeId string `json:"suTypeId" validate:"required"`
            SuId string `json:"suId" validate:"required"`
            SuName string `json:"suName" validate:"omitempty"`
            Weight uint `json:"weight" validate:"required"`
            ShardingQuota uint64 `json:"shardingQuota" validate:"omitempty"`
            SuInstanceList []struct {
                    Action string `json:"action" validate:"omitempty"`
                    SuInstanceInfo struct {
                        SuInstanceId string `json:"suInstanceId" validate:"required"`
                        SuInstanceName string `json:"suInstanceName" validate:"omitempty"`
                    }
                } `json:"suInstanceList" validate:"omitempty"`
        } `json:"suList" validate:"omitempty"`
    ServiceList []struct {
            Action string `json:"action" validate:"omitempty"`
            ServiceInfo struct {
                ServiceId string `json:"serviceId" validate:"required"`
                ServiceType string `json:"serviceType" validate:"required"`
                Name string `json:"name" validate:"omitempty"`
                Configs map[string]interface{} `json:"configs" validate:"omitempty"`
            }
        } `json:"serviceList"`
    ShardingList []struct {
            Action string `json:"action" validate:"omitempty"`
            GlsShardingInfo struct {
                SuTypeId string `json:"suTypeId" validate:"required"`
                PrimaryId string `json:"primaryId" validate:"required"`
                SecondaryId string `json:"secondaryId" validate:"required"`
                Key string `json:"key" validate:"required"`
                Value string `json:"value" validate:"required"`
            }
        } `json:"shardingList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | Y | required |
| importCode | string | Y | required |
| environmentCode | string | Y | required |
| result | integer | Y | required |
| suList | array[SuResult] | N | omitempty |
| SuResult.result | integer | Y | required |
| SuResult.suId | string | N | omitempty |
| SuResult.suTypeId | string | N | omitempty |
| SuResult.currentName | string | N | omitempty |
| SuResult.runtimeName | string | N | omitempty |
| SuResult.currentWeight | uint | Y | required |
| SuResult.runtimeWeight | uint | N | omitempty |
| SuResult.currentShardingQuota | integer | N | omitempty |
| SuResult.runtimeShardingQuota | integer | N | omitempty |
| SuResult.suInstanceList | array[SuInstanceResult] | N | omitempty |
| SuInstanceResult.result | integer | Y | required |
| SuInstanceResult.suInstanceId | string | Y | required |
| SuInstanceResult.currentSuInstanceName | string | N | omitempty |
| SuInstanceResult.runtimeSuInstanceName | string | N | omitempty |
| serviceList | array[ServiceResult] | N |  |
| ServiceResult.result | integer | Y | required |
| ServiceResult.serviceId | string | N | omitempty |
| ServiceResult.serviceType | string | N | omitempty |
| ServiceResult.currentName | string | N | omitempty |
| ServiceResult.runtimeName | string | N | omitempty |
| ServiceResult.configsResult | array[ServiceConfigResult] | N | omitempty |
| ServiceConfigResult.result | integer | Y | required |
| ServiceConfigResult.key | string | N | omitempty |
| ServiceConfigResult.currentValue | object | N | omitempty |
| ServiceConfigResult.runtimeValue | object | N | omitempty |
| shardingList | array[ShardingResult] | N |  |
| ShardingResult.result | integer | Y | required |
| ShardingResult.suTypeId | string | N | omitempty |
| ShardingResult.primaryId | string | N | omitempty |
| ShardingResult.secondaryId | string | N | omitempty |
| ShardingResult.key | string | N | omitempty |
| ShardingResult.currentValue | string | N | omitempty |
| ShardingResult.runtimeValue | string | N | omitempty |

**Range of values**

response example:

```
{
    "id": 0,
    "importCode": "importCode",
    "environmentCode": "environmentCode",
    "result": 0,
    "suList": [
        {
            "result": 0,
            "suId": "suId",
            "suTypeId": "suTypeId",
            "currentName": "currentName",
            "runtimeName": "runtimeName",
            "currentWeight": "currentWeight",
            "runtimeWeight": "runtimeWeight",
            "currentShardingQuota": 0,
            "runtimeShardingQuota": 0,
            "suInstanceList": [
                {
                    "result": 0,
                    "suInstanceId": "suInstanceId",
                    "currentSuInstanceName": "currentSuInstanceName",
                    "runtimeSuInstanceName": "runtimeSuInstanceName"
                }
            ]
        }
    ],
    "serviceList": [
        {
            "result": 0,
            "serviceId": "serviceId",
            "serviceType": "serviceType",
            "currentName": "currentName",
            "runtimeName": "runtimeName",
            "configsResult": [
                {
                    "result": 0,
                    "key": "key",
                    "currentValue": {
                    },
                    "runtimeValue": {
                    }
                }
            ]
        }
    ],
    "shardingList": [
        {
            "result": 0,
            "suTypeId": "suTypeId",
            "primaryId": "primaryId",
            "secondaryId": "secondaryId",
            "key": "key",
            "currentValue": "currentValue",
            "runtimeValue": "runtimeValue"
        }
    ]
}
```



# **environment - precompare - API**

## URL: /environment/v1/environment/precompare ##

## Method : Post ##

## Event ID : EnvEnvironmentPreCompare ##

**Description:**

pre check environment configuration

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| importCode | string |  | Y | required |
| environmentCode | string |  | Y | required |
| suList | array[SuData] |  | N | omitempty |
| SuData.action | string |  | N | omitempty |
| SuData.suTypeId | string |  | Y | required |
| SuData.suId | string |  | Y | required |
| SuData.suName | string |  | N | omitempty |
| SuData.weight | uint |  | Y | required |
| SuData.shardingQuota | integer |  | N | omitempty |
| SuData.suInstanceList | array[SuInstanceData] |  | N | omitempty |
| SuInstanceData.action | string |  | N | omitempty |
| SuInstanceData.SuInstanceInfo | object |  | N |  |
| SuInstanceInfo.suInstanceId | string |  | Y | required |
| SuInstanceInfo.suInstanceName | string |  | N | omitempty |
| serviceList | array[ServiceData] |  | N |  |
| ServiceData.action | string |  | N | omitempty |
| ServiceData.ServiceInfo | object |  | N |  |
| ServiceInfo.serviceId | string |  | Y | required |
| ServiceInfo.serviceType | string |  | Y | required |
| ServiceInfo.name | string |  | N | omitempty |
| ServiceInfo.configs | map[string]interface |  | N | omitempty |
| shardingList | array[ShardingData] |  | N |  |
| ShardingData.action | string |  | N | omitempty |
| ShardingData.GlsShardingInfo | object |  | N |  |
| GlsShardingInfo.suTypeId | string |  | Y | required |
| GlsShardingInfo.primaryId | string |  | Y | required |
| GlsShardingInfo.secondaryId | string |  | Y | required |
| GlsShardingInfo.key | string |  | Y | required |
| GlsShardingInfo.value | string |  | Y | required |

**Range of values**

request example:
```
{
    "importCode": "importCode",
    "environmentCode": "environmentCode",
    "suList": [
        {
            "action": "action",
            "suTypeId": "suTypeId",
            "suId": "suId",
            "suName": "suName",
            "weight": "weight",
            "shardingQuota": 0,
            "suInstanceList": [
                {
                    "action": "action",
                    "SuInstanceInfo": {
                        "suInstanceId": "suInstanceId",
                        "suInstanceName": "suInstanceName"
                    }
                }
            ]
        }
    ],
    "serviceList": [
        {
            "action": "action",
            "ServiceInfo": {
                "serviceId": "serviceId",
                "serviceType": "serviceType",
                "name": "name",
                "configs": {
                }
            }
        }
    ],
    "shardingList": [
        {
            "action": "action",
            "GlsShardingInfo": {
                "suTypeId": "suTypeId",
                "primaryId": "primaryId",
                "secondaryId": "secondaryId",
                "key": "key",
                "value": "value"
            }
        }
    ]
}
```

**struct example:**

```
type EnvironmentConfigureRequest struct {
    ImportCode string `json:"importCode" validate:"required"`
    EnvironmentCode string `json:"environmentCode" validate:"required"`
    SuList []struct {
            Action string `json:"action" validate:"omitempty"`
            SuTypeId string `json:"suTypeId" validate:"required"`
            SuId string `json:"suId" validate:"required"`
            SuName string `json:"suName" validate:"omitempty"`
            Weight uint `json:"weight" validate:"required"`
            ShardingQuota uint64 `json:"shardingQuota" validate:"omitempty"`
            SuInstanceList []struct {
                    Action string `json:"action" validate:"omitempty"`
                    SuInstanceInfo struct {
                        SuInstanceId string `json:"suInstanceId" validate:"required"`
                        SuInstanceName string `json:"suInstanceName" validate:"omitempty"`
                    }
                } `json:"suInstanceList" validate:"omitempty"`
        } `json:"suList" validate:"omitempty"`
    ServiceList []struct {
            Action string `json:"action" validate:"omitempty"`
            ServiceInfo struct {
                ServiceId string `json:"serviceId" validate:"required"`
                ServiceType string `json:"serviceType" validate:"required"`
                Name string `json:"name" validate:"omitempty"`
                Configs map[string]interface{} `json:"configs" validate:"omitempty"`
            }
        } `json:"serviceList"`
    ShardingList []struct {
            Action string `json:"action" validate:"omitempty"`
            GlsShardingInfo struct {
                SuTypeId string `json:"suTypeId" validate:"required"`
                PrimaryId string `json:"primaryId" validate:"required"`
                SecondaryId string `json:"secondaryId" validate:"required"`
                Key string `json:"key" validate:"required"`
                Value string `json:"value" validate:"required"`
            }
        } `json:"shardingList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | Y | required |
| importCode | string | Y | required |
| environmentCode | string | Y | required |
| result | integer | Y | required |
| suList | array[SuResult] | N | omitempty |
| SuResult.result | integer | Y | required |
| SuResult.suId | string | N | omitempty |
| SuResult.suTypeId | string | N | omitempty |
| SuResult.currentName | string | N | omitempty |
| SuResult.runtimeName | string | N | omitempty |
| SuResult.currentWeight | uint | Y | required |
| SuResult.runtimeWeight | uint | N | omitempty |
| SuResult.currentShardingQuota | integer | N | omitempty |
| SuResult.runtimeShardingQuota | integer | N | omitempty |
| SuResult.suInstanceList | array[SuInstanceResult] | N | omitempty |
| SuInstanceResult.result | integer | Y | required |
| SuInstanceResult.suInstanceId | string | Y | required |
| SuInstanceResult.currentSuInstanceName | string | N | omitempty |
| SuInstanceResult.runtimeSuInstanceName | string | N | omitempty |
| serviceList | array[ServiceResult] | N |  |
| ServiceResult.result | integer | Y | required |
| ServiceResult.serviceId | string | N | omitempty |
| ServiceResult.serviceType | string | N | omitempty |
| ServiceResult.currentName | string | N | omitempty |
| ServiceResult.runtimeName | string | N | omitempty |
| ServiceResult.configsResult | array[ServiceConfigResult] | N | omitempty |
| ServiceConfigResult.result | integer | Y | required |
| ServiceConfigResult.key | string | N | omitempty |
| ServiceConfigResult.currentValue | object | N | omitempty |
| ServiceConfigResult.runtimeValue | object | N | omitempty |
| shardingList | array[ShardingResult] | N |  |
| ShardingResult.result | integer | Y | required |
| ShardingResult.suTypeId | string | N | omitempty |
| ShardingResult.primaryId | string | N | omitempty |
| ShardingResult.secondaryId | string | N | omitempty |
| ShardingResult.key | string | N | omitempty |
| ShardingResult.currentValue | string | N | omitempty |
| ShardingResult.runtimeValue | string | N | omitempty |

**Range of values**

response example:

```
{
    "id": 0,
    "importCode": "importCode",
    "environmentCode": "environmentCode",
    "result": 0,
    "suList": [
        {
            "result": 0,
            "suId": "suId",
            "suTypeId": "suTypeId",
            "currentName": "currentName",
            "runtimeName": "runtimeName",
            "currentWeight": "currentWeight",
            "runtimeWeight": "runtimeWeight",
            "currentShardingQuota": 0,
            "runtimeShardingQuota": 0,
            "suInstanceList": [
                {
                    "result": 0,
                    "suInstanceId": "suInstanceId",
                    "currentSuInstanceName": "currentSuInstanceName",
                    "runtimeSuInstanceName": "runtimeSuInstanceName"
                }
            ]
        }
    ],
    "serviceList": [
        {
            "result": 0,
            "serviceId": "serviceId",
            "serviceType": "serviceType",
            "currentName": "currentName",
            "runtimeName": "runtimeName",
            "configsResult": [
                {
                    "result": 0,
                    "key": "key",
                    "currentValue": {
                    },
                    "runtimeValue": {
                    }
                }
            ]
        }
    ],
    "shardingList": [
        {
            "result": 0,
            "suTypeId": "suTypeId",
            "primaryId": "primaryId",
            "secondaryId": "secondaryId",
            "key": "key",
            "currentValue": "currentValue",
            "runtimeValue": "runtimeValue"
        }
    ]
}
```



# **environment - process - API**

## URL: /environment/v1/environment/process ##

## Method : Post ##

## Event ID : EnvEnvironmentProcess ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | gt=0,required |
| action | string |  | Y | gt=0,required |
| envId | integer |  | Y | gt=0,required |
| folder | string |  | N | omitempty,name,max=64 |

**Range of values**

request example:
```
{
    "id": 0,
    "action": "action",
    "envId": 0,
    "folder": "folder"
}
```

**struct example:**

```
type EnvOperateParameter struct {
    Id uint64 `json:"id" validate:"gt=0,required"`
    Action string `json:"action" validate:"gt=0,required"`
    EnvId uint64 `json:"envId" validate:"gt=0,required"`
    Folder string `json:"folder" validate:"omitempty,name,max=64"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **environment - products - API**

## URL: /environment/v1/environment/products ##

## Method : Post ##

## Event ID : EnvEnvironmentProducts ##

**Description:**

query environment products

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |

**Range of values**

request example:
```
{
}
```

**struct example:**

```
type Void struct {
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| products | array[string] | Y | required |

**Range of values**

response example:

```
{
    "products": [
    ]
}
```



# **environment - syncProduct - API**

## URL: /environment/v1/environment/syncProduct ##

## Method : Post ##

## Event ID : EnvEnvironmentSyncProduct ##

**Description:**

sync environment products

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| envId | integer |  | Y | required |
| product | string |  | Y | required |

**Range of values**

request example:
```
{
    "envId": 0,
    "product": "product"
}
```

**struct example:**

```
type SyncProductRequest struct {
    EnvId uint64 `json:"envId" validate:"required"`
    Product string `json:"product" validate:"required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **environment - update - API**

## URL: /environment/v1/environment/update ##

## Method : Post ##

## Event ID : EnvEnvironmentUpdate ##

**Description:**

update environment

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | gt=0,required |
| name | string |  | N | name,max=32 |
| description | string |  | N | omitempty,max=255 |
| resourceGroupId | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0,
    "name": "name",
    "description": "description",
    "resourceGroupId": 0
}
```

**struct example:**

```
type UpdateEnvironmentRequest struct {
    Id uint64 `json:"id" validate:"gt=0,required"`
    Name string `json:"name" validate:"name,max=32"`
    Description string `json:"description" validate:"omitempty,max=255"`
    ResourceGroupId uint64 `json:"resourceGroupId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| workspaceId | string | N |  |
| resourceGroupId | integer | N |  |
| resourceGroupName | string | N |  |
| name | string | N |  |
| code | string | N |  |
| phase | string | N |  |
| preset | string | N |  |
| serverless | string | N |  |
| wsGitPath | string | N |  |
| repoId | integer | N |  |
| repodbId | integer | N |  |
| templateGroupId | integer | N |  |
| configProjID | integer | N |  |
| upmDataId | integer | N |  |
| description | string | N |  |
| status | string | N |  |
| tenantId | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "workspaceId": "workspaceId",
    "resourceGroupId": 0,
    "resourceGroupName": "resourceGroupName",
    "name": "name",
    "code": "code",
    "phase": "phase",
    "preset": "preset",
    "serverless": "serverless",
    "wsGitPath": "wsGitPath",
    "repoId": 0,
    "repodbId": 0,
    "templateGroupId": 0,
    "configProjID": 0,
    "upmDataId": 0,
    "description": "description",
    "status": "status",
    "tenantId": "tenantId",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```


