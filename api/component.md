
# **component - attach - API**

## URL: /environment/v1/component/attach ##

## Method : Post ##

## Event ID : EnvComponentAttach ##

**Description:**

attach service instance

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| planId | integer |  | N | gt=0 |
| type | string |  | N | name,max=64 |
| componentId | integer |  | N | gt=0 |
| serviceInstanceId | integer |  | N | gt=0 |
| versionId | integer |  | N | gt=0 |

**Range of values**

request example:
```
{
    "planId": 0,
    "type": "type",
    "componentId": 0,
    "serviceInstanceId": 0,
    "versionId": 0
}
```

**struct example:**

```
type AttachInstanceRequest struct {
    PlanId uint64 `json:"planId" validate:"gt=0"`
    Type string `json:"type" validate:"name,max=64"`
    ComponentId uint64 `json:"componentId" validate:"gt=0"`
    ServiceInstanceId uint64 `json:"serviceInstanceId" validate:"gt=0"`
    VersionId uint64 `json:"versionId" validate:"gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **component - create - API**

## URL: /environment/v1/component/create ##

## Method : Post ##

## Event ID : EnvComponentCreate ##

**Description:**

create component

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| planId | integer |  | N | gt=0 |
| type | string |  | N | name,max=64 |
| packageId | integer |  | N |  |
| rdbClusterId | integer |  | N | omitempty,gte=0 |
| k8sClusterId | integer |  | N | omitempty,gt=0 |
| cacheClusterId | integer |  | N | omitempty,gte=0 |

**Range of values**

request example:
```
{
    "planId": 0,
    "type": "type",
    "packageId": 0,
    "rdbClusterId": 0,
    "k8sClusterId": 0,
    "cacheClusterId": 0
}
```

**struct example:**

```
type CreateComponentRequest struct {
    PlanId uint64 `json:"planId" validate:"gt=0"`
    Type string `json:"type" validate:"name,max=64"`
    PackageId uint64 `json:"packageId"`
    RdbClusterId uint64 `json:"rdbClusterId" validate:"omitempty,gte=0"`
    K8sClusterId uint64 `json:"k8sClusterId" validate:"omitempty,gt=0"`
    CacheClusterId uint64 `json:"cacheClusterId" validate:"omitempty,gte=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **component - detach - API**

## URL: /environment/v1/component/detach ##

## Method : Post ##

## Event ID : EnvComponentDetach ##

**Description:**

detach service instance

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| type | string |  | N | name,max=64 |
| componentId | integer |  | N | gt=0 |
| serviceInstanceId | integer |  | N | gt=0 |

**Range of values**

request example:
```
{
    "type": "type",
    "componentId": 0,
    "serviceInstanceId": 0
}
```

**struct example:**

```
type DetachInstanceRequest struct {
    Type string `json:"type" validate:"name,max=64"`
    ComponentId uint64 `json:"componentId" validate:"gt=0"`
    ServiceInstanceId uint64 `json:"serviceInstanceId" validate:"gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **component - offline - API**

## URL: /environment/v1/component/offline ##

## Method : Post ##

## Event ID : EnvComponentOffline ##

**Description:**

offline a component

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| type | string |  | N | name,max=64 |
| id | integer |  | N | gt=0 |

**Range of values**

request example:
```
{
    "type": "type",
    "id": 0
}
```

**struct example:**

```
type OfflineComponentRequest struct {
    Type string `json:"type" validate:"name,max=64"`
    Id uint64 `json:"id" validate:"gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```


