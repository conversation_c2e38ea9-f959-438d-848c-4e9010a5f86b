
# **api - fields - API**

## URL: /environment/v1/api/fields ##

## Method : Post ##

## Event ID : EnvRouteFields ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| gatewayId | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "gatewayId": 0
}
```

**struct example:**

```
type OperateGatewayRequest struct {
    GatewayId uint64 `json:"gatewayId" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| key | string | N |  |
| value | object | N |  |
| map_string_Fields.key | string | N |  |
| map_string_Fields.value | array[ConfigureColumn] | N |  |
| ConfigureColumn.id | string | N |  |
| ConfigureColumn.fieldId | integer | N |  |
| ConfigureColumn.domain | string | N |  |
| ConfigureColumn.group | string | N |  |
| ConfigureColumn.section | string | N |  |
| ConfigureColumn.label | string | N |  |
| ConfigureColumn.note | string | N |  |
| ConfigureColumn.placeholder | string | N |  |
| ConfigureColumn.unit | string | N |  |
| ConfigureColumn.priority | integer | N |  |
| ConfigureColumn.necessary | boolean | N |  |
| ConfigureColumn.path | string | N |  |
| ConfigureColumn.dataType | string | N |  |
| ConfigureColumn.type | string | N |  |
| ConfigureColumn.runtimeValue | object | N |  |
| ConfigureColumn.value | object | N |  |
| ConfigureColumn.min | number | N |  |
| ConfigureColumn.max | number | N |  |
| ConfigureColumn.readOnly | boolean | N |  |
| ConfigureColumn.hidden | boolean | N |  |
| ConfigureColumn.validate | string | N |  |
| ConfigureColumn.items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |

**Range of values**

response example:

```
{
    "key": "key",
    "value": {
        "key": "key",
        "value": [
            {
                "id": "id",
                "fieldId": 0,
                "domain": "domain",
                "group": "group",
                "section": "section",
                "label": "label",
                "note": "note",
                "placeholder": "placeholder",
                "unit": "unit",
                "priority": 0,
                "necessary": false,
                "path": "path",
                "dataType": "dataType",
                "type": "type",
                "runtimeValue": {
                },
                "value": {
                },
                "min": "min",
                "max": "max",
                "readOnly": false,
                "hidden": false,
                "validate": "validate",
                "items": [
                    {
                        "value": "value",
                        "text": "text"
                    }
                ]
            }
        ]
    }
}
```



# **api - list - API**

## URL: /environment/v1/api/list ##

## Method : Post ##

## Event ID : EnvRouteAPIList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| gatewayId | integer |  | Y | required,gt=0 |
| category | string |  | N | omitempty,max=128 |
| name | string |  | N | omitempty,max=128 |
| pageIndex | integer |  | N | gte=0 |
| pageSize | integer |  | N | gte=0 |
| orderBy | string |  | N | omitempty,max=128 |
| sort | integer |  | N | oneof=0 1 |

**Range of values**

request example:
```
{
    "gatewayId": 0,
    "category": "category",
    "name": "name",
    "pageIndex": 0,
    "pageSize": 0,
    "orderBy": "orderBy",
    "sort": 0
}
```

**struct example:**

```
type QueryUnboundAPIPageRequest struct {
    GatewayId uint64 `json:"gatewayId" validate:"required,gt=0"`
    Category string `json:"category" validate:"omitempty,max=128"`
    Name string `json:"name" validate:"omitempty,max=128"`
    PageIndex int `json:"pageIndex" validate:"gte=0"`
    PageSize int `json:"pageSize" validate:"gte=0"`
    OrderBy string `json:"orderBy" validate:"omitempty,max=128"`
    Sort int `json:"sort" validate:"oneof=0 1"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| lastPatchId | integer | N |  |
| baseVersionId | integer | N |  |
| linkVersionId | integer | N |  |
| status | string | N |  |
| journal | string | N |  |
| data | map[string]interface | N |  |
| Route | object | N |  |
| Route.id | integer | N |  |
| Route.stackId | integer | N |  |
| Route.apiId | integer | N |  |
| Route.apiUuid | string | N |  |
| Route.eventUuid | string | N |  |
| Route.eventCode | string | N |  |
| Route.eventType | string | N |  |
| Route.proxyBy | integer | N |  |
| Route.currentVersionId | integer | N |  |
| Route.targetVersionId | integer | N |  |
| Route.latestVersionId | integer | N |  |
| Route.name | string | N |  |
| Route.code | string | N |  |
| Route.routeType | string | N |  |
| Route.category | string | N |  |
| Route.version | string | N |  |
| Route.path | string | N |  |
| Route.method | string | N |  |
| Route.configure | map[string]interface | N |  |
| Route.status | string | N |  |
| Route.targetStatus | string | N |  |
| Route.description | string | N |  |
| Route.tenantId | string | N |  |
| Route.creatorId | string | N |  |
| Route.creator | string | N |  |
| Route.updaterId | string | N |  |
| Route.updater | string | N |  |
| Route.createAt | string | N |  |
| Route.updatedAt | string | N |  |
| Route.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "lastPatchId": 0,
    "baseVersionId": 0,
    "linkVersionId": 0,
    "status": "status",
    "journal": "journal",
    "data": {
    },
    "Route": {
        "id": 0,
        "stackId": 0,
        "apiId": 0,
        "apiUuid": "apiUuid",
        "eventUuid": "eventUuid",
        "eventCode": "eventCode",
        "eventType": "eventType",
        "proxyBy": 0,
        "currentVersionId": 0,
        "targetVersionId": 0,
        "latestVersionId": 0,
        "name": "name",
        "code": "code",
        "routeType": "routeType",
        "category": "category",
        "version": "version",
        "path": "path",
        "method": "method",
        "configure": {
        },
        "status": "status",
        "targetStatus": "targetStatus",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```


