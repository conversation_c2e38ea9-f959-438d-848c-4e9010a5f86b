
# **field - create - API**

## URL: /environment/v1/field/create ##

## Method : Post ##

## Event ID : EnvFieldCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| sectionId | integer |  | Y | required,gt=0 |
| columnId | string |  | Y | required,gt=0 |
| note | string |  | N | omitempty,max=255 |
| priority | integer |  | N | min=0,max=9 |
| path | string |  | N | omitempty,min=2,max=128 |
| value | string |  | N | omitempty |

**Range of values**

request example:
```
{
    "sectionId": 0,
    "columnId": "columnId",
    "note": "note",
    "priority": 0,
    "path": "path",
    "value": "value"
}
```

**struct example:**

```
type CreateFieldRequest struct {
    SectionId uint64 `json:"sectionId" validate:"required,gt=0"`
    ColumnId string `json:"columnId" validate:"required,gt=0"`
    Note string `json:"note" validate:"omitempty,max=255"`
    Priority int `json:"priority" validate:"min=0,max=9"`
    Path string `json:"path" validate:"omitempty,min=2,max=128"`
    Value string `json:"value" validate:"omitempty"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **field - delete - API**

## URL: /environment/v1/field/delete ##

## Method : Post ##

## Event ID : EnvFieldDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type fieldIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **field - detail - API**

## URL: /environment/v1/field/detail ##

## Method : Post ##

## Event ID : EnvFieldDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type fieldIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| domain | string | N |  |
| group | string | N |  |
| label | string | N |  |
| columnNote | string | N |  |
| placeholder | string | N |  |
| unit | string | N |  |
| necessary | boolean | N |  |
| columnPath | string | N |  |
| type | string | N |  |
| dataType | string | N |  |
| columnValue | string | N |  |
| min | number | N |  |
| max | number | N |  |
| readOnly | boolean | N |  |
| hidden | boolean | N |  |
| validate | string | N |  |
| items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |
| Field | object | N |  |
| Field.id | integer | N |  |
| Field.stackId | integer | N |  |
| Field.sectionId | integer | N |  |
| Field.columnId | string | N |  |
| Field.scope | integer | N |  |
| Field.note | string | N |  |
| Field.priority | integer | N |  |
| Field.path | string | N |  |
| Field.value | string | N |  |
| Field.tenantId | string | N |  |
| Field.updaterId | string | N |  |
| Field.updater | string | N |  |
| Field.creatorId | string | N |  |
| Field.creator | string | N |  |
| Field.createAt | string | N |  |
| Field.updatedAt | string | N |  |
| Field.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "domain": "domain",
    "group": "group",
    "label": "label",
    "columnNote": "columnNote",
    "placeholder": "placeholder",
    "unit": "unit",
    "necessary": false,
    "columnPath": "columnPath",
    "type": "type",
    "dataType": "dataType",
    "columnValue": "columnValue",
    "min": "min",
    "max": "max",
    "readOnly": false,
    "hidden": false,
    "validate": "validate",
    "items": [
        {
            "value": "value",
            "text": "text"
        }
    ],
    "Field": {
        "id": 0,
        "stackId": 0,
        "sectionId": 0,
        "columnId": "columnId",
        "scope": 0,
        "note": "note",
        "priority": 0,
        "path": "path",
        "value": "value",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **field - list - API**

## URL: /environment/v1/field/list ##

## Method : Post ##

## Event ID : EnvFieldList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| sectionId | integer |  | N |  |
| sectionIdList | array[uint64] |  | N |  |
| pathList | array[string] |  | N |  |
| tenantId | string |  | N |  |
| scope | integer |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "sectionId": 0,
    "sectionIdList": [
    ],
    "pathList": [
    ],
    "tenantId": "tenantId",
    "scope": 0
}
```

**struct example:**

```
type FieldFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    SectionId uint64 `json:"sectionId"`
    SectionIdList []uint64 `json:"sectionIdList"`
    PathList []string `json:"pathList"`
    TenantId string `json:"tenantId"`
    Scope int `json:"scope"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| domain | string | N |  |
| group | string | N |  |
| label | string | N |  |
| columnNote | string | N |  |
| placeholder | string | N |  |
| unit | string | N |  |
| necessary | boolean | N |  |
| columnPath | string | N |  |
| type | string | N |  |
| dataType | string | N |  |
| columnValue | string | N |  |
| min | number | N |  |
| max | number | N |  |
| readOnly | boolean | N |  |
| hidden | boolean | N |  |
| validate | string | N |  |
| items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |
| Field | object | N |  |
| Field.id | integer | N |  |
| Field.stackId | integer | N |  |
| Field.sectionId | integer | N |  |
| Field.columnId | string | N |  |
| Field.scope | integer | N |  |
| Field.note | string | N |  |
| Field.priority | integer | N |  |
| Field.path | string | N |  |
| Field.value | string | N |  |
| Field.tenantId | string | N |  |
| Field.updaterId | string | N |  |
| Field.updater | string | N |  |
| Field.creatorId | string | N |  |
| Field.creator | string | N |  |
| Field.createAt | string | N |  |
| Field.updatedAt | string | N |  |
| Field.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "domain": "domain",
    "group": "group",
    "label": "label",
    "columnNote": "columnNote",
    "placeholder": "placeholder",
    "unit": "unit",
    "necessary": false,
    "columnPath": "columnPath",
    "type": "type",
    "dataType": "dataType",
    "columnValue": "columnValue",
    "min": "min",
    "max": "max",
    "readOnly": false,
    "hidden": false,
    "validate": "validate",
    "items": [
        {
            "value": "value",
            "text": "text"
        }
    ],
    "Field": {
        "id": 0,
        "stackId": 0,
        "sectionId": 0,
        "columnId": "columnId",
        "scope": 0,
        "note": "note",
        "priority": 0,
        "path": "path",
        "value": "value",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **field - page - API**

## URL: /environment/v1/field/page ##

## Method : Post ##

## Event ID : EnvFieldPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| sectionId | integer |  | N |  |
| sectionIdList | array[uint64] |  | N |  |
| pathList | array[string] |  | N |  |
| tenantId | string |  | N |  |
| scope | integer |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "sectionId": 0,
    "sectionIdList": [
    ],
    "pathList": [
    ],
    "tenantId": "tenantId",
    "scope": 0
}
```

**struct example:**

```
type FieldFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    SectionId uint64 `json:"sectionId"`
    SectionIdList []uint64 `json:"sectionIdList"`
    PathList []string `json:"pathList"`
    TenantId string `json:"tenantId"`
    Scope int `json:"scope"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **field - update - API**

## URL: /environment/v1/field/update ##

## Method : Post ##

## Event ID : EnvFieldUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |
| note | string |  | N | omitempty,max=255 |
| priority | integer |  | N | min=0,max=9 |
| path | string |  | N | omitempty,min=2,max=128 |
| value | string |  | N | omitempty |

**Range of values**

request example:
```
{
    "id": 0,
    "note": "note",
    "priority": 0,
    "path": "path",
    "value": "value"
}
```

**struct example:**

```
type UpdateFieldRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
    Note string `json:"note" validate:"omitempty,max=255"`
    Priority int `json:"priority" validate:"min=0,max=9"`
    Path string `json:"path" validate:"omitempty,min=2,max=128"`
    Value string `json:"value" validate:"omitempty"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


