
# **db - create - API**

## URL: /environment/v1/db/create ##

## Method : Post ##

## Event ID : EnvDBInstanceCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| stackId | integer |  | Y | required,gt=0 |
| suId | integer |  | Y | required,gt=0 |
| dbId | integer |  | Y | required,gt=0 |
| rdbClusterId | integer |  | Y | required,gt=0 |
| dbType | string |  | N | name,max=64 |
| name | string |  | N | name,max=64 |
| ip | string |  | Y | required,max=64 |
| port | integer |  | Y | required,gt=0 |
| adminAccount | string |  | N | name,max=64 |
| adminPassword | string |  | Y | required,max=64 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "stackId": 0,
    "suId": 0,
    "dbId": 0,
    "rdbClusterId": 0,
    "dbType": "dbType",
    "name": "name",
    "ip": "ip",
    "port": 0,
    "adminAccount": "adminAccount",
    "adminPassword": "adminPassword",
    "description": "description"
}
```

**struct example:**

```
type CreateDBInstanceRequest struct {
    StackId uint64 `json:"stackId" validate:"required,gt=0"`
    SuId uint64 `json:"suId" validate:"required,gt=0"`
    DbId uint64 `json:"dbId" validate:"required,gt=0"`
    RdbClusterId uint64 `json:"rdbClusterId" validate:"required,gt=0"`
    DbType string `json:"dbType" validate:"name,max=64"`
    Name string `json:"name" validate:"name,max=64"`
    Ip string `json:"ip" validate:"required,max=64"`
    Port int `json:"port" validate:"required,gt=0"`
    AdminAccount string `json:"adminAccount" validate:"name,max=64"`
    AdminPassword string `json:"adminPassword" validate:"required,max=64"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **db - detail - API**

## URL: /environment/v1/db/detail ##

## Method : Post ##

## Event ID : EnvDBInstanceDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type databaseIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| stackId | integer | N |  |
| envId | integer | N |  |
| suTypeId | integer | N |  |
| suTypeCode | string | N |  |
| name | string | N |  |
| code | string | N |  |
| repoDbSubId | integer | N |  |
| repoDbSubName | string | N |  |
| DBInstance | object | N |  |
| DBInstance.id | integer | N |  |
| DBInstance.planId | integer | N |  |
| DBInstance.refId | integer | N |  |
| DBInstance.suId | integer | N |  |
| DBInstance.databaseId | integer | N |  |
| DBInstance.currentVersionId | integer | N |  |
| DBInstance.rdbClusterId | integer | N |  |
| DBInstance.type | string | N |  |
| DBInstance.name | string | N |  |
| DBInstance.uuid | string | N |  |
| DBInstance.ip | string | N |  |
| DBInstance.port | integer | N |  |
| DBInstance.adminAccount | string | N |  |
| DBInstance.adminPassword | string | N |  |
| DBInstance.configure | map[string]interface | N |  |
| DBInstance.status | string | N |  |
| DBInstance.description | string | N |  |
| DBInstance.tenantId | string | N |  |
| DBInstance.creatorId | string | N |  |
| DBInstance.creator | string | N |  |
| DBInstance.updaterId | string | N |  |
| DBInstance.updater | string | N |  |
| DBInstance.createAt | string | N |  |
| DBInstance.updatedAt | string | N |  |
| DBInstance.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "stackId": 0,
    "envId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "name": "name",
    "code": "code",
    "repoDbSubId": 0,
    "repoDbSubName": "repoDbSubName",
    "DBInstance": {
        "id": 0,
        "planId": 0,
        "refId": 0,
        "suId": 0,
        "databaseId": 0,
        "currentVersionId": 0,
        "rdbClusterId": 0,
        "type": "type",
        "name": "name",
        "uuid": "uuid",
        "ip": "ip",
        "port": 0,
        "adminAccount": "adminAccount",
        "adminPassword": "adminPassword",
        "configure": {
        },
        "status": "status",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **db - list - API**

## URL: /environment/v1/db/list ##

## Method : Post ##

## Event ID : EnvDBInstanceList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| suId | integer |  | N |  |
| databaseId | integer |  | N |  |
| tenantId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "suId": 0,
    "databaseId": 0,
    "tenantId": "tenantId"
}
```

**struct example:**

```
type DBInstanceFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    SuId uint64 `json:"suId"`
    DatabaseId uint64 `json:"databaseId"`
    TenantId string `json:"tenantId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| stackId | integer | N |  |
| envId | integer | N |  |
| suTypeId | integer | N |  |
| suTypeCode | string | N |  |
| name | string | N |  |
| code | string | N |  |
| repoDbSubId | integer | N |  |
| repoDbSubName | string | N |  |
| DBInstance | object | N |  |
| DBInstance.id | integer | N |  |
| DBInstance.planId | integer | N |  |
| DBInstance.refId | integer | N |  |
| DBInstance.suId | integer | N |  |
| DBInstance.databaseId | integer | N |  |
| DBInstance.currentVersionId | integer | N |  |
| DBInstance.rdbClusterId | integer | N |  |
| DBInstance.type | string | N |  |
| DBInstance.name | string | N |  |
| DBInstance.uuid | string | N |  |
| DBInstance.ip | string | N |  |
| DBInstance.port | integer | N |  |
| DBInstance.adminAccount | string | N |  |
| DBInstance.adminPassword | string | N |  |
| DBInstance.configure | map[string]interface | N |  |
| DBInstance.status | string | N |  |
| DBInstance.description | string | N |  |
| DBInstance.tenantId | string | N |  |
| DBInstance.creatorId | string | N |  |
| DBInstance.creator | string | N |  |
| DBInstance.updaterId | string | N |  |
| DBInstance.updater | string | N |  |
| DBInstance.createAt | string | N |  |
| DBInstance.updatedAt | string | N |  |
| DBInstance.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "stackId": 0,
    "envId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "name": "name",
    "code": "code",
    "repoDbSubId": 0,
    "repoDbSubName": "repoDbSubName",
    "DBInstance": {
        "id": 0,
        "planId": 0,
        "refId": 0,
        "suId": 0,
        "databaseId": 0,
        "currentVersionId": 0,
        "rdbClusterId": 0,
        "type": "type",
        "name": "name",
        "uuid": "uuid",
        "ip": "ip",
        "port": 0,
        "adminAccount": "adminAccount",
        "adminPassword": "adminPassword",
        "configure": {
        },
        "status": "status",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **db - page - API**

## URL: /environment/v1/db/page ##

## Method : Post ##

## Event ID : EnvDBInstancePage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| suId | integer |  | N |  |
| databaseId | integer |  | N |  |
| tenantId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "suId": 0,
    "databaseId": 0,
    "tenantId": "tenantId"
}
```

**struct example:**

```
type DBInstanceFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    SuId uint64 `json:"suId"`
    DatabaseId uint64 `json:"databaseId"`
    TenantId string `json:"tenantId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **db - patch - API**

## URL: /environment/v1/db/patch ##

## Method : Post ##

## Event ID : EnvDBIVersionPatch ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| version | string |  | N | omitempty,max=64 |
| note | string |  | N | omitempty,max=255 |
| versions | array[uint64] |  | Y | gt=0,dive,required |
| patches | array[uint64] |  | Y | gt=0,dive,required |

**Range of values**

request example:
```
{
    "version": "version",
    "note": "note",
    "versions": [
    ],
    "patches": [
    ]
}
```

**struct example:**

```
type PatchDBInstanceRequest struct {
    Version string `json:"version" validate:"omitempty,max=64"`
    Note string `json:"note" validate:"omitempty,max=255"`
    Versions []uint64 `json:"versions" validate:"gt=0,dive,required"`
    Patches []uint64 `json:"patches" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **db - update - API**

## URL: /environment/v1/db/update ##

## Method : Post ##

## Event ID : EnvDBInstanceUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "id": 0,
    "description": "description"
}
```

**struct example:**

```
type UpdateDBInstanceRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **db - version delete - API**

## URL: /environment/v1/db/version/delete ##

## Method : Post ##

## Event ID : EnvDBIVersionDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type dbInstanceVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| suId | integer | N |  |
| databaseId | integer | N |  |
| rdbClusterId | integer | N |  |
| dbType | string | N |  |
| name | string | N |  |
| ip | string | N |  |
| port | integer | N |  |
| adminAccount | string | N |  |
| adminPassword | string | N |  |
| status | string | N |  |
| DBInstanceVersion | object | N |  |
| DBInstanceVersion.id | integer | N |  |
| DBInstanceVersion.dbInstanceId | integer | N |  |
| DBInstanceVersion.lastPatchId | integer | N |  |
| DBInstanceVersion.baseVersionId | integer | N |  |
| DBInstanceVersion.linkVersionId | integer | N |  |
| DBInstanceVersion.status | string | N |  |
| DBInstanceVersion.journal | string | N |  |
| DBInstanceVersion.data | map[string]interface | N |  |
| DBInstanceVersion.version | string | N |  |
| DBInstanceVersion.description | string | N |  |
| DBInstanceVersion.tenantId | string | N |  |
| DBInstanceVersion.updaterId | string | N |  |
| DBInstanceVersion.updater | string | N |  |
| DBInstanceVersion.creatorId | string | N |  |
| DBInstanceVersion.creator | string | N |  |
| DBInstanceVersion.createAt | string | N |  |
| DBInstanceVersion.updatedAt | string | N |  |
| DBInstanceVersion.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "suId": 0,
    "databaseId": 0,
    "rdbClusterId": 0,
    "dbType": "dbType",
    "name": "name",
    "ip": "ip",
    "port": 0,
    "adminAccount": "adminAccount",
    "adminPassword": "adminPassword",
    "status": "status",
    "DBInstanceVersion": {
        "id": 0,
        "dbInstanceId": 0,
        "lastPatchId": 0,
        "baseVersionId": 0,
        "linkVersionId": 0,
        "status": "status",
        "journal": "journal",
        "data": {
        },
        "version": "version",
        "description": "description",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **db - version detail - API**

## URL: /environment/v1/db/version/detail ##

## Method : Post ##

## Event ID : EnvDBIVersionDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type dbInstanceVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| suId | integer | N |  |
| databaseId | integer | N |  |
| rdbClusterId | integer | N |  |
| dbType | string | N |  |
| name | string | N |  |
| ip | string | N |  |
| port | integer | N |  |
| adminAccount | string | N |  |
| adminPassword | string | N |  |
| status | string | N |  |
| DBInstanceVersion | object | N |  |
| DBInstanceVersion.id | integer | N |  |
| DBInstanceVersion.dbInstanceId | integer | N |  |
| DBInstanceVersion.lastPatchId | integer | N |  |
| DBInstanceVersion.baseVersionId | integer | N |  |
| DBInstanceVersion.linkVersionId | integer | N |  |
| DBInstanceVersion.status | string | N |  |
| DBInstanceVersion.journal | string | N |  |
| DBInstanceVersion.data | map[string]interface | N |  |
| DBInstanceVersion.version | string | N |  |
| DBInstanceVersion.description | string | N |  |
| DBInstanceVersion.tenantId | string | N |  |
| DBInstanceVersion.updaterId | string | N |  |
| DBInstanceVersion.updater | string | N |  |
| DBInstanceVersion.creatorId | string | N |  |
| DBInstanceVersion.creator | string | N |  |
| DBInstanceVersion.createAt | string | N |  |
| DBInstanceVersion.updatedAt | string | N |  |
| DBInstanceVersion.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "suId": 0,
    "databaseId": 0,
    "rdbClusterId": 0,
    "dbType": "dbType",
    "name": "name",
    "ip": "ip",
    "port": 0,
    "adminAccount": "adminAccount",
    "adminPassword": "adminPassword",
    "status": "status",
    "DBInstanceVersion": {
        "id": 0,
        "dbInstanceId": 0,
        "lastPatchId": 0,
        "baseVersionId": 0,
        "linkVersionId": 0,
        "status": "status",
        "journal": "journal",
        "data": {
        },
        "version": "version",
        "description": "description",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **db - version fields - API**

## URL: /environment/v1/db/version/fields ##

## Method : Post ##

## Event ID : EnvDBIVersionFields ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| instanceVersionId | integer |  | Y | required,gt=0 |
| sectionId | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "instanceVersionId": 0,
    "sectionId": 0
}
```

**struct example:**

```
type QueryDBInstanceFieldRequest struct {
    InstanceVersionId uint64 `json:"instanceVersionId" validate:"required,gt=0"`
    SectionId uint64 `json:"sectionId" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| requestId | string | N |  |
| fields | array[FieldInfo] | N |  |
| FieldInfo.domain | string | N |  |
| FieldInfo.group | string | N |  |
| FieldInfo.label | string | N |  |
| FieldInfo.columnNote | string | N |  |
| FieldInfo.placeholder | string | N |  |
| FieldInfo.unit | string | N |  |
| FieldInfo.necessary | boolean | N |  |
| FieldInfo.columnPath | string | N |  |
| FieldInfo.type | string | N |  |
| FieldInfo.dataType | string | N |  |
| FieldInfo.columnValue | string | N |  |
| FieldInfo.min | number | N |  |
| FieldInfo.max | number | N |  |
| FieldInfo.readOnly | boolean | N |  |
| FieldInfo.hidden | boolean | N |  |
| FieldInfo.validate | string | N |  |
| FieldInfo.items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |
| FieldInfo.Field | object | N |  |
| Field.id | integer | N |  |
| Field.stackId | integer | N |  |
| Field.sectionId | integer | N |  |
| Field.columnId | string | N |  |
| Field.scope | integer | N |  |
| Field.note | string | N |  |
| Field.priority | integer | N |  |
| Field.path | string | N |  |
| Field.value | string | N |  |
| Field.tenantId | string | N |  |
| Field.updaterId | string | N |  |
| Field.updater | string | N |  |
| Field.creatorId | string | N |  |
| Field.creator | string | N |  |
| Field.createAt | string | N |  |
| Field.updatedAt | string | N |  |
| Field.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "requestId": "requestId",
    "fields": [
        {
            "domain": "domain",
            "group": "group",
            "label": "label",
            "columnNote": "columnNote",
            "placeholder": "placeholder",
            "unit": "unit",
            "necessary": false,
            "columnPath": "columnPath",
            "type": "type",
            "dataType": "dataType",
            "columnValue": "columnValue",
            "min": "min",
            "max": "max",
            "readOnly": false,
            "hidden": false,
            "validate": "validate",
            "items": [
                {
                    "value": "value",
                    "text": "text"
                }
            ],
            "Field": {
                "id": 0,
                "stackId": 0,
                "sectionId": 0,
                "columnId": "columnId",
                "scope": 0,
                "note": "note",
                "priority": 0,
                "path": "path",
                "value": "value",
                "tenantId": "tenantId",
                "updaterId": "updaterId",
                "updater": "updater",
                "creatorId": "creatorId",
                "creator": "creator",
                "createAt": "createAt",
                "updatedAt": "updatedAt",
                "deletedAt": "deletedAt"
            }
        }
    ]
}
```



# **db - version list - API**

## URL: /environment/v1/db/version/list ##

## Method : Post ##

## Event ID : EnvDBIVersionList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| dbInstanceId | integer |  | N |  |
| tenantId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "dbInstanceId": 0,
    "tenantId": "tenantId"
}
```

**struct example:**

```
type DBInstanceVersionFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    DbInstanceId uint64 `json:"dbInstanceId"`
    TenantId string `json:"tenantId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| suId | integer | N |  |
| databaseId | integer | N |  |
| rdbClusterId | integer | N |  |
| dbType | string | N |  |
| name | string | N |  |
| ip | string | N |  |
| port | integer | N |  |
| adminAccount | string | N |  |
| adminPassword | string | N |  |
| status | string | N |  |
| DBInstanceVersion | object | N |  |
| DBInstanceVersion.id | integer | N |  |
| DBInstanceVersion.dbInstanceId | integer | N |  |
| DBInstanceVersion.lastPatchId | integer | N |  |
| DBInstanceVersion.baseVersionId | integer | N |  |
| DBInstanceVersion.linkVersionId | integer | N |  |
| DBInstanceVersion.status | string | N |  |
| DBInstanceVersion.journal | string | N |  |
| DBInstanceVersion.data | map[string]interface | N |  |
| DBInstanceVersion.version | string | N |  |
| DBInstanceVersion.description | string | N |  |
| DBInstanceVersion.tenantId | string | N |  |
| DBInstanceVersion.updaterId | string | N |  |
| DBInstanceVersion.updater | string | N |  |
| DBInstanceVersion.creatorId | string | N |  |
| DBInstanceVersion.creator | string | N |  |
| DBInstanceVersion.createAt | string | N |  |
| DBInstanceVersion.updatedAt | string | N |  |
| DBInstanceVersion.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "suId": 0,
    "databaseId": 0,
    "rdbClusterId": 0,
    "dbType": "dbType",
    "name": "name",
    "ip": "ip",
    "port": 0,
    "adminAccount": "adminAccount",
    "adminPassword": "adminPassword",
    "status": "status",
    "DBInstanceVersion": {
        "id": 0,
        "dbInstanceId": 0,
        "lastPatchId": 0,
        "baseVersionId": 0,
        "linkVersionId": 0,
        "status": "status",
        "journal": "journal",
        "data": {
        },
        "version": "version",
        "description": "description",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **db - version page - API**

## URL: /environment/v1/db/version/page ##

## Method : Post ##

## Event ID : EnvDBIVersionPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| dbInstanceId | integer |  | N |  |
| tenantId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "dbInstanceId": 0,
    "tenantId": "tenantId"
}
```

**struct example:**

```
type DBInstanceVersionFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    DbInstanceId uint64 `json:"dbInstanceId"`
    TenantId string `json:"tenantId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **db - version revises - API**

## URL: /environment/v1/db/version/revises ##

## Method : Post ##

## Event ID : EnvDBIVersionRevises ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type dbInstanceVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| suId | integer | N |  |
| databaseId | integer | N |  |
| rdbClusterId | integer | N |  |
| dbType | string | N |  |
| name | string | N |  |
| ip | string | N |  |
| port | integer | N |  |
| adminAccount | string | N |  |
| adminPassword | string | N |  |
| status | string | N |  |
| DBInstanceVersion | object | N |  |
| DBInstanceVersion.id | integer | N |  |
| DBInstanceVersion.dbInstanceId | integer | N |  |
| DBInstanceVersion.lastPatchId | integer | N |  |
| DBInstanceVersion.baseVersionId | integer | N |  |
| DBInstanceVersion.linkVersionId | integer | N |  |
| DBInstanceVersion.status | string | N |  |
| DBInstanceVersion.journal | string | N |  |
| DBInstanceVersion.data | map[string]interface | N |  |
| DBInstanceVersion.version | string | N |  |
| DBInstanceVersion.description | string | N |  |
| DBInstanceVersion.tenantId | string | N |  |
| DBInstanceVersion.updaterId | string | N |  |
| DBInstanceVersion.updater | string | N |  |
| DBInstanceVersion.creatorId | string | N |  |
| DBInstanceVersion.creator | string | N |  |
| DBInstanceVersion.createAt | string | N |  |
| DBInstanceVersion.updatedAt | string | N |  |
| DBInstanceVersion.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "suId": 0,
    "databaseId": 0,
    "rdbClusterId": 0,
    "dbType": "dbType",
    "name": "name",
    "ip": "ip",
    "port": 0,
    "adminAccount": "adminAccount",
    "adminPassword": "adminPassword",
    "status": "status",
    "DBInstanceVersion": {
        "id": 0,
        "dbInstanceId": 0,
        "lastPatchId": 0,
        "baseVersionId": 0,
        "linkVersionId": 0,
        "status": "status",
        "journal": "journal",
        "data": {
        },
        "version": "version",
        "description": "description",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **db - version update - API**

## URL: /environment/v1/db/version/update ##

## Method : Post ##

## Event ID : EnvDBIVersionUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |
| note | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "id": 0,
    "note": "note"
}
```

**struct example:**

```
type UpdateDBInstanceVersionRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
    Note string `json:"note" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


