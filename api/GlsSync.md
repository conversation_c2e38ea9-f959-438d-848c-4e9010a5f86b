
# **GlsSync - confirm - API**

## URL: /environment/v1/GlsSync/confirm ##

## Method : Post ##

## Event ID : EnvGlsSyncConfirm ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| glsId | integer |  | Y | required |
| confirm | string |  | Y | required |

**Range of values**

request example:
```
{
    "glsId": 0,
    "confirm": "confirm"
}
```

**struct example:**

```
type GlsSyncDataConfirmRequest struct {
    GlsId uint64 `json:"glsId" validate:"required"`
    Confirm string `json:"confirm" validate:"required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| result | boolean | N |  |

**Range of values**

response example:

```
{
    "result": false
}
```



# **GlsSync - create - API**

## URL: /environment/v1/GlsSync/create ##

## Method : Post ##

## Event ID : EnvGlsSyncCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| envId | integer |  | Y | required |
| tenantId | string |  | N |  |
| suTypeId | integer |  | N |  |
| suTypeCode | string |  | N |  |
| suTypeName | string |  | N |  |
| primaryId | integer |  | N |  |
| primaryCode | string |  | N |  |
| primaryName | string |  | N |  |
| primaryUuid | string |  | Y | required |
| secondaryCode | string |  | N |  |
| secondaryUuid | string |  | Y | required |
| secondaryName | string |  | N |  |
| list | array[GlsShardingDataRecord] |  | N |  |
| GlsShardingDataRecord.Id | integer |  | N |  |
| GlsShardingDataRecord.key | string |  | Y | required,max=255 |
| GlsShardingDataRecord.value | string |  | N |  |
| GlsShardingDataRecord.name | string |  | N |  |

**Range of values**

request example:
```
{
    "envId": 0,
    "tenantId": "tenantId",
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "suTypeName": "suTypeName",
    "primaryId": 0,
    "primaryCode": "primaryCode",
    "primaryName": "primaryName",
    "primaryUuid": "primaryUuid",
    "secondaryCode": "secondaryCode",
    "secondaryUuid": "secondaryUuid",
    "secondaryName": "secondaryName",
    "list": [
        {
            "Id": 0,
            "key": "key",
            "value": "value",
            "name": "name"
        }
    ]
}
```

**struct example:**

```
type GlsShardingDataGroup struct {
    EnvId uint64 `json:"envId" validate:"required"`
    TenantId string `json:"tenantId"`
    SuTypeId uint64 `json:"suTypeId"`
    SuTypeCode string `json:"suTypeCode"`
    SuTypeName string `json:"suTypeName"`
    PrimaryId uint64 `json:"primaryId"`
    PrimaryCode string `json:"primaryCode"`
    PrimaryName string `json:"primaryName"`
    PrimaryUuid string `json:"primaryUuid" validate:"required"`
    SecondaryCode string `json:"secondaryCode"`
    SecondaryUuid string `json:"secondaryUuid" validate:"required"`
    SecondaryName string `json:"secondaryName"`
    List []struct {
            Id uint64 `json:"Id"`
            Key string `json:"key" validate:"required,max=255"`
            Value string `json:"value"`
            Name string `json:"name"`
        } `json:"list"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| confirm | string | N |  |
| data | array[Data] | N |  |
| Data.suType | string | N |  |
| Data.glsData | object | N |  |
| GlsData.glsSu | array[GlsSu] | N |  |
| GlsSu.id | string | N |  |
| GlsSu.curSuId | string | N |  |
| GlsSu.curWeight | integer | N |  |
| GlsSu.curQuota | integer | N |  |
| GlsSu.runSuId | string | N |  |
| GlsSu.runWeight | integer | N |  |
| GlsSu.runQuota | integer | N |  |
| GlsSu.comparison | integer | N |  |
| GlsData.primarySharding | array[PrimarySharding] | N |  |
| PrimarySharding.id | string | N |  |
| PrimarySharding.curName | string | N |  |
| PrimarySharding.curValue | string | N |  |
| PrimarySharding.runName | string | N |  |
| PrimarySharding.runValue | string | N |  |
| PrimarySharding.comparison | integer | N |  |
| PrimarySharding.secondarySharding | array[SecondarySharding] | N |  |
| SecondarySharding.id | string | N |  |
| SecondarySharding.curName | string | N |  |
| SecondarySharding.curType | string | N |  |
| SecondarySharding.runName | string | N |  |
| SecondarySharding.runType | string | N |  |
| SecondarySharding.comparison | integer | N |  |
| SecondarySharding.shardingElement | array[ShardingElement] | N |  |
| ShardingElement.id | string | N |  |
| ShardingElement.curKey | string | N |  |
| ShardingElement.curValue | string | N |  |
| ShardingElement.runKey | string | N |  |
| ShardingElement.runValue | string | N |  |
| ShardingElement.comparison | integer | N |  |
| GlsData.topic | array[Topic] | N |  |
| Topic.id | string | N |  |
| Topic.curType | string | N |  |
| Topic.curCode | string | N |  |
| Topic.runType | string | N |  |
| Topic.runCode | string | N |  |
| Topic.comparison | integer | N |  |

**Range of values**

response example:

```
{
    "confirm": "confirm",
    "data": [
        {
            "suType": "suType",
            "glsData": {
                "glsSu": [
                    {
                        "id": "id",
                        "curSuId": "curSuId",
                        "curWeight": 0,
                        "curQuota": 0,
                        "runSuId": "runSuId",
                        "runWeight": 0,
                        "runQuota": 0,
                        "comparison": 0
                    }
                ],
                "primarySharding": [
                    {
                        "id": "id",
                        "curName": "curName",
                        "curValue": "curValue",
                        "runName": "runName",
                        "runValue": "runValue",
                        "comparison": 0,
                        "secondarySharding": [
                            {
                                "id": "id",
                                "curName": "curName",
                                "curType": "curType",
                                "runName": "runName",
                                "runType": "runType",
                                "comparison": 0,
                                "shardingElement": [
                                    {
                                        "id": "id",
                                        "curKey": "curKey",
                                        "curValue": "curValue",
                                        "runKey": "runKey",
                                        "runValue": "runValue",
                                        "comparison": 0
                                    }
                                ]
                            }
                        ]
                    }
                ],
                "topic": [
                    {
                        "id": "id",
                        "curType": "curType",
                        "curCode": "curCode",
                        "runType": "runType",
                        "runCode": "runCode",
                        "comparison": 0
                    }
                ]
            }
        }
    ]
}
```



# **GlsSync - delete - API**

## URL: /environment/v1/GlsSync/delete ##

## Method : Post ##

## Event ID : EnvGlsSyncDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| idList | array[uint64] |  | Y | required |

**Range of values**

request example:
```
{
    "idList": [
    ]
}
```

**struct example:**

```
type DeleteGlsShardingDataRequest struct {
    IdList []uint64 `json:"idList" validate:"required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **GlsSync - detail - API**

## URL: /environment/v1/GlsSync/detail ##

## Method : Post ##

## Event ID : EnvGlsSyncDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type Request struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| org | string | N |  |
| wks | string | Y | required |
| env | string | Y | required |
| incrementalFlag | boolean | N |  |
| data | array[GlsPrepareSyncSuType] | N |  |
| GlsPrepareSyncSuType.suType | string | Y | required |
| GlsPrepareSyncSuType.primarySharding | array[PrimaryShardingDefine] | Y | required |
| PrimaryShardingDefine.id | integer | Y | required |
| PrimaryShardingDefine.name | string | Y | required |
| PrimaryShardingDefine.value | string | Y | required |
| PrimaryShardingDefine.secondarySharding | array[SecondaryShardingDefine] | N |  |
| SecondaryShardingDefine.id | integer | Y | required |
| SecondaryShardingDefine.name | string | Y | required |
| SecondaryShardingDefine.type | string | Y | required |
| SecondaryShardingDefine.shardingElement | array[ShardingElementDefine] | Y | required |
| ShardingElementDefine.id | integer | Y | required |
| ShardingElementDefine.key | string | Y | required |
| ShardingElementDefine.value | string | Y | required |
| GlsPrepareSyncSuType.glsSu | array[GlsSuDefine] | Y | required |
| GlsSuDefine.id | integer | Y | required |
| GlsSuDefine.suId | string | Y | required |
| GlsSuDefine.weight | integer | N |  |
| GlsSuDefine.quota | integer | N |  |
| GlsPrepareSyncSuType.topic | array[GlsTopicDefine] | Y | required |
| GlsTopicDefine.id | integer | Y | required |
| GlsTopicDefine.type | string | Y | required |
| GlsTopicDefine.code | string | Y | required |
| GlsTopicDefine.serviceId | string | N |  |
| GlsTopicDefine.serviceType | string | N |  |

**Range of values**

response example:

```
{
    "org": "org",
    "wks": "wks",
    "env": "env",
    "incrementalFlag": false,
    "data": [
        {
            "suType": "suType",
            "primarySharding": [
                {
                    "id": 0,
                    "name": "name",
                    "value": "value",
                    "secondarySharding": [
                        {
                            "id": 0,
                            "name": "name",
                            "type": "type",
                            "shardingElement": [
                                {
                                    "id": 0,
                                    "key": "key",
                                    "value": "value"
                                }
                            ]
                        }
                    ]
                }
            ],
            "glsSu": [
                {
                    "id": 0,
                    "suId": "suId",
                    "weight": 0,
                    "quota": 0
                }
            ],
            "topic": [
                {
                    "id": 0,
                    "type": "type",
                    "code": "code",
                    "serviceId": "serviceId",
                    "serviceType": "serviceType"
                }
            ]
        }
    ]
}
```



# **GlsSync - export - API**

## URL: /environment/v1/GlsSync/export ##

## Method : Post ##

## Event ID : EnvGlsSyncExport ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| glsId | integer |  | Y | required |

**Range of values**

request example:
```
{
    "glsId": 0
}
```

**struct example:**

```
type GlsSyncDataExportRequest struct {
    GlsId uint64 `json:"glsId" validate:"required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| request | object | N |  |
| GlsSyncExportData.org | string | N |  |
| GlsSyncExportData.wks | string | N |  |
| GlsSyncExportData.env | string | N |  |
| GlsSyncExportData.data | array[GlsPrepareSyncSuType] | N |  |
| GlsPrepareSyncSuType.suType | string | Y | required |
| GlsPrepareSyncSuType.primarySharding | array[PrimaryShardingDefine] | Y | required |
| PrimaryShardingDefine.id | integer | Y | required |
| PrimaryShardingDefine.name | string | Y | required |
| PrimaryShardingDefine.value | string | Y | required |
| PrimaryShardingDefine.secondarySharding | array[SecondaryShardingDefine] | N |  |
| SecondaryShardingDefine.id | integer | Y | required |
| SecondaryShardingDefine.name | string | Y | required |
| SecondaryShardingDefine.type | string | Y | required |
| SecondaryShardingDefine.shardingElement | array[ShardingElementDefine] | Y | required |
| ShardingElementDefine.id | integer | Y | required |
| ShardingElementDefine.key | string | Y | required |
| ShardingElementDefine.value | string | Y | required |
| GlsPrepareSyncSuType.glsSu | array[GlsSuDefine] | Y | required |
| GlsSuDefine.id | integer | Y | required |
| GlsSuDefine.suId | string | Y | required |
| GlsSuDefine.weight | integer | N |  |
| GlsSuDefine.quota | integer | N |  |
| GlsPrepareSyncSuType.topic | array[GlsTopicDefine] | Y | required |
| GlsTopicDefine.id | integer | Y | required |
| GlsTopicDefine.type | string | Y | required |
| GlsTopicDefine.code | string | Y | required |
| GlsTopicDefine.serviceId | string | N |  |
| GlsTopicDefine.serviceType | string | N |  |
| shardingElements | object | N |  |
| response | object | N |  |
| GlsSyncDataResponse.org | string | N |  |
| GlsSyncDataResponse.wks | string | N |  |
| GlsSyncDataResponse.env | string | N |  |
| GlsSyncDataResponse.confirm | string | N |  |
| GlsSyncDataResponse.data | array[GlsSyncDataCompareDefine] | N |  |
| GlsSyncDataCompareDefine.suType | string | N |  |
| GlsSyncDataCompareDefine.comparison | integer | N |  |
| GlsSyncDataCompareDefine.glsSu | array[GlsSyncSuCompareDefine] | N |  |
| GlsSyncSuCompareDefine.id | integer | N |  |
| GlsSyncSuCompareDefine.curSuId | string | N |  |
| GlsSyncSuCompareDefine.curWeight | integer | N |  |
| GlsSyncSuCompareDefine.curQuota | integer | N |  |
| GlsSyncSuCompareDefine.runSuId | string | N |  |
| GlsSyncSuCompareDefine.runWeight | integer | N |  |
| GlsSyncSuCompareDefine.runQuota | integer | N |  |
| GlsSyncSuCompareDefine.comparison | integer | N |  |
| GlsSyncDataCompareDefine.topic | array[GlsSyncTopicCompareDefine] | N |  |
| GlsSyncTopicCompareDefine.id | integer | N |  |
| GlsSyncTopicCompareDefine.curType | string | N |  |
| GlsSyncTopicCompareDefine.curCode | string | N |  |
| GlsSyncTopicCompareDefine.runType | string | N |  |
| GlsSyncTopicCompareDefine.runCode | string | N |  |
| GlsSyncTopicCompareDefine.comparison | integer | N |  |
| GlsSyncDataCompareDefine.primarySharding | array[GlsSyncPrimaryCompareDefine] | N |  |
| GlsSyncPrimaryCompareDefine.id | integer | N |  |
| GlsSyncPrimaryCompareDefine.curName | string | N |  |
| GlsSyncPrimaryCompareDefine.curValue | string | N |  |
| GlsSyncPrimaryCompareDefine.runName | string | N |  |
| GlsSyncPrimaryCompareDefine.runValue | string | N |  |
| GlsSyncPrimaryCompareDefine.comparison | integer | N |  |
| GlsSyncPrimaryCompareDefine.secondarySharding | array[GlsSyncSecondaryCompareDefine] | N |  |
| GlsSyncSecondaryCompareDefine.id | integer | N |  |
| GlsSyncSecondaryCompareDefine.curName | string | N |  |
| GlsSyncSecondaryCompareDefine.curType | string | N |  |
| GlsSyncSecondaryCompareDefine.runName | string | N |  |
| GlsSyncSecondaryCompareDefine.runType | string | N |  |
| GlsSyncSecondaryCompareDefine.comparison | integer | N |  |
| GlsSyncSecondaryCompareDefine.shardingElement | array[GlsSyncElementCompareDefine] | N |  |
| GlsSyncElementCompareDefine.id | integer | N |  |
| GlsSyncElementCompareDefine.curKey | string | N |  |
| GlsSyncElementCompareDefine.curValue | string | N |  |
| GlsSyncElementCompareDefine.runKey | string | N |  |
| GlsSyncElementCompareDefine.runValue | string | N |  |
| GlsSyncElementCompareDefine.comparison | integer | N |  |

**Range of values**

response example:

```
{
    "request": {
        "org": "org",
        "wks": "wks",
        "env": "env",
        "data": [
            {
                "suType": "suType",
                "primarySharding": [
                    {
                        "id": 0,
                        "name": "name",
                        "value": "value",
                        "secondarySharding": [
                            {
                                "id": 0,
                                "name": "name",
                                "type": "type",
                                "shardingElement": [
                                    {
                                        "id": 0,
                                        "key": "key",
                                        "value": "value"
                                    }
                                ]
                            }
                        ]
                    }
                ],
                "glsSu": [
                    {
                        "id": 0,
                        "suId": "suId",
                        "weight": 0,
                        "quota": 0
                    }
                ],
                "topic": [
                    {
                        "id": 0,
                        "type": "type",
                        "code": "code",
                        "serviceId": "serviceId",
                        "serviceType": "serviceType"
                    }
                ]
            }
        ]
    },
    "shardingElements": {
    },
    "response": {
        "org": "org",
        "wks": "wks",
        "env": "env",
        "confirm": "confirm",
        "data": [
            {
                "suType": "suType",
                "comparison": 0,
                "glsSu": [
                    {
                        "id": 0,
                        "curSuId": "curSuId",
                        "curWeight": 0,
                        "curQuota": 0,
                        "runSuId": "runSuId",
                        "runWeight": 0,
                        "runQuota": 0,
                        "comparison": 0
                    }
                ],
                "topic": [
                    {
                        "id": 0,
                        "curType": "curType",
                        "curCode": "curCode",
                        "runType": "runType",
                        "runCode": "runCode",
                        "comparison": 0
                    }
                ],
                "primarySharding": [
                    {
                        "id": 0,
                        "curName": "curName",
                        "curValue": "curValue",
                        "runName": "runName",
                        "runValue": "runValue",
                        "comparison": 0,
                        "secondarySharding": [
                            {
                                "id": 0,
                                "curName": "curName",
                                "curType": "curType",
                                "runName": "runName",
                                "runType": "runType",
                                "comparison": 0,
                                "shardingElement": [
                                    {
                                        "id": 0,
                                        "curKey": "curKey",
                                        "curValue": "curValue",
                                        "runKey": "runKey",
                                        "runValue": "runValue",
                                        "comparison": 0
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
```



# **GlsSync - list - API**

## URL: /environment/v1/GlsSync/list ##

## Method : Post ##

## Event ID : EnvGlsSyncList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |
| envId | integer |  | Y | required |
| suTypeId | integer |  | N |  |
| suTypeCode | string |  | N |  |
| suTypeName | string |  | N |  |
| suCode | string |  | N |  |
| primaryId | integer |  | N |  |
| primaryCode | string |  | N |  |
| primaryUuid | string |  | N |  |
| secondaryId | integer |  | N |  |
| secondaryCode | string |  | N |  |
| secondaryUuid | string |  | N |  |
| key | string |  | N |  |

**Range of values**

request example:
```
{
    "id": 0,
    "envId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "suTypeName": "suTypeName",
    "suCode": "suCode",
    "primaryId": 0,
    "primaryCode": "primaryCode",
    "primaryUuid": "primaryUuid",
    "secondaryId": 0,
    "secondaryCode": "secondaryCode",
    "secondaryUuid": "secondaryUuid",
    "key": "key"
}
```

**struct example:**

```
type GlsShardingDataListFilter struct {
    Id uint64 `json:"id"`
    EnvId uint64 `json:"envId" validate:"required"`
    SuTypeId uint64 `json:"suTypeId"`
    SuTypeCode string `json:"suTypeCode"`
    SuTypeName string `json:"suTypeName"`
    SuCode string `json:"suCode"`
    PrimaryId uint64 `json:"primaryId"`
    PrimaryCode string `json:"primaryCode"`
    PrimaryUuid string `json:"primaryUuid"`
    SecondaryId uint64 `json:"secondaryId"`
    SecondaryCode string `json:"secondaryCode"`
    SecondaryUuid string `json:"secondaryUuid"`
    Key string `json:"key"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **GlsSync - page - API**

## URL: /environment/v1/GlsSync/page ##

## Method : Post ##

## Event ID : EnvGlsSyncPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| envId | integer |  | Y | required |
| suTypeId | integer |  | N |  |
| suTypeCode | string |  | N |  |
| suTypeName | string |  | N |  |
| suCode | string |  | N |  |
| primaryId | integer |  | N |  |
| primaryCode | string |  | N |  |
| primaryUuid | string |  | N |  |
| secondaryId | integer |  | N |  |
| secondaryCode | string |  | N |  |
| secondaryUuid | string |  | N |  |
| key | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "envId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "suTypeName": "suTypeName",
    "suCode": "suCode",
    "primaryId": 0,
    "primaryCode": "primaryCode",
    "primaryUuid": "primaryUuid",
    "secondaryId": 0,
    "secondaryCode": "secondaryCode",
    "secondaryUuid": "secondaryUuid",
    "key": "key"
}
```

**struct example:**

```
type GlsShardingDataFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    EnvId uint64 `json:"envId" validate:"required"`
    SuTypeId uint64 `json:"suTypeId"`
    SuTypeCode string `json:"suTypeCode"`
    SuTypeName string `json:"suTypeName"`
    SuCode string `json:"suCode"`
    PrimaryId uint64 `json:"primaryId"`
    PrimaryCode string `json:"primaryCode"`
    PrimaryUuid string `json:"primaryUuid"`
    SecondaryId uint64 `json:"secondaryId"`
    SecondaryCode string `json:"secondaryCode"`
    SecondaryUuid string `json:"secondaryUuid"`
    Key string `json:"key"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **GlsSync - prepare - API**

## URL: /environment/v1/GlsSync/prepare ##

## Method : Post ##

## Event ID : EnvGlsSyncPrepare ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| glsId | integer |  | Y | required |
| incrementalFlag | boolean |  | N |  |
| suTypes | array[GlsSuType] |  | N |  |
| GlsSuType.suTypeId | integer |  | Y | required |
| GlsSuType.suTypeCode | string |  | Y | required |

**Range of values**

request example:
```
{
    "glsId": 0,
    "incrementalFlag": false,
    "suTypes": [
        {
            "suTypeId": 0,
            "suTypeCode": "suTypeCode"
        }
    ]
}
```

**struct example:**

```
type GlsSyncDataRequest struct {
    GlsId uint64 `json:"glsId" validate:"required"`
    IncrementalFlag bool `json:"incrementalFlag"`
    SuTypes []struct {
            SuTypeId uint64 `json:"suTypeId" validate:"required"`
            SuTypeCode string `json:"suTypeCode" validate:"required"`
        } `json:"suTypes"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| org | string | N |  |
| wks | string | N |  |
| env | string | N |  |
| confirm | string | N |  |
| data | array[GlsSyncDataCompareDefine] | N |  |
| GlsSyncDataCompareDefine.suType | string | N |  |
| GlsSyncDataCompareDefine.comparison | integer | N |  |
| GlsSyncDataCompareDefine.glsSu | array[GlsSyncSuCompareDefine] | N |  |
| GlsSyncSuCompareDefine.id | integer | N |  |
| GlsSyncSuCompareDefine.curSuId | string | N |  |
| GlsSyncSuCompareDefine.curWeight | integer | N |  |
| GlsSyncSuCompareDefine.curQuota | integer | N |  |
| GlsSyncSuCompareDefine.runSuId | string | N |  |
| GlsSyncSuCompareDefine.runWeight | integer | N |  |
| GlsSyncSuCompareDefine.runQuota | integer | N |  |
| GlsSyncSuCompareDefine.comparison | integer | N |  |
| GlsSyncDataCompareDefine.topic | array[GlsSyncTopicCompareDefine] | N |  |
| GlsSyncTopicCompareDefine.id | integer | N |  |
| GlsSyncTopicCompareDefine.curType | string | N |  |
| GlsSyncTopicCompareDefine.curCode | string | N |  |
| GlsSyncTopicCompareDefine.runType | string | N |  |
| GlsSyncTopicCompareDefine.runCode | string | N |  |
| GlsSyncTopicCompareDefine.comparison | integer | N |  |
| GlsSyncDataCompareDefine.primarySharding | array[GlsSyncPrimaryCompareDefine] | N |  |
| GlsSyncPrimaryCompareDefine.id | integer | N |  |
| GlsSyncPrimaryCompareDefine.curName | string | N |  |
| GlsSyncPrimaryCompareDefine.curValue | string | N |  |
| GlsSyncPrimaryCompareDefine.runName | string | N |  |
| GlsSyncPrimaryCompareDefine.runValue | string | N |  |
| GlsSyncPrimaryCompareDefine.comparison | integer | N |  |
| GlsSyncPrimaryCompareDefine.secondarySharding | array[GlsSyncSecondaryCompareDefine] | N |  |
| GlsSyncSecondaryCompareDefine.id | integer | N |  |
| GlsSyncSecondaryCompareDefine.curName | string | N |  |
| GlsSyncSecondaryCompareDefine.curType | string | N |  |
| GlsSyncSecondaryCompareDefine.runName | string | N |  |
| GlsSyncSecondaryCompareDefine.runType | string | N |  |
| GlsSyncSecondaryCompareDefine.comparison | integer | N |  |
| GlsSyncSecondaryCompareDefine.shardingElement | array[GlsSyncElementCompareDefine] | N |  |
| GlsSyncElementCompareDefine.id | integer | N |  |
| GlsSyncElementCompareDefine.curKey | string | N |  |
| GlsSyncElementCompareDefine.curValue | string | N |  |
| GlsSyncElementCompareDefine.runKey | string | N |  |
| GlsSyncElementCompareDefine.runValue | string | N |  |
| GlsSyncElementCompareDefine.comparison | integer | N |  |

**Range of values**

response example:

```
{
    "org": "org",
    "wks": "wks",
    "env": "env",
    "confirm": "confirm",
    "data": [
        {
            "suType": "suType",
            "comparison": 0,
            "glsSu": [
                {
                    "id": 0,
                    "curSuId": "curSuId",
                    "curWeight": 0,
                    "curQuota": 0,
                    "runSuId": "runSuId",
                    "runWeight": 0,
                    "runQuota": 0,
                    "comparison": 0
                }
            ],
            "topic": [
                {
                    "id": 0,
                    "curType": "curType",
                    "curCode": "curCode",
                    "runType": "runType",
                    "runCode": "runCode",
                    "comparison": 0
                }
            ],
            "primarySharding": [
                {
                    "id": 0,
                    "curName": "curName",
                    "curValue": "curValue",
                    "runName": "runName",
                    "runValue": "runValue",
                    "comparison": 0,
                    "secondarySharding": [
                        {
                            "id": 0,
                            "curName": "curName",
                            "curType": "curType",
                            "runName": "runName",
                            "runType": "runType",
                            "comparison": 0,
                            "shardingElement": [
                                {
                                    "id": 0,
                                    "curKey": "curKey",
                                    "curValue": "curValue",
                                    "runKey": "runKey",
                                    "runValue": "runValue",
                                    "comparison": 0
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
}
```



# **GlsSync - sutype list - API**

## URL: /environment/v1/GlsSync/sutype/list ##

## Method : Post ##

## Event ID : EnvGlsSyncSuTypeList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type Request struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **GlsSync - update - API**

## URL: /environment/v1/GlsSync/update ##

## Method : Post ##

## Event ID : EnvGlsSyncUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| shardingElement | array[ShardingElementDefine] |  | Y | required,max=255 |
| ShardingElementDefine.id | integer |  | Y | required |
| ShardingElementDefine.key | string |  | Y | required |
| ShardingElementDefine.value | string |  | Y | required |

**Range of values**

request example:
```
{
    "shardingElement": [
        {
            "id": 0,
            "key": "key",
            "value": "value"
        }
    ]
}
```

**struct example:**

```
type UpdateGlsShardingDataRequest struct {
    ShardingElement []struct {
            Id uint64 `json:"id" validate:"required"`
            Key string `json:"key" validate:"required"`
            Value string `json:"value" validate:"required"`
        } `json:"shardingElement" validate:"required,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


