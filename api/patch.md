
# **patch - create - API**

## URL: /environment/v1/patch/create ##

## Method : Post ##

## Event ID : EnvPatchCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| PatchInfo | object |  | N |  |
| PatchInfo.planId | integer |  | N | gte=0 |
| PatchInfo.patchType | integer |  | Y | required,min=1,max=2 |
| PatchInfo.level | string |  | N | name,max=64 |
| PatchInfo.status | string |  | N | name,max=64 |
| PatchInfo.note | string |  | N | omitempty,max=255 |
| PatchInfo.version | string |  | N | omitempty,max=64 |
| PatchInfo.data | map[string]interface |  | Y | gt=0,dive,required |
| requestId | string |  | N | name,max=64 |

**Range of values**

request example:
```
{
    "PatchInfo": {
        "planId": 0,
        "patchType": 0,
        "level": "level",
        "status": "status",
        "note": "note",
        "version": "version",
        "data": {
        }
    },
    "requestId": "requestId"
}
```

**struct example:**

```
type CreatePatchRequest struct {
    PatchInfo struct {
        PlanId uint64 `json:"planId" validate:"gte=0"`
        PatchType int `json:"patchType" validate:"required,min=1,max=2"`
        Level string `json:"level" validate:"name,max=64"`
        Status string `json:"status" validate:"name,max=64"`
        Note string `json:"note" validate:"omitempty,max=255"`
        Version string `json:"version" validate:"omitempty,max=64"`
        Data map[string]interface{} `json:"data" validate:"gt=0,dive,required"`
    }
    RequestId string `json:"requestId" validate:"name,max=64"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **patch - detail - API**

## URL: /environment/v1/patch/detail ##

## Method : Post ##

## Event ID : EnvPatchDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type patchIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| planId | integer | N | gte=0 |
| patchType | integer | Y | required,min=1,max=2 |
| level | string | N | name,max=64 |
| status | string | N | name,max=64 |
| note | string | N | omitempty,max=255 |
| version | string | N | omitempty,max=64 |
| data | map[string]interface | Y | gt=0,dive,required |

**Range of values**

response example:

```
{
    "planId": 0,
    "patchType": 0,
    "level": "level",
    "status": "status",
    "note": "note",
    "version": "version",
    "data": {
    }
}
```



# **patch - list - API**

## URL: /environment/v1/patch/list ##

## Method : Post ##

## Event ID : EnvPatchList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| lastPatchId | integer |  | N |  |
| patchIdList | array[uint64] |  | N |  |
| patchType | integer |  | N |  |
| instanceId | integer |  | N |  |
| planId | integer |  | N |  |
| serviceCode | string |  | N |  |
| serviceType | string |  | N |  |
| suInstanceId | integer |  | N |  |
| suId | integer |  | N |  |
| environmentId | integer |  | N |  |
| stackId | integer |  | N |  |
| sectionId | integer |  | N |  |
| serviceScope | integer |  | N |  |
| locationScope | integer |  | N |  |
| tenantId | string |  | N |  |
| domain | string |  | N |  |
| status | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "lastPatchId": 0,
    "patchIdList": [
    ],
    "patchType": 0,
    "instanceId": 0,
    "planId": 0,
    "serviceCode": "serviceCode",
    "serviceType": "serviceType",
    "suInstanceId": 0,
    "suId": 0,
    "environmentId": 0,
    "stackId": 0,
    "sectionId": 0,
    "serviceScope": 0,
    "locationScope": 0,
    "tenantId": "tenantId",
    "domain": "domain",
    "status": "status"
}
```

**struct example:**

```
type PatchFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    LastPatchId uint64 `json:"lastPatchId"`
    PatchIdList []uint64 `json:"patchIdList"`
    PatchType int `json:"patchType"`
    InstanceId uint64 `json:"instanceId"`
    PlanId uint64 `json:"planId"`
    ServiceCode string `json:"serviceCode"`
    ServiceType string `json:"serviceType"`
    SuInstanceId uint64 `json:"suInstanceId"`
    SuId uint64 `json:"suId"`
    EnvironmentId uint64 `json:"environmentId"`
    StackId uint64 `json:"stackId"`
    SectionId uint64 `json:"sectionId"`
    ServiceScope int `json:"serviceScope"`
    LocationScope int `json:"locationScope"`
    TenantId string `json:"tenantId"`
    Domain string `json:"domain"`
    Status string `json:"status"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| planId | integer | N | gte=0 |
| patchType | integer | Y | required,min=1,max=2 |
| level | string | N | name,max=64 |
| status | string | N | name,max=64 |
| note | string | N | omitempty,max=255 |
| version | string | N | omitempty,max=64 |
| data | map[string]interface | Y | gt=0,dive,required |

**Range of values**

response example:

```
{
    "planId": 0,
    "patchType": 0,
    "level": "level",
    "status": "status",
    "note": "note",
    "version": "version",
    "data": {
    }
}
```



# **patch - page - API**

## URL: /environment/v1/patch/page ##

## Method : Post ##

## Event ID : EnvPatchPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| lastPatchId | integer |  | N |  |
| patchIdList | array[uint64] |  | N |  |
| patchType | integer |  | N |  |
| instanceId | integer |  | N |  |
| planId | integer |  | N |  |
| serviceCode | string |  | N |  |
| serviceType | string |  | N |  |
| suInstanceId | integer |  | N |  |
| suId | integer |  | N |  |
| environmentId | integer |  | N |  |
| stackId | integer |  | N |  |
| sectionId | integer |  | N |  |
| serviceScope | integer |  | N |  |
| locationScope | integer |  | N |  |
| tenantId | string |  | N |  |
| domain | string |  | N |  |
| status | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "lastPatchId": 0,
    "patchIdList": [
    ],
    "patchType": 0,
    "instanceId": 0,
    "planId": 0,
    "serviceCode": "serviceCode",
    "serviceType": "serviceType",
    "suInstanceId": 0,
    "suId": 0,
    "environmentId": 0,
    "stackId": 0,
    "sectionId": 0,
    "serviceScope": 0,
    "locationScope": 0,
    "tenantId": "tenantId",
    "domain": "domain",
    "status": "status"
}
```

**struct example:**

```
type PatchFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    LastPatchId uint64 `json:"lastPatchId"`
    PatchIdList []uint64 `json:"patchIdList"`
    PatchType int `json:"patchType"`
    InstanceId uint64 `json:"instanceId"`
    PlanId uint64 `json:"planId"`
    ServiceCode string `json:"serviceCode"`
    ServiceType string `json:"serviceType"`
    SuInstanceId uint64 `json:"suInstanceId"`
    SuId uint64 `json:"suId"`
    EnvironmentId uint64 `json:"environmentId"`
    StackId uint64 `json:"stackId"`
    SectionId uint64 `json:"sectionId"`
    ServiceScope int `json:"serviceScope"`
    LocationScope int `json:"locationScope"`
    TenantId string `json:"tenantId"`
    Domain string `json:"domain"`
    Status string `json:"status"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **patch - revises - API**

## URL: /environment/v1/patch/revises ##

## Method : Post ##

## Event ID : EnvPatchRevises ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type patchIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| planId | integer | N | gte=0 |
| patchType | integer | Y | required,min=1,max=2 |
| level | string | N | name,max=64 |
| status | string | N | name,max=64 |
| note | string | N | omitempty,max=255 |
| version | string | N | omitempty,max=64 |
| data | map[string]interface | Y | gt=0,dive,required |

**Range of values**

response example:

```
{
    "planId": 0,
    "patchType": 0,
    "level": "level",
    "status": "status",
    "note": "note",
    "version": "version",
    "data": {
    }
}
```



# **patch - update - API**

## URL: /environment/v1/patch/update ##

## Method : Post ##

## Event ID : EnvPatchUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| patches | array[Patch] |  | Y | gt=0,dive,required |
| Patch.patchId | integer |  | Y | required,gt=0 |
| Patch.patchType | integer |  | N | omitempty,min=1,max=2 |
| Patch.name | string |  | N | omitempty,name,max=64 |
| Patch.status | string |  | N | omitempty,name,max=64 |
| note | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "patches": [
        {
            "patchId": 0,
            "patchType": 0,
            "name": "name",
            "status": "status"
        }
    ],
    "note": "note"
}
```

**struct example:**

```
type UpdatePatchRequest struct {
    Patches []struct {
            PatchId uint64 `json:"patchId" validate:"required,gt=0"`
            PatchType int `json:"patchType" validate:"omitempty,min=1,max=2"`
            Name string `json:"name" validate:"omitempty,name,max=64"`
            Status string `json:"status" validate:"omitempty,name,max=64"`
        } `json:"patches" validate:"gt=0,dive,required"`
    Note string `json:"note" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


