
# **nsh - deploy - API**

## URL: /environment/v1/nsh/deploy ##

## Method : Post ##

## Event ID : EnvNshDeploy ##

**Description:**

create nsh cluster, create plan and deploy task

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| clusterName | string |  | Y | required |
| description | string |  | N |  |
| batchId | integer |  | N | omitempty,gte=0 |
| stackId | integer |  | N | omitempty,gte=0 |
| packageVersion | string |  | N |  |
| imageVersion | string |  | N |  |
| suInstanceId | integer |  | Y | gt=0,required |
| k8sId | integer |  | N |  |
| rdbId | integer |  | N |  |
| kvcId | integer |  | N |  |
| dosId | integer |  | N |  |
| vpnId | integer |  | N |  |
| meshId | integer |  | N |  |
| AdvanceDeployParameter | object |  | N |  |
| AdvanceDeployParameter.podReplicas | integer |  | N |  |
| AdvanceDeployParameter.logLevel | string |  | N |  |
| AdvanceDeployParameter.appLimitsCpu | string |  | N |  |
| AdvanceDeployParameter.appRequestsCpu | string |  | N |  |
| AdvanceDeployParameter.appLimitsMemory | string |  | N |  |
| AdvanceDeployParameter.appRequestsMemory | string |  | N |  |
| parameters | map[string]interface |  | Y | gte=0,dive,required |

**Range of values**

request example:
```
{
    "clusterName": "clusterName",
    "description": "description",
    "batchId": 0,
    "stackId": 0,
    "packageVersion": "packageVersion",
    "imageVersion": "imageVersion",
    "suInstanceId": 0,
    "k8sId": 0,
    "rdbId": 0,
    "kvcId": 0,
    "dosId": 0,
    "vpnId": 0,
    "meshId": 0,
    "AdvanceDeployParameter": {
        "podReplicas": 0,
        "logLevel": "logLevel",
        "appLimitsCpu": "appLimitsCpu",
        "appRequestsCpu": "appRequestsCpu",
        "appLimitsMemory": "appLimitsMemory",
        "appRequestsMemory": "appRequestsMemory"
    },
    "parameters": {
    }
}
```

**struct example:**

```
type NshDeployRequest struct {
    ClusterName string `json:"clusterName" validate:"required"`
    Description string `json:"description"`
    BatchId uint64 `json:"batchId" validate:"omitempty,gte=0"`
    StackId uint64 `json:"stackId" validate:"omitempty,gte=0"`
    PackageVersion string `json:"packageVersion"`
    ImageVersion string `json:"imageVersion"`
    SuInstanceId uint64 `json:"suInstanceId" validate:"gt=0,required"`
    K8sId uint64 `json:"k8sId"`
    RdbId uint64 `json:"rdbId"`
    KvcId uint64 `json:"kvcId"`
    DosId uint64 `json:"dosId"`
    VpnId uint64 `json:"vpnId"`
    MeshId uint64 `json:"meshId"`
    AdvanceDeployParameter struct {
        PodReplicas uint64 `json:"podReplicas"`
        LogLevel string `json:"logLevel"`
        AppLimitsCpu string `json:"appLimitsCpu"`
        AppRequestsCpu string `json:"appRequestsCpu"`
        AppLimitsMemory string `json:"appLimitsMemory"`
        AppRequestsMemory string `json:"appRequestsMemory"`
    }
    Parameters map[string]interface{} `json:"parameters" validate:"gte=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| sopId | integer | N |  |
| jobId | integer | N |  |
| taskId | integer | N |  |

**Range of values**

response example:

```
{
    "sopId": 0,
    "jobId": 0,
    "taskId": 0
}
```



# **nsh - detail - API**

## URL: /environment/v1/nsh/detail ##

## Method : Post ##

## Event ID : EnvNshDetail ##

**Description:**

query nsh cluster detail with nsh services

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| clusterId | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "clusterId": 0
}
```

**struct example:**

```
type NshClusterDetailRequest struct {
    ClusterId uint64 `json:"clusterId" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| code | string | N |  |
| name | string | N |  |
| description | string | N |  |
| imageVersion | string | N |  |
| environment | object | N |  |
| Environment.id | integer | N |  |
| Environment.workspaceId | string | N |  |
| Environment.resourceGroupId | integer | N |  |
| Environment.resourceGroupName | string | N |  |
| Environment.name | string | N |  |
| Environment.code | string | N |  |
| Environment.phase | string | N |  |
| Environment.preset | string | N |  |
| Environment.serverless | string | N |  |
| Environment.wsGitPath | string | N |  |
| Environment.repoId | integer | N |  |
| Environment.repodbId | integer | N |  |
| Environment.templateGroupId | integer | N |  |
| Environment.configProjID | integer | N |  |
| Environment.upmDataId | integer | N |  |
| Environment.description | string | N |  |
| Environment.status | string | N |  |
| Environment.tenantId | string | N |  |
| Environment.updaterId | string | N |  |
| Environment.updater | string | N |  |
| Environment.creatorId | string | N |  |
| Environment.creator | string | N |  |
| Environment.createAt | string | N |  |
| Environment.updatedAt | string | N |  |
| Environment.deletedAt | string | N |  |
| suInstanceInfo | object | N |  |
| SuInstanceInfo.workspaceId | string | N |  |
| SuInstanceInfo.wsGitPath | string | N |  |
| SuInstanceInfo.resourceGroupId | integer | N |  |
| SuInstanceInfo.resourceGroupName | string | N |  |
| SuInstanceInfo.repoId | integer | N |  |
| SuInstanceInfo.repodbId | integer | N |  |
| SuInstanceInfo.environmentName | string | N |  |
| SuInstanceInfo.environmentCode | string | N |  |
| SuInstanceInfo.phase | string | N |  |
| SuInstanceInfo.stackId | integer | N |  |
| SuInstanceInfo.envId | integer | N |  |
| SuInstanceInfo.networkZoneID | integer | N |  |
| SuInstanceInfo.suTypeId | integer | N |  |
| SuInstanceInfo.suTypeCode | string | N |  |
| SuInstanceInfo.suName | string | N |  |
| SuInstanceInfo.suCode | string | N |  |
| SuInstanceInfo.suWeight | uint | N |  |
| SuInstanceInfo.suQuota | integer | N |  |
| SuInstanceInfo.objRelations | map[string]uint64 | N |  |
| SuInstanceInfo.networkZone | object | N |  |
| NetworkZoneInfo.id | integer | N |  |
| NetworkZoneInfo.taskId | integer | N |  |
| NetworkZoneInfo.specId | integer | N |  |
| NetworkZoneInfo.name | string | N |  |
| NetworkZoneInfo.code | string | N |  |
| NetworkZoneInfo.tenantRegionId | integer | N |  |
| NetworkZoneInfo.tenantVpcId | integer | N |  |
| NetworkZoneInfo.resourceGroupId | integer | N |  |
| NetworkZoneInfo.azId | integer | N |  |
| NetworkZoneInfo.serialNumber | integer | N |  |
| NetworkZoneInfo.tenantId | string | N |  |
| NetworkZoneInfo.tenantCode | string | N |  |
| NetworkZoneInfo.status | string | N |  |
| NetworkZoneInfo.description | string | N |  |
| SuInstanceInfo.SuInstance | object | N |  |
| SuInstance.id | integer | N |  |
| SuInstance.envId | integer | N |  |
| SuInstance.suId | integer | N |  |
| SuInstance.name | string | N |  |
| SuInstance.code | string | N |  |
| SuInstance.description | string | N |  |
| SuInstance.tenantId | string | N |  |
| SuInstance.creatorId | string | N |  |
| SuInstance.creator | string | N |  |
| SuInstance.createAt | string | N |  |
| SuInstance.updatedAt | string | N |  |
| SuInstance.deletedAt | string | N |  |
| workspace | object | N |  |
| WorkspaceSpec.orgWorkspaceId | string | N |  |
| WorkspaceSpec.organizationId | string | N |  |
| WorkspaceSpec.projectKey | string | N |  |
| WorkspaceSpec.projectName | string | N |  |
| WorkspaceSpec.cicdToolId | string | N |  |
| WorkspaceSpec.workspaceName | string | N |  |
| WorkspaceSpec.description | string | N |  |
| WorkspaceSpec.workspaceGitPath | string | N |  |
| WorkspaceSpec.accountabilityId | string | N |  |
| services | array[NshService] | N |  |
| NshService.id | integer | N |  |
| NshService.envId | integer | N |  |
| NshService.tenantId | string | N |  |
| NshService.clusterId | integer | N |  |
| NshService.applicationId | integer | N |  |
| NshService.instanceRefID | integer | N |  |
| NshService.name | string | N |  |
| NshService.description | string | N |  |
| NshService.status | string | N |  |
| NshService.runningStatus | string | N |  |
| NshService.type | string | N |  |
| NshService.instanceNum | string | N |  |
| NshService.image | string | N |  |
| NshService.version | string | N |  |
| NshService.logLevel | string | N |  |
| NshService.cpuRequest | string | N |  |
| NshService.cpuLimit | string | N |  |
| NshService.memoryRequest | string | N |  |
| NshService.memoryLimit | string | N |  |
| NshService.nodeIP | string | N |  |
| NshService.createdAt | string | N |  |
| NshService.creatorId | string | N |  |
| NshService.creator | string | N |  |
| NshService.updatedAt | string | N |  |
| NshService.updaterId | string | N |  |
| NshService.updater | string | N |  |
| NshService.deletedAt | string | N |  |
| resource | object | N |  |
| SuInstanceResource.networkZone | object | N |  |
| SuInstanceResource.k8sCluster | object | N |  |
| ResourceK8sCluster.id | integer | N |  |
| ResourceK8sCluster.agentId | integer | N |  |
| ResourceK8sCluster.clusterCidr | string | N |  |
| ResourceK8sCluster.clusterCode | string | N |  |
| ResourceK8sCluster.clusterName | string | N |  |
| ResourceK8sCluster.harbor | string | N |  |
| ResourceK8sCluster.harborId | integer | N |  |
| ResourceK8sCluster.k8sConfigId | integer | N |  |
| ResourceK8sCluster.network | string | N |  |
| ResourceK8sCluster.networkZone | string | N |  |
| ResourceK8sCluster.nodePortRange | string | N |  |
| ResourceK8sCluster.pemId | integer | N |  |
| ResourceK8sCluster.regionId | integer | N |  |
| ResourceK8sCluster.resourceGroup | string | N |  |
| ResourceK8sCluster.securityGroup | string | N |  |
| ResourceK8sCluster.sepcId | integer | N |  |
| ResourceK8sCluster.serviceCidr | string | N |  |
| ResourceK8sCluster.status | string | N |  |
| ResourceK8sCluster.suInstance | string | N |  |
| ResourceK8sCluster.taskId | integer | N |  |
| ResourceK8sCluster.tenantCode | string | N |  |
| ResourceK8sCluster.tenantId | string | N |  |
| ResourceK8sCluster.tenantRegion | string | N |  |
| ResourceK8sCluster.type | string | N |  |
| ResourceK8sCluster.K8SNode | array[ResourceK8SNode] | N |  |
| ResourceK8SNode.id | integer | N |  |
| ResourceK8SNode.templateId | integer | N |  |
| ResourceK8SNode.templateName | string | N |  |
| ResourceK8SNode.dataVolumeSize | integer | N |  |
| ResourceK8SNode.regionName | string | N |  |
| ResourceK8SNode.regionCode | string | N |  |
| ResourceK8SNode.tenantAzName | string | N |  |
| ResourceK8SNode.azName | string | N |  |
| ResourceK8SNode.azCode | string | N |  |
| ResourceK8SNode.memory | integer | N |  |
| ResourceK8SNode.cpu | integer | N |  |
| ResourceK8SNode.rootVolumeSize | integer | N |  |
| ResourceK8SNode.disk | integer | N |  |
| ResourceK8SNode.taskId | integer | N |  |
| ResourceK8SNode.sepcId | integer | N |  |
| ResourceK8SNode.orderNumber | integer | N |  |
| ResourceK8SNode.clusterId | integer | N |  |
| ResourceK8SNode.clusterCode | string | N |  |
| ResourceK8SNode.region | string | N |  |
| ResourceK8SNode.resourceGroup | string | N |  |
| ResourceK8SNode.networkZone | string | N |  |
| ResourceK8SNode.availabilityZone | string | N |  |
| ResourceK8SNode.ip | string | N |  |
| ResourceK8SNode.status | string | N |  |
| ResourceK8SNode.role | string | N |  |
| ResourceK8SNode.drain | string | N |  |
| ResourceK8SNode.schedule | string | N |  |
| ResourceK8SNode.vmId | integer | N |  |
| ResourceK8SNode.tenantId | string | N |  |
| SuInstanceResource.cacheCluster | object | N |  |
| ResourceCacheCluster.id | integer | N |  |
| ResourceCacheCluster.clusterCode | string | N |  |
| ResourceCacheCluster.clusterName | string | N |  |
| ResourceCacheCluster.harborId | integer | N |  |
| ResourceCacheCluster.networkZone | string | N |  |
| ResourceCacheCluster.occupied | string | N |  |
| ResourceCacheCluster.password | string | N |  |
| ResourceCacheCluster.pemId | integer | N |  |
| ResourceCacheCluster.property | string | N |  |
| ResourceCacheCluster.regionId | integer | N |  |
| ResourceCacheCluster.resourceGroup | string | N |  |
| ResourceCacheCluster.sepcId | integer | N |  |
| ResourceCacheCluster.status | string | N |  |
| ResourceCacheCluster.taskId | integer | N |  |
| ResourceCacheCluster.tenantCode | string | N |  |
| ResourceCacheCluster.tenantId | string | N |  |
| ResourceCacheCluster.tenantRegion | string | N |  |
| ResourceCacheCluster.type | string | N |  |
| ResourceCacheCluster.CacheNode | array[ResourceCacheNode] | N |  |
| ResourceCacheNode.memory | integer | N |  |
| ResourceCacheNode.cpu | integer | N |  |
| ResourceCacheNode.templateId | integer | N |  |
| ResourceCacheNode.templateName | string | N |  |
| ResourceCacheNode.dataVolumeSize | integer | N |  |
| ResourceCacheNode.regionName | string | N |  |
| ResourceCacheNode.regionCode | string | N |  |
| ResourceCacheNode.tenantAzCode | string | N |  |
| ResourceCacheNode.tenantAzName | string | N |  |
| ResourceCacheNode.azName | string | N |  |
| ResourceCacheNode.azCode | string | N |  |
| ResourceCacheNode.id | integer | N |  |
| ResourceCacheNode.taskId | integer | N |  |
| ResourceCacheNode.specId | integer | N |  |
| ResourceCacheNode.clusterId | integer | N |  |
| ResourceCacheNode.ip | string | N |  |
| ResourceCacheNode.status | string | N |  |
| ResourceCacheNode.port | string | N |  |
| ResourceCacheNode.role | string | N |  |
| ResourceCacheNode.master_ip | string | N |  |
| ResourceCacheNode.master_port | string | N |  |
| ResourceCacheNode.vmId | integer | N |  |
| ResourceCacheNode.resourceGroup | string | N |  |
| ResourceCacheNode.networkZone | string | N |  |
| ResourceCacheNode.tenantId | string | N |  |
| SuInstanceResource.cacheClusterList | array[ResourceCacheCluster] | N |  |
| SuInstanceResource.pemVpn | object | N |  |
| VpnInfo.mesh | object | N |  |
| MeshSpec.agentId | integer | N |  |
| MeshSpec.code | string | N |  |
| MeshSpec.description | string | N |  |
| MeshSpec.harborId | integer | N |  |
| MeshSpec.id | integer | N |  |
| MeshSpec.maxConnection | integer | N |  |
| MeshSpec.name | string | N |  |
| MeshSpec.networkZone | string | N |  |
| MeshSpec.occupied | string | N |  |
| MeshSpec.password | string | N |  |
| MeshSpec.property | string | N |  |
| MeshSpec.resourceGroup | string | N |  |
| MeshSpec.role | string | N |  |
| MeshSpec.siteId | integer | N |  |
| MeshSpec.specId | integer | N |  |
| MeshSpec.status | string | N |  |
| MeshSpec.taskId | integer | N |  |
| MeshSpec.tenantId | string | N |  |
| MeshSpec.type | string | N |  |
| MeshSpec.username | string | N |  |
| MeshSpec.vmTemplateId | integer | N |  |
| MeshSpec.meshNode | array[MeshNodeInfo] | N |  |
| MeshNodeInfo.templateId | integer | N |  |
| MeshNodeInfo.templateName | string | N |  |
| MeshNodeInfo.dataVolumeSize | integer | N |  |
| MeshNodeInfo.regionName | string | N |  |
| MeshNodeInfo.regionCode | string | N |  |
| MeshNodeInfo.tenantAzName | string | N |  |
| MeshNodeInfo.tenantAzCode | string | N |  |
| MeshNodeInfo.azName | string | N |  |
| MeshNodeInfo.azCode | string | N |  |
| MeshNodeInfo.id | integer | N |  |
| MeshNodeInfo.taskId | integer | N |  |
| MeshNodeInfo.specId | integer | N |  |
| MeshNodeInfo.meshId | integer | N |  |
| MeshNodeInfo.meshCode | string | N |  |
| MeshNodeInfo.ip | string | N |  |
| MeshNodeInfo.role | string | N |  |
| MeshNodeInfo.adminPort | integer | N |  |
| MeshNodeInfo.workerPort | integer | N |  |
| MeshNodeInfo.tenantId | string | N |  |
| MeshNodeInfo.resourceGroup | string | N |  |
| MeshNodeInfo.vmId | integer | N |  |
| MeshNodeInfo.status | string | N |  |
| MeshNodeInfo.routerName | string | N |  |
| MeshNodeInfo.description | string | N |  |
| VpnInfo.vpn | object | N |  |
| SuInstanceResource.rdbGroup | object | N |  |
| RDBGroupInfo.rdbCluster | object | N |  |
| ResourceRdbCluster.id | integer | N |  |
| ResourceRdbCluster.clusterId | string | N |  |
| ResourceRdbCluster.clusterName | string | N |  |
| ResourceRdbCluster.clusterType | string | N |  |
| ResourceRdbCluster.networkZone | string | N |  |
| ResourceRdbCluster.occupied | string | N |  |
| ResourceRdbCluster.property | string | N |  |
| ResourceRdbCluster.region | string | N |  |
| ResourceRdbCluster.resourceGroup | string | N |  |
| ResourceRdbCluster.specId | integer | N |  |
| ResourceRdbCluster.status | string | N |  |
| ResourceRdbCluster.taskId | integer | N |  |
| ResourceRdbCluster.tenantId | string | N |  |
| ResourceRdbCluster.updater | string | N |  |
| ResourceRdbCluster.updaterId | string | N |  |
| ResourceRdbCluster.version | string | N |  |
| ResourceRdbCluster.vmTemplateId | integer | N |  |
| ResourceRdbCluster.RDBGroup | array[ResourceRdbGroup] | N |  |
| ResourceRdbGroup.id | integer | N |  |
| ResourceRdbGroup.taskId | integer | N |  |
| ResourceRdbGroup.sepcId | integer | N |  |
| ResourceRdbGroup.region | string | N |  |
| ResourceRdbGroup.resourceGroup | string | N |  |
| ResourceRdbGroup.networkZone | string | N |  |
| ResourceRdbGroup.rdbGroupCode | string | N |  |
| ResourceRdbGroup.clusterId | integer | N |  |
| ResourceRdbGroup.clusterCode | string | N |  |
| ResourceRdbGroup.harborId | integer | N |  |
| ResourceRdbGroup.pemId | integer | N |  |
| ResourceRdbGroup.vip | string | N |  |
| ResourceRdbGroup.az | string | N |  |
| ResourceRdbGroup.vport | integer | N |  |
| ResourceRdbGroup.type | string | N |  |
| ResourceRdbGroup.purpose | string | N |  |
| ResourceRdbGroup.name | string | N |  |
| ResourceRdbGroup.description | string | N |  |
| RDBGroupInfo.rdbGroup | object | N |  |
| SuInstanceResource.aemVpnList | array[VpnInfo] | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "code": "code",
    "name": "name",
    "description": "description",
    "imageVersion": "imageVersion",
    "environment": {
        "id": 0,
        "workspaceId": "workspaceId",
        "resourceGroupId": 0,
        "resourceGroupName": "resourceGroupName",
        "name": "name",
        "code": "code",
        "phase": "phase",
        "preset": "preset",
        "serverless": "serverless",
        "wsGitPath": "wsGitPath",
        "repoId": 0,
        "repodbId": 0,
        "templateGroupId": 0,
        "configProjID": 0,
        "upmDataId": 0,
        "description": "description",
        "status": "status",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    },
    "suInstanceInfo": {
        "workspaceId": "workspaceId",
        "wsGitPath": "wsGitPath",
        "resourceGroupId": 0,
        "resourceGroupName": "resourceGroupName",
        "repoId": 0,
        "repodbId": 0,
        "environmentName": "environmentName",
        "environmentCode": "environmentCode",
        "phase": "phase",
        "stackId": 0,
        "envId": 0,
        "networkZoneID": 0,
        "suTypeId": 0,
        "suTypeCode": "suTypeCode",
        "suName": "suName",
        "suCode": "suCode",
        "suWeight": "suWeight",
        "suQuota": 0,
        "objRelations": {
        },
        "networkZone": {
            "id": 0,
            "taskId": 0,
            "specId": 0,
            "name": "name",
            "code": "code",
            "tenantRegionId": 0,
            "tenantVpcId": 0,
            "resourceGroupId": 0,
            "azId": 0,
            "serialNumber": 0,
            "tenantId": "tenantId",
            "tenantCode": "tenantCode",
            "status": "status",
            "description": "description"
        },
        "SuInstance": {
            "id": 0,
            "envId": 0,
            "suId": 0,
            "name": "name",
            "code": "code",
            "description": "description",
            "tenantId": "tenantId",
            "creatorId": "creatorId",
            "creator": "creator",
            "createAt": "createAt",
            "updatedAt": "updatedAt",
            "deletedAt": "deletedAt"
        }
    },
    "workspace": {
        "orgWorkspaceId": "orgWorkspaceId",
        "organizationId": "organizationId",
        "projectKey": "projectKey",
        "projectName": "projectName",
        "cicdToolId": "cicdToolId",
        "workspaceName": "workspaceName",
        "description": "description",
        "workspaceGitPath": "workspaceGitPath",
        "accountabilityId": "accountabilityId"
    },
    "services": [
        {
            "id": 0,
            "envId": 0,
            "tenantId": "tenantId",
            "clusterId": 0,
            "applicationId": 0,
            "instanceRefID": 0,
            "name": "name",
            "description": "description",
            "status": "status",
            "runningStatus": "runningStatus",
            "type": "type",
            "instanceNum": "instanceNum",
            "image": "image",
            "version": "version",
            "logLevel": "logLevel",
            "cpuRequest": "cpuRequest",
            "cpuLimit": "cpuLimit",
            "memoryRequest": "memoryRequest",
            "memoryLimit": "memoryLimit",
            "nodeIP": "nodeIP",
            "createdAt": "createdAt",
            "creatorId": "creatorId",
            "creator": "creator",
            "updatedAt": "updatedAt",
            "updaterId": "updaterId",
            "updater": "updater",
            "deletedAt": "deletedAt"
        }
    ],
    "resource": {
        "networkZone": {
        },
        "k8sCluster": {
            "id": 0,
            "agentId": 0,
            "clusterCidr": "clusterCidr",
            "clusterCode": "clusterCode",
            "clusterName": "clusterName",
            "harbor": "harbor",
            "harborId": 0,
            "k8sConfigId": 0,
            "network": "network",
            "networkZone": "networkZone",
            "nodePortRange": "nodePortRange",
            "pemId": 0,
            "regionId": 0,
            "resourceGroup": "resourceGroup",
            "securityGroup": "securityGroup",
            "sepcId": 0,
            "serviceCidr": "serviceCidr",
            "status": "status",
            "suInstance": "suInstance",
            "taskId": 0,
            "tenantCode": "tenantCode",
            "tenantId": "tenantId",
            "tenantRegion": "tenantRegion",
            "type": "type",
            "K8SNode": [
                {
                    "id": 0,
                    "templateId": 0,
                    "templateName": "templateName",
                    "dataVolumeSize": 0,
                    "regionName": "regionName",
                    "regionCode": "regionCode",
                    "tenantAzName": "tenantAzName",
                    "azName": "azName",
                    "azCode": "azCode",
                    "memory": 0,
                    "cpu": 0,
                    "rootVolumeSize": 0,
                    "disk": 0,
                    "taskId": 0,
                    "sepcId": 0,
                    "orderNumber": 0,
                    "clusterId": 0,
                    "clusterCode": "clusterCode",
                    "region": "region",
                    "resourceGroup": "resourceGroup",
                    "networkZone": "networkZone",
                    "availabilityZone": "availabilityZone",
                    "ip": "ip",
                    "status": "status",
                    "role": "role",
                    "drain": "drain",
                    "schedule": "schedule",
                    "vmId": 0,
                    "tenantId": "tenantId"
                }
            ]
        },
        "cacheCluster": {
            "id": 0,
            "clusterCode": "clusterCode",
            "clusterName": "clusterName",
            "harborId": 0,
            "networkZone": "networkZone",
            "occupied": "occupied",
            "password": "password",
            "pemId": 0,
            "property": "property",
            "regionId": 0,
            "resourceGroup": "resourceGroup",
            "sepcId": 0,
            "status": "status",
            "taskId": 0,
            "tenantCode": "tenantCode",
            "tenantId": "tenantId",
            "tenantRegion": "tenantRegion",
            "type": "type",
            "CacheNode": [
                {
                    "memory": 0,
                    "cpu": 0,
                    "templateId": 0,
                    "templateName": "templateName",
                    "dataVolumeSize": 0,
                    "regionName": "regionName",
                    "regionCode": "regionCode",
                    "tenantAzCode": "tenantAzCode",
                    "tenantAzName": "tenantAzName",
                    "azName": "azName",
                    "azCode": "azCode",
                    "id": 0,
                    "taskId": 0,
                    "specId": 0,
                    "clusterId": 0,
                    "ip": "ip",
                    "status": "status",
                    "port": "port",
                    "role": "role",
                    "master_ip": "master_ip",
                    "master_port": "master_port",
                    "vmId": 0,
                    "resourceGroup": "resourceGroup",
                    "networkZone": "networkZone",
                    "tenantId": "tenantId"
                }
            ]
        },
        "cacheClusterList": [
            {
            }
        ],
        "pemVpn": {
            "mesh": {
                "agentId": 0,
                "code": "code",
                "description": "description",
                "harborId": 0,
                "id": 0,
                "maxConnection": 0,
                "name": "name",
                "networkZone": "networkZone",
                "occupied": "occupied",
                "password": "password",
                "property": "property",
                "resourceGroup": "resourceGroup",
                "role": "role",
                "siteId": 0,
                "specId": 0,
                "status": "status",
                "taskId": 0,
                "tenantId": "tenantId",
                "type": "type",
                "username": "username",
                "vmTemplateId": 0,
                "meshNode": [
                    {
                        "templateId": 0,
                        "templateName": "templateName",
                        "dataVolumeSize": 0,
                        "regionName": "regionName",
                        "regionCode": "regionCode",
                        "tenantAzName": "tenantAzName",
                        "tenantAzCode": "tenantAzCode",
                        "azName": "azName",
                        "azCode": "azCode",
                        "id": 0,
                        "taskId": 0,
                        "specId": 0,
                        "meshId": 0,
                        "meshCode": "meshCode",
                        "ip": "ip",
                        "role": "role",
                        "adminPort": 0,
                        "workerPort": 0,
                        "tenantId": "tenantId",
                        "resourceGroup": "resourceGroup",
                        "vmId": 0,
                        "status": "status",
                        "routerName": "routerName",
                        "description": "description"
                    }
                ]
            },
            "vpn": {
            }
        },
        "rdbGroup": {
            "rdbCluster": {
                "id": 0,
                "clusterId": "clusterId",
                "clusterName": "clusterName",
                "clusterType": "clusterType",
                "networkZone": "networkZone",
                "occupied": "occupied",
                "property": "property",
                "region": "region",
                "resourceGroup": "resourceGroup",
                "specId": 0,
                "status": "status",
                "taskId": 0,
                "tenantId": "tenantId",
                "updater": "updater",
                "updaterId": "updaterId",
                "version": "version",
                "vmTemplateId": 0,
                "RDBGroup": [
                    {
                        "id": 0,
                        "taskId": 0,
                        "sepcId": 0,
                        "region": "region",
                        "resourceGroup": "resourceGroup",
                        "networkZone": "networkZone",
                        "rdbGroupCode": "rdbGroupCode",
                        "clusterId": 0,
                        "clusterCode": "clusterCode",
                        "harborId": 0,
                        "pemId": 0,
                        "vip": "vip",
                        "az": "az",
                        "vport": 0,
                        "type": "type",
                        "purpose": "purpose",
                        "name": "name",
                        "description": "description"
                    }
                ]
            },
            "rdbGroup": {
            }
        },
        "aemVpnList": [
            {
            }
        ]
    }
}
```



# **nsh - offline - API**

## URL: /environment/v1/nsh/offline ##

## Method : Post ##

## Event ID : EnvNshOffline ##

**Description:**

offline nsh cluster, destroy nsh services

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| clusterId | integer |  | Y | required |
| forceOffline | boolean |  | N |  |

**Range of values**

request example:
```
{
    "clusterId": 0,
    "forceOffline": false
}
```

**struct example:**

```
type NshOfflineRequest struct {
    ClusterId uint64 `json:"clusterId" validate:"required"`
    ForceOffline bool `json:"forceOffline"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **nsh - page - API**

## URL: /environment/v1/nsh/page ##

## Method : Post ##

## Event ID : EnvNshPage ##

**Description:**

query nsh cluster list

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| tenantId | string |  | N |  |
| environmentId | integer |  | N |  |
| imageVersion | string |  | N |  |
| suInstanceID | integer |  | N |  |
| networkZoneId | integer |  | N |  |
| meshId | integer |  | N |  |
| meshVpnId | integer |  | N |  |
| k8sId | integer |  | N |  |
| rdbId | integer |  | N |  |
| cacheId | integer |  | N |  |
| dosId | integer |  | N |  |
| clusterCode | string |  | N |  |
| clusterName | string |  | N |  |
| status | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "tenantId": "tenantId",
    "environmentId": 0,
    "imageVersion": "imageVersion",
    "suInstanceID": 0,
    "networkZoneId": 0,
    "meshId": 0,
    "meshVpnId": 0,
    "k8sId": 0,
    "rdbId": 0,
    "cacheId": 0,
    "dosId": 0,
    "clusterCode": "clusterCode",
    "clusterName": "clusterName",
    "status": "status"
}
```

**struct example:**

```
type NshClusterFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    TenantId string `json:"tenantId"`
    EnvironmentId uint64 `json:"environmentId"`
    ImageVersion string `json:"imageVersion"`
    SuInstanceID uint64 `json:"suInstanceID"`
    NetworkZoneId uint64 `json:"networkZoneId"`
    MeshId uint64 `json:"meshId"`
    MeshVpnId uint64 `json:"meshVpnId"`
    K8sId uint64 `json:"k8sId"`
    RdbId uint64 `json:"rdbId"`
    CacheId uint64 `json:"cacheId"`
    DosId uint64 `json:"dosId"`
    ClusterCode string `json:"clusterCode"`
    ClusterName string `json:"clusterName"`
    Status string `json:"status"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **nsh - update - API**

## URL: /environment/v1/nsh/update ##

## Method : Post ##

## Event ID : EnvNshUpdate ##

**Description:**

change nsh cluster name

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| clusterId | integer |  | Y | required,gt=0 |
| clusterName | string |  | N |  |
| description | string |  | N |  |

**Range of values**

request example:
```
{
    "clusterId": 0,
    "clusterName": "clusterName",
    "description": "description"
}
```

**struct example:**

```
type NshUpdateRequest struct {
    ClusterId uint64 `json:"clusterId" validate:"required,gt=0"`
    ClusterName string `json:"clusterName"`
    Description string `json:"description"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **nsh - upgrade - API**

## URL: /environment/v1/nsh/upgrade ##

## Method : Post ##

## Event ID : EnvNshUpgrade ##

**Description:**

upgrade nsh cluster, change nsh service instanceNum or version

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| clusterId | integer |  | Y | required |
| serviceIdList | array[uint64] |  | N |  |
| packageVersion | string |  | N |  |
| imageVersion | string |  | N |  |
| AdvanceDeployParameter | object |  | N |  |
| AdvanceDeployParameter.podReplicas | integer |  | N |  |
| AdvanceDeployParameter.logLevel | string |  | N |  |
| AdvanceDeployParameter.appLimitsCpu | string |  | N |  |
| AdvanceDeployParameter.appRequestsCpu | string |  | N |  |
| AdvanceDeployParameter.appLimitsMemory | string |  | N |  |
| AdvanceDeployParameter.appRequestsMemory | string |  | N |  |
| parameters | map[string]interface |  | Y | gte=0,dive,required |

**Range of values**

request example:
```
{
    "clusterId": 0,
    "serviceIdList": [
    ],
    "packageVersion": "packageVersion",
    "imageVersion": "imageVersion",
    "AdvanceDeployParameter": {
        "podReplicas": 0,
        "logLevel": "logLevel",
        "appLimitsCpu": "appLimitsCpu",
        "appRequestsCpu": "appRequestsCpu",
        "appLimitsMemory": "appLimitsMemory",
        "appRequestsMemory": "appRequestsMemory"
    },
    "parameters": {
    }
}
```

**struct example:**

```
type NshUpgradeRequest struct {
    ClusterId uint64 `json:"clusterId" validate:"required"`
    ServiceIdList []uint64 `json:"serviceIdList"`
    PackageVersion string `json:"packageVersion"`
    ImageVersion string `json:"imageVersion"`
    AdvanceDeployParameter struct {
        PodReplicas uint64 `json:"podReplicas"`
        LogLevel string `json:"logLevel"`
        AppLimitsCpu string `json:"appLimitsCpu"`
        AppRequestsCpu string `json:"appRequestsCpu"`
        AppLimitsMemory string `json:"appLimitsMemory"`
        AppRequestsMemory string `json:"appRequestsMemory"`
    }
    Parameters map[string]interface{} `json:"parameters" validate:"gte=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| sopId | integer | N |  |
| jobId | integer | N |  |
| taskId | integer | N |  |

**Range of values**

response example:

```
{
    "sopId": 0,
    "jobId": 0,
    "taskId": 0
}
```


