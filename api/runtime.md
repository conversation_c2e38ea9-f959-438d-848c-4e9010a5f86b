
# **runtime - add - API**

## URL: /environment/v1/runtime/add ##

## Method : Post ##

## Event ID : EnvRuntimeAdd ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| environment | string |  | Y | required,max=64 |
| entity | string |  | Y | required,max=64 |
| entityType | string |  | Y | required,name,max=64 |
| suTypeId | integer |  | Y | required |
| suTypeCode | string |  | Y | required |
| su | string |  | N | omitempty,max=64 |
| suInstance | string |  | N | omitempty,max=64 |
| path | string |  | N | min=2,max=128 |
| value | string |  | N | omitempty,max=255 |
| label | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "environment": "environment",
    "entity": "entity",
    "entityType": "entityType",
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "su": "su",
    "suInstance": "suInstance",
    "path": "path",
    "value": "value",
    "label": "label"
}
```

**struct example:**

```
type AddRuntimeConfigRequest struct {
    Environment string `json:"environment" validate:"required,max=64"`
    Entity string `json:"entity" validate:"required,max=64"`
    EntityType string `json:"entityType" validate:"required,name,max=64"`
    SuTypeId uint64 `json:"suTypeId" validate:"required"`
    SuTypeCode string `json:"suTypeCode" validate:"required"`
    Su string `json:"su" validate:"omitempty,max=64"`
    SuInstance string `json:"suInstance" validate:"omitempty,max=64"`
    Path string `json:"path" validate:"min=2,max=128"`
    Value string `json:"value" validate:"omitempty,max=255"`
    Label string `json:"label" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **runtime - column list - API**

## URL: /environment/v1/runtime/column/list ##

## Method : Post ##

## Event ID : EnvRuntimeColumnList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| environmentId | integer |  | N |  |
| suId | integer |  | N |  |
| suIdList | array[uint64] |  | N |  |
| suInstanceId | integer |  | N |  |
| suInstanceIdList | array[uint64] |  | N |  |
| tenantId | string |  | N |  |
| path | string |  | N |  |
| entityId | string |  | N |  |
| entityName | string |  | N |  |
| entityType | string |  | N |  |
| category | string |  | N |  |
| suTypeCode | string |  | N |  |
| description | string |  | N |  |
| domains | array[string] |  | N |  |
| operate | string |  | N |  |
| group | array[RuntimeGroupFilter] |  | N |  |
| RuntimeGroupFilter.environmentId | integer |  | N |  |
| RuntimeGroupFilter.suId | integer |  | N |  |
| RuntimeGroupFilter.suInstanceId | integer |  | N |  |
| RuntimeGroupFilter.path | string |  | N |  |
| RuntimeGroupFilter.entityId | string |  | N |  |
| RuntimeGroupFilter.entity | string |  | N |  |
| RuntimeGroupFilter.entityType | string |  | N |  |
| RuntimeGroupFilter.category | string |  | N |  |
| RuntimeGroupFilter.description | string |  | N |  |
| orderBy | string |  | N | omitempty,max=128 |
| sort | integer |  | N | oneof=0 1 |
| pathFilter | array[string] |  | N | omitempty |
| filterRuntime | boolean |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "environmentId": 0,
    "suId": 0,
    "suIdList": [
    ],
    "suInstanceId": 0,
    "suInstanceIdList": [
    ],
    "tenantId": "tenantId",
    "path": "path",
    "entityId": "entityId",
    "entityName": "entityName",
    "entityType": "entityType",
    "category": "category",
    "suTypeCode": "suTypeCode",
    "description": "description",
    "domains": [
    ],
    "operate": "operate",
    "group": [
        {
            "environmentId": 0,
            "suId": 0,
            "suInstanceId": 0,
            "path": "path",
            "entityId": "entityId",
            "entity": "entity",
            "entityType": "entityType",
            "category": "category",
            "description": "description"
        }
    ],
    "orderBy": "orderBy",
    "sort": 0,
    "pathFilter": [
    ],
    "filterRuntime": false
}
```

**struct example:**

```
type RuntimeConfigFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    EnvironmentId uint64 `json:"environmentId"`
    SuId uint64 `json:"suId"`
    SuIdList []uint64 `json:"suIdList"`
    SuInstanceId uint64 `json:"suInstanceId"`
    SuInstanceIdList []uint64 `json:"suInstanceIdList"`
    TenantId string `json:"tenantId"`
    Path string `json:"path"`
    EntityId string `json:"entityId"`
    EntityName string `json:"entityName"`
    EntityType string `json:"entityType"`
    Category string `json:"category"`
    SuTypeCode string `json:"suTypeCode"`
    Description string `json:"description"`
    Domains []string `json:"domains"`
    Operate string `json:"operate"`
    Group []struct {
            EnvironmentId uint64 `json:"environmentId"`
            SuId uint64 `json:"suId"`
            SuInstanceId uint64 `json:"suInstanceId"`
            Path string `json:"path"`
            EntityId string `json:"entityId"`
            Entity string `json:"entity"`
            EntityType string `json:"entityType"`
            Category string `json:"category"`
            Description string `json:"description"`
        } `json:"group"`
    OrderBy string `json:"orderBy" validate:"omitempty,max=128"`
    Sort int `json:"sort" validate:"oneof=0 1"`
    PathFilter []string `json:"pathFilter" validate:"omitempty"`
    FilterRuntime bool `json:"filterRuntime"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| workspaceId | string | N |  |
| name | string | N |  |
| code | string | N |  |
| phase | string | N |  |
| RuntimeConfig | object | N |  |
| RuntimeConfig.id | integer | N |  |
| RuntimeConfig.entity | string | N |  |
| RuntimeConfig.entityType | string | N |  |
| RuntimeConfig.category | string | N |  |
| RuntimeConfig.entityName | string | N |  |
| RuntimeConfig.environmentId | integer | N |  |
| RuntimeConfig.suTypeCode | string | N |  |
| RuntimeConfig.suId | integer | N |  |
| RuntimeConfig.suInstanceId | integer | N |  |
| RuntimeConfig.path | string | N |  |
| RuntimeConfig.value | string | N |  |
| RuntimeConfig.label | string | N |  |
| RuntimeConfig.tenantId | string | N |  |
| RuntimeConfig.creatorId | string | N |  |
| RuntimeConfig.creator | string | N |  |
| RuntimeConfig.updaterId | string | N |  |
| RuntimeConfig.updater | string | N |  |
| RuntimeConfig.createAt | string | N |  |
| RuntimeConfig.updatedAt | string | N |  |
| RuntimeConfig.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "workspaceId": "workspaceId",
    "name": "name",
    "code": "code",
    "phase": "phase",
    "RuntimeConfig": {
        "id": 0,
        "entity": "entity",
        "entityType": "entityType",
        "category": "category",
        "entityName": "entityName",
        "environmentId": 0,
        "suTypeCode": "suTypeCode",
        "suId": 0,
        "suInstanceId": 0,
        "path": "path",
        "value": "value",
        "label": "label",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **runtime - delete - API**

## URL: /environment/v1/runtime/delete ##

## Method : Post ##

## Event ID : EnvRuntimeDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type stackIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **runtime - detail - API**

## URL: /environment/v1/runtime/detail ##

## Method : Post ##

## Event ID : EnvRuntimeDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type stackIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| workspaceId | string | N |  |
| name | string | N |  |
| code | string | N |  |
| phase | string | N |  |
| RuntimeConfig | object | N |  |
| RuntimeConfig.id | integer | N |  |
| RuntimeConfig.entity | string | N |  |
| RuntimeConfig.entityType | string | N |  |
| RuntimeConfig.category | string | N |  |
| RuntimeConfig.entityName | string | N |  |
| RuntimeConfig.environmentId | integer | N |  |
| RuntimeConfig.suTypeCode | string | N |  |
| RuntimeConfig.suId | integer | N |  |
| RuntimeConfig.suInstanceId | integer | N |  |
| RuntimeConfig.path | string | N |  |
| RuntimeConfig.value | string | N |  |
| RuntimeConfig.label | string | N |  |
| RuntimeConfig.tenantId | string | N |  |
| RuntimeConfig.creatorId | string | N |  |
| RuntimeConfig.creator | string | N |  |
| RuntimeConfig.updaterId | string | N |  |
| RuntimeConfig.updater | string | N |  |
| RuntimeConfig.createAt | string | N |  |
| RuntimeConfig.updatedAt | string | N |  |
| RuntimeConfig.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "workspaceId": "workspaceId",
    "name": "name",
    "code": "code",
    "phase": "phase",
    "RuntimeConfig": {
        "id": 0,
        "entity": "entity",
        "entityType": "entityType",
        "category": "category",
        "entityName": "entityName",
        "environmentId": 0,
        "suTypeCode": "suTypeCode",
        "suId": 0,
        "suInstanceId": 0,
        "path": "path",
        "value": "value",
        "label": "label",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **runtime - list - API**

## URL: /environment/v1/runtime/list ##

## Method : Post ##

## Event ID : EnvRuntimeList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| environmentId | integer |  | N |  |
| suId | integer |  | N |  |
| suIdList | array[uint64] |  | N |  |
| suInstanceId | integer |  | N |  |
| suInstanceIdList | array[uint64] |  | N |  |
| tenantId | string |  | N |  |
| path | string |  | N |  |
| entityId | string |  | N |  |
| entityName | string |  | N |  |
| entityType | string |  | N |  |
| category | string |  | N |  |
| suTypeCode | string |  | N |  |
| description | string |  | N |  |
| domains | array[string] |  | N |  |
| operate | string |  | N |  |
| group | array[RuntimeGroupFilter] |  | N |  |
| RuntimeGroupFilter.environmentId | integer |  | N |  |
| RuntimeGroupFilter.suId | integer |  | N |  |
| RuntimeGroupFilter.suInstanceId | integer |  | N |  |
| RuntimeGroupFilter.path | string |  | N |  |
| RuntimeGroupFilter.entityId | string |  | N |  |
| RuntimeGroupFilter.entity | string |  | N |  |
| RuntimeGroupFilter.entityType | string |  | N |  |
| RuntimeGroupFilter.category | string |  | N |  |
| RuntimeGroupFilter.description | string |  | N |  |
| orderBy | string |  | N | omitempty,max=128 |
| sort | integer |  | N | oneof=0 1 |
| pathFilter | array[string] |  | N | omitempty |
| filterRuntime | boolean |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "environmentId": 0,
    "suId": 0,
    "suIdList": [
    ],
    "suInstanceId": 0,
    "suInstanceIdList": [
    ],
    "tenantId": "tenantId",
    "path": "path",
    "entityId": "entityId",
    "entityName": "entityName",
    "entityType": "entityType",
    "category": "category",
    "suTypeCode": "suTypeCode",
    "description": "description",
    "domains": [
    ],
    "operate": "operate",
    "group": [
        {
            "environmentId": 0,
            "suId": 0,
            "suInstanceId": 0,
            "path": "path",
            "entityId": "entityId",
            "entity": "entity",
            "entityType": "entityType",
            "category": "category",
            "description": "description"
        }
    ],
    "orderBy": "orderBy",
    "sort": 0,
    "pathFilter": [
    ],
    "filterRuntime": false
}
```

**struct example:**

```
type RuntimeConfigFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    EnvironmentId uint64 `json:"environmentId"`
    SuId uint64 `json:"suId"`
    SuIdList []uint64 `json:"suIdList"`
    SuInstanceId uint64 `json:"suInstanceId"`
    SuInstanceIdList []uint64 `json:"suInstanceIdList"`
    TenantId string `json:"tenantId"`
    Path string `json:"path"`
    EntityId string `json:"entityId"`
    EntityName string `json:"entityName"`
    EntityType string `json:"entityType"`
    Category string `json:"category"`
    SuTypeCode string `json:"suTypeCode"`
    Description string `json:"description"`
    Domains []string `json:"domains"`
    Operate string `json:"operate"`
    Group []struct {
            EnvironmentId uint64 `json:"environmentId"`
            SuId uint64 `json:"suId"`
            SuInstanceId uint64 `json:"suInstanceId"`
            Path string `json:"path"`
            EntityId string `json:"entityId"`
            Entity string `json:"entity"`
            EntityType string `json:"entityType"`
            Category string `json:"category"`
            Description string `json:"description"`
        } `json:"group"`
    OrderBy string `json:"orderBy" validate:"omitempty,max=128"`
    Sort int `json:"sort" validate:"oneof=0 1"`
    PathFilter []string `json:"pathFilter" validate:"omitempty"`
    FilterRuntime bool `json:"filterRuntime"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| workspaceId | string | N |  |
| name | string | N |  |
| code | string | N |  |
| phase | string | N |  |
| RuntimeConfig | object | N |  |
| RuntimeConfig.id | integer | N |  |
| RuntimeConfig.entity | string | N |  |
| RuntimeConfig.entityType | string | N |  |
| RuntimeConfig.category | string | N |  |
| RuntimeConfig.entityName | string | N |  |
| RuntimeConfig.environmentId | integer | N |  |
| RuntimeConfig.suTypeCode | string | N |  |
| RuntimeConfig.suId | integer | N |  |
| RuntimeConfig.suInstanceId | integer | N |  |
| RuntimeConfig.path | string | N |  |
| RuntimeConfig.value | string | N |  |
| RuntimeConfig.label | string | N |  |
| RuntimeConfig.tenantId | string | N |  |
| RuntimeConfig.creatorId | string | N |  |
| RuntimeConfig.creator | string | N |  |
| RuntimeConfig.updaterId | string | N |  |
| RuntimeConfig.updater | string | N |  |
| RuntimeConfig.createAt | string | N |  |
| RuntimeConfig.updatedAt | string | N |  |
| RuntimeConfig.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "workspaceId": "workspaceId",
    "name": "name",
    "code": "code",
    "phase": "phase",
    "RuntimeConfig": {
        "id": 0,
        "entity": "entity",
        "entityType": "entityType",
        "category": "category",
        "entityName": "entityName",
        "environmentId": 0,
        "suTypeCode": "suTypeCode",
        "suId": 0,
        "suInstanceId": 0,
        "path": "path",
        "value": "value",
        "label": "label",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **runtime - modify - API**

## URL: /environment/v1/runtime/modify ##

## Method : Post ##

## Event ID : EnvRuntimeModify ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| environmentId | integer |  | Y | required |
| services | array[RuntimeConfigService] |  | Y | required |
| RuntimeConfigService.code | string |  | N |  |
| RuntimeConfigService.serviceInstanceID | integer |  | N |  |
| RuntimeConfigService.releaseID | integer |  | N |  |
| RuntimeConfigService.suId | integer |  | Y | required |
| RuntimeConfigService.suInstanceId | integer |  | Y | required |
| RuntimeConfigService.suInstanceCode | string |  | Y | required |
| RuntimeConfigService.networkZoneId | integer |  | Y | required |
| RuntimeConfigService.suTypeCode | string |  | Y | required |
| RuntimeConfigService.entity | string |  | Y | required |
| RuntimeConfigService.entityType | string |  | N | name,max=64 |
| RuntimeConfigService.category | string |  | N | name,max=64 |
| RuntimeConfigService.entityName | string |  | N | omitempty |
| RuntimeConfigService.description | string |  | N | omitempty |
| RuntimeConfigService.runtimeStatus | string |  | N |  |
| RuntimeConfigService.uuid | string |  | N |  |
| RuntimeConfigService.datas | array[RuntimeConfigElement] |  | N | omitempty,max=255 |
| RuntimeConfigElement.path | string |  | N | min=2,max=128 |
| RuntimeConfigElement.value | string |  | N | omitempty,max=255 |
| RuntimeConfigElement.label | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "environmentId": 0,
    "services": [
        {
            "code": "code",
            "serviceInstanceID": 0,
            "releaseID": 0,
            "suId": 0,
            "suInstanceId": 0,
            "suInstanceCode": "suInstanceCode",
            "networkZoneId": 0,
            "suTypeCode": "suTypeCode",
            "entity": "entity",
            "entityType": "entityType",
            "category": "category",
            "entityName": "entityName",
            "description": "description",
            "runtimeStatus": "runtimeStatus",
            "uuid": "uuid",
            "datas": [
                {
                    "path": "path",
                    "value": "value",
                    "label": "label"
                }
            ]
        }
    ]
}
```

**struct example:**

```
type ModifyRuntimeConfigRequest struct {
    EnvironmentId uint64 `json:"environmentId" validate:"required"`
    Services []struct {
            Code string `json:"code"`
            ServiceInstanceID uint64 `json:"serviceInstanceID"`
            ReleaseID uint64 `json:"releaseID"`
            SuId uint64 `json:"suId" validate:"required"`
            SuInstanceId uint64 `json:"suInstanceId" validate:"required"`
            SuInstanceCode string `json:"suInstanceCode" validate:"required"`
            NetworkZoneId uint64 `json:"networkZoneId" validate:"required"`
            SuTypeCode string `json:"suTypeCode" validate:"required"`
            Entity string `json:"entity" validate:"required"`
            EntityType string `json:"entityType" validate:"name,max=64"`
            Category string `json:"category" validate:"name,max=64"`
            EntityName string `json:"entityName" validate:"omitempty"`
            Description string `json:"description" validate:"omitempty"`
            RuntimeStatus string `json:"runtimeStatus"`
            Uuid string `json:"uuid"`
            Datas []struct {
                    Path string `json:"path" validate:"min=2,max=128"`
                    Value string `json:"value" validate:"omitempty,max=255"`
                    Label string `json:"label" validate:"omitempty,max=255"`
                } `json:"datas" validate:"omitempty,max=255"`
        } `json:"services" validate:"required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **runtime - page - API**

## URL: /environment/v1/runtime/page ##

## Method : Post ##

## Event ID : EnvRuntimePage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| environmentId | integer |  | N |  |
| suId | integer |  | N |  |
| suIdList | array[uint64] |  | N |  |
| suInstanceId | integer |  | N |  |
| suInstanceIdList | array[uint64] |  | N |  |
| tenantId | string |  | N |  |
| path | string |  | N |  |
| entityId | string |  | N |  |
| entityName | string |  | N |  |
| entityType | string |  | N |  |
| category | string |  | N |  |
| suTypeCode | string |  | N |  |
| description | string |  | N |  |
| domains | array[string] |  | N |  |
| operate | string |  | N |  |
| group | array[RuntimeGroupFilter] |  | N |  |
| RuntimeGroupFilter.environmentId | integer |  | N |  |
| RuntimeGroupFilter.suId | integer |  | N |  |
| RuntimeGroupFilter.suInstanceId | integer |  | N |  |
| RuntimeGroupFilter.path | string |  | N |  |
| RuntimeGroupFilter.entityId | string |  | N |  |
| RuntimeGroupFilter.entity | string |  | N |  |
| RuntimeGroupFilter.entityType | string |  | N |  |
| RuntimeGroupFilter.category | string |  | N |  |
| RuntimeGroupFilter.description | string |  | N |  |
| orderBy | string |  | N | omitempty,max=128 |
| sort | integer |  | N | oneof=0 1 |
| pathFilter | array[string] |  | N | omitempty |
| filterRuntime | boolean |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "environmentId": 0,
    "suId": 0,
    "suIdList": [
    ],
    "suInstanceId": 0,
    "suInstanceIdList": [
    ],
    "tenantId": "tenantId",
    "path": "path",
    "entityId": "entityId",
    "entityName": "entityName",
    "entityType": "entityType",
    "category": "category",
    "suTypeCode": "suTypeCode",
    "description": "description",
    "domains": [
    ],
    "operate": "operate",
    "group": [
        {
            "environmentId": 0,
            "suId": 0,
            "suInstanceId": 0,
            "path": "path",
            "entityId": "entityId",
            "entity": "entity",
            "entityType": "entityType",
            "category": "category",
            "description": "description"
        }
    ],
    "orderBy": "orderBy",
    "sort": 0,
    "pathFilter": [
    ],
    "filterRuntime": false
}
```

**struct example:**

```
type RuntimeConfigFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    EnvironmentId uint64 `json:"environmentId"`
    SuId uint64 `json:"suId"`
    SuIdList []uint64 `json:"suIdList"`
    SuInstanceId uint64 `json:"suInstanceId"`
    SuInstanceIdList []uint64 `json:"suInstanceIdList"`
    TenantId string `json:"tenantId"`
    Path string `json:"path"`
    EntityId string `json:"entityId"`
    EntityName string `json:"entityName"`
    EntityType string `json:"entityType"`
    Category string `json:"category"`
    SuTypeCode string `json:"suTypeCode"`
    Description string `json:"description"`
    Domains []string `json:"domains"`
    Operate string `json:"operate"`
    Group []struct {
            EnvironmentId uint64 `json:"environmentId"`
            SuId uint64 `json:"suId"`
            SuInstanceId uint64 `json:"suInstanceId"`
            Path string `json:"path"`
            EntityId string `json:"entityId"`
            Entity string `json:"entity"`
            EntityType string `json:"entityType"`
            Category string `json:"category"`
            Description string `json:"description"`
        } `json:"group"`
    OrderBy string `json:"orderBy" validate:"omitempty,max=128"`
    Sort int `json:"sort" validate:"oneof=0 1"`
    PathFilter []string `json:"pathFilter" validate:"omitempty"`
    FilterRuntime bool `json:"filterRuntime"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **runtime - save - API**

## URL: /environment/v1/runtime/save ##

## Method : Post ##

## Event ID : EnvRuntimeSave ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| environment | string |  | Y | required,max=64 |
| entity | string |  | Y | required,max=64 |
| entityType | string |  | Y | required,name,max=64 |
| suTypeId | integer |  | Y | required |
| suTypeCode | string |  | Y | required |
| su | string |  | N | omitempty,max=64 |
| suInstance | string |  | N | omitempty,max=64 |
| path | string |  | N | min=2,max=128 |
| value | string |  | N | omitempty,max=255 |
| label | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "environment": "environment",
    "entity": "entity",
    "entityType": "entityType",
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "su": "su",
    "suInstance": "suInstance",
    "path": "path",
    "value": "value",
    "label": "label"
}
```

**struct example:**

```
type AddRuntimeConfigRequest struct {
    Environment string `json:"environment" validate:"required,max=64"`
    Entity string `json:"entity" validate:"required,max=64"`
    EntityType string `json:"entityType" validate:"required,name,max=64"`
    SuTypeId uint64 `json:"suTypeId" validate:"required"`
    SuTypeCode string `json:"suTypeCode" validate:"required"`
    Su string `json:"su" validate:"omitempty,max=64"`
    SuInstance string `json:"suInstance" validate:"omitempty,max=64"`
    Path string `json:"path" validate:"min=2,max=128"`
    Value string `json:"value" validate:"omitempty,max=255"`
    Label string `json:"label" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **runtime - update - API**

## URL: /environment/v1/runtime/update ##

## Method : Post ##

## Event ID : EnvRuntimeUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | gt=0,required |
| value | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "id": 0,
    "value": "value"
}
```

**struct example:**

```
type UpdateRuntimeConfigRequest struct {
    Id uint64 `json:"id" validate:"gt=0,required"`
    Value string `json:"value" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


