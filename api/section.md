
# **section - create - API**

## URL: /environment/v1/section/create ##

## Method : Post ##

## Event ID : EnvSectionCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| stackId | integer |  | Y | required,gt=0 |
| domain | string |  | N | name,max=64 |
| scope | string |  | N | name,max=64 |
| code | string |  | N | name,max=64 |
| name | string |  | N | omitempty,max=64 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "stackId": 0,
    "domain": "domain",
    "scope": "scope",
    "code": "code",
    "name": "name",
    "description": "description"
}
```

**struct example:**

```
type CreateSectionRequest struct {
    StackId uint64 `json:"stackId" validate:"required,gt=0"`
    Domain string `json:"domain" validate:"name,max=64"`
    Scope string `json:"scope" validate:"name,max=64"`
    Code string `json:"code" validate:"name,max=64"`
    Name string `json:"name" validate:"omitempty,max=64"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **section - delete - API**

## URL: /environment/v1/section/delete ##

## Method : Post ##

## Event ID : EnvSectionDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type sectionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **section - detail - API**

## URL: /environment/v1/section/detail ##

## Method : Post ##

## Event ID : EnvSectionDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type sectionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| workspaceId | integer | N |  |
| code | string | N |  |
| name | string | N |  |
| status | string | N |  |
| Section | object | N |  |
| Section.id | integer | N |  |
| Section.stackId | integer | N |  |
| Section.scope | string | N |  |
| Section.domain | string | N |  |
| Section.serviceType | string | N |  |
| Section.targetCode | string | N |  |
| Section.name | string | N |  |
| Section.code | string | N |  |
| Section.description | string | N |  |
| Section.tenantId | string | N |  |
| Section.updaterId | string | N |  |
| Section.updater | string | N |  |
| Section.creatorId | string | N |  |
| Section.creator | string | N |  |
| Section.createAt | string | N |  |
| Section.updatedAt | string | N |  |
| Section.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "workspaceId": 0,
    "code": "code",
    "name": "name",
    "status": "status",
    "Section": {
        "id": 0,
        "stackId": 0,
        "scope": "scope",
        "domain": "domain",
        "serviceType": "serviceType",
        "targetCode": "targetCode",
        "name": "name",
        "code": "code",
        "description": "description",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **section - fields - API**

## URL: /environment/v1/section/fields ##

## Method : Post ##

## Event ID : EnvSectionFields ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| PatchScope | object |  | N |  |
| PatchScope.instanceId | integer |  | N | gte=0 |
| PatchScope.serviceCode | string |  | N | omitempty,name,max=64 |
| PatchScope.serviceType | string |  | N | omitempty,name,max=64 |
| PatchScope.suType | string |  | N | omitempty,name,max=64 |
| PatchScope.suInstanceId | integer |  | N | gte=0 |
| PatchScope.suId | integer |  | N | gte=0 |
| PatchScope.environmentId | integer |  | N | gte=0 |
| PatchScope.planId | integer |  | N | gte=0 |
| PatchScope.packageID | integer |  | N |  |
| sectionId | integer |  | N |  |

**Range of values**

request example:
```
{
    "PatchScope": {
        "instanceId": 0,
        "serviceCode": "serviceCode",
        "serviceType": "serviceType",
        "suType": "suType",
        "suInstanceId": 0,
        "suId": 0,
        "environmentId": 0,
        "planId": 0,
        "packageID": 0
    },
    "sectionId": 0
}
```

**struct example:**

```
type QueryFieldRequest struct {
    PatchScope struct {
        InstanceId uint64 `json:"instanceId" validate:"gte=0"`
        ServiceCode string `json:"serviceCode" validate:"omitempty,name,max=64"`
        ServiceType string `json:"serviceType" validate:"omitempty,name,max=64"`
        SuType string `json:"suType" validate:"omitempty,name,max=64"`
        SuInstanceId uint64 `json:"suInstanceId" validate:"gte=0"`
        SuId uint64 `json:"suId" validate:"gte=0"`
        EnvironmentId uint64 `json:"environmentId" validate:"gte=0"`
        PlanId uint64 `json:"planId" validate:"gte=0"`
        PackageID uint64 `json:"packageID"`
    }
    SectionId uint64 `json:"sectionId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| requestId | string | N |  |
| fields | array[FieldInfo] | N |  |
| FieldInfo.domain | string | N |  |
| FieldInfo.group | string | N |  |
| FieldInfo.label | string | N |  |
| FieldInfo.columnNote | string | N |  |
| FieldInfo.placeholder | string | N |  |
| FieldInfo.unit | string | N |  |
| FieldInfo.necessary | boolean | N |  |
| FieldInfo.columnPath | string | N |  |
| FieldInfo.type | string | N |  |
| FieldInfo.dataType | string | N |  |
| FieldInfo.columnValue | string | N |  |
| FieldInfo.min | number | N |  |
| FieldInfo.max | number | N |  |
| FieldInfo.readOnly | boolean | N |  |
| FieldInfo.hidden | boolean | N |  |
| FieldInfo.validate | string | N |  |
| FieldInfo.items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |
| FieldInfo.Field | object | N |  |
| Field.id | integer | N |  |
| Field.stackId | integer | N |  |
| Field.sectionId | integer | N |  |
| Field.columnId | string | N |  |
| Field.scope | integer | N |  |
| Field.note | string | N |  |
| Field.priority | integer | N |  |
| Field.path | string | N |  |
| Field.value | string | N |  |
| Field.tenantId | string | N |  |
| Field.updaterId | string | N |  |
| Field.updater | string | N |  |
| Field.creatorId | string | N |  |
| Field.creator | string | N |  |
| Field.createAt | string | N |  |
| Field.updatedAt | string | N |  |
| Field.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "requestId": "requestId",
    "fields": [
        {
            "domain": "domain",
            "group": "group",
            "label": "label",
            "columnNote": "columnNote",
            "placeholder": "placeholder",
            "unit": "unit",
            "necessary": false,
            "columnPath": "columnPath",
            "type": "type",
            "dataType": "dataType",
            "columnValue": "columnValue",
            "min": "min",
            "max": "max",
            "readOnly": false,
            "hidden": false,
            "validate": "validate",
            "items": [
                {
                    "value": "value",
                    "text": "text"
                }
            ],
            "Field": {
                "id": 0,
                "stackId": 0,
                "sectionId": 0,
                "columnId": "columnId",
                "scope": 0,
                "note": "note",
                "priority": 0,
                "path": "path",
                "value": "value",
                "tenantId": "tenantId",
                "updaterId": "updaterId",
                "updater": "updater",
                "creatorId": "creatorId",
                "creator": "creator",
                "createAt": "createAt",
                "updatedAt": "updatedAt",
                "deletedAt": "deletedAt"
            }
        }
    ]
}
```



# **section - list - API**

## URL: /environment/v1/section/list ##

## Method : Post ##

## Event ID : EnvSectionList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| tenantId | string |  | N |  |
| domain | string |  | N |  |
| domainList | array[string] |  | N |  |
| code | string |  | N |  |
| codeList | array[string] |  | N |  |
| serviceType | string |  | N |  |
| targetCode | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "tenantId": "tenantId",
    "domain": "domain",
    "domainList": [
    ],
    "code": "code",
    "codeList": [
    ],
    "serviceType": "serviceType",
    "targetCode": "targetCode"
}
```

**struct example:**

```
type SectionFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    TenantId string `json:"tenantId"`
    Domain string `json:"domain"`
    DomainList []string `json:"domainList"`
    Code string `json:"code"`
    CodeList []string `json:"codeList"`
    ServiceType string `json:"serviceType"`
    TargetCode string `json:"targetCode"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| workspaceId | integer | N |  |
| code | string | N |  |
| name | string | N |  |
| status | string | N |  |
| Section | object | N |  |
| Section.id | integer | N |  |
| Section.stackId | integer | N |  |
| Section.scope | string | N |  |
| Section.domain | string | N |  |
| Section.serviceType | string | N |  |
| Section.targetCode | string | N |  |
| Section.name | string | N |  |
| Section.code | string | N |  |
| Section.description | string | N |  |
| Section.tenantId | string | N |  |
| Section.updaterId | string | N |  |
| Section.updater | string | N |  |
| Section.creatorId | string | N |  |
| Section.creator | string | N |  |
| Section.createAt | string | N |  |
| Section.updatedAt | string | N |  |
| Section.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "workspaceId": 0,
    "code": "code",
    "name": "name",
    "status": "status",
    "Section": {
        "id": 0,
        "stackId": 0,
        "scope": "scope",
        "domain": "domain",
        "serviceType": "serviceType",
        "targetCode": "targetCode",
        "name": "name",
        "code": "code",
        "description": "description",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **section - page - API**

## URL: /environment/v1/section/page ##

## Method : Post ##

## Event ID : EnvSectionPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| tenantId | string |  | N |  |
| domain | string |  | N |  |
| domainList | array[string] |  | N |  |
| code | string |  | N |  |
| codeList | array[string] |  | N |  |
| serviceType | string |  | N |  |
| targetCode | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "tenantId": "tenantId",
    "domain": "domain",
    "domainList": [
    ],
    "code": "code",
    "codeList": [
    ],
    "serviceType": "serviceType",
    "targetCode": "targetCode"
}
```

**struct example:**

```
type SectionFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    TenantId string `json:"tenantId"`
    Domain string `json:"domain"`
    DomainList []string `json:"domainList"`
    Code string `json:"code"`
    CodeList []string `json:"codeList"`
    ServiceType string `json:"serviceType"`
    TargetCode string `json:"targetCode"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **section - update - API**

## URL: /environment/v1/section/update ##

## Method : Post ##

## Event ID : EnvSectionUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| sectionId | integer |  | Y | required,gt=0 |
| name | string |  | N | omitempty,max=64 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "sectionId": 0,
    "name": "name",
    "description": "description"
}
```

**struct example:**

```
type UpdateSectionRequest struct {
    SectionId uint64 `json:"sectionId" validate:"required,gt=0"`
    Name string `json:"name" validate:"omitempty,max=64"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


