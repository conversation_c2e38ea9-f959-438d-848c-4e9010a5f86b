
# **service - instance create - API**

## URL: /environment/v1/service/instance/create ##

## Method : Post ##

## Event ID : EnvSICreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| packageId | integer |  | N | omitempty |
| suInstanceId | integer |  | Y | required,gt=0 |
| serviceIdList | array[string] |  | Y | gt=0,dive,required |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "packageId": 0,
    "suInstanceId": 0,
    "serviceIdList": [
    ],
    "description": "description"
}
```

**struct example:**

```
type CreateServiceInstanceRequest struct {
    PackageId uint64 `json:"packageId" validate:"omitempty"`
    SuInstanceId uint64 `json:"suInstanceId" validate:"required,gt=0"`
    ServiceIdList []string `json:"serviceIdList" validate:"gt=0,dive,required"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **service - instance detail - API**

## URL: /environment/v1/service/instance/detail ##

## Method : Post ##

## Event ID : EnvSIDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type serviceInstanceIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| suInstanceName | string | N |  |
| suInstanceCode | string | N |  |
| suName | string | N |  |
| suCode | string | N |  |
| ServiceInstance | object | N |  |
| ServiceInstance.id | integer | N |  |
| ServiceInstance.refId | integer | N |  |
| ServiceInstance.stackId | integer | N |  |
| ServiceInstance.stackInstanceId | integer | N |  |
| ServiceInstance.serviceId | string | N |  |
| ServiceInstance.packageId | integer | N |  |
| ServiceInstance.appId | integer | N |  |
| ServiceInstance.suTypeId | integer | N |  |
| ServiceInstance.suTypeCode | string | N |  |
| ServiceInstance.name | string | N |  |
| ServiceInstance.code | string | N |  |
| ServiceInstance.dbClusterId | string | N |  |
| ServiceInstance.dbUsername | string | N |  |
| ServiceInstance.dbPassword | string | N |  |
| ServiceInstance.dbName | array[string] | N |  |
| ServiceInstance.sections | array[string] | N |  |
| ServiceInstance.serviceType | string | N |  |
| ServiceInstance.version | string | N |  |
| ServiceInstance.replicas | integer | N |  |
| ServiceInstance.published | boolean | N |  |
| ServiceInstance.status | string | N |  |
| ServiceInstance.runningStatus | string | N |  |
| ServiceInstance.brokerVpnId | integer | N |  |
| ServiceInstance.releaseId | integer | N |  |
| ServiceInstance.releaseCode | string | N |  |
| ServiceInstance.environmentId | integer | N |  |
| ServiceInstance.suId | integer | N |  |
| ServiceInstance.suInstanceId | integer | N |  |
| ServiceInstance.serviceUuid | string | N |  |
| ServiceInstance.currentVersionId | integer | N |  |
| ServiceInstance.targetVersionId | integer | N |  |
| ServiceInstance.description | string | N |  |
| ServiceInstance.meshRole | string | N |  |
| ServiceInstance.svrType | string | N |  |
| ServiceInstance.tenantId | string | N |  |
| ServiceInstance.updaterId | string | N |  |
| ServiceInstance.updater | string | N |  |
| ServiceInstance.creatorId | string | N |  |
| ServiceInstance.creator | string | N |  |
| ServiceInstance.createAt | string | N |  |
| ServiceInstance.updatedAt | string | N |  |
| ServiceInstance.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "suInstanceName": "suInstanceName",
    "suInstanceCode": "suInstanceCode",
    "suName": "suName",
    "suCode": "suCode",
    "ServiceInstance": {
        "id": 0,
        "refId": 0,
        "stackId": 0,
        "stackInstanceId": 0,
        "serviceId": "serviceId",
        "packageId": 0,
        "appId": 0,
        "suTypeId": 0,
        "suTypeCode": "suTypeCode",
        "name": "name",
        "code": "code",
        "dbClusterId": "dbClusterId",
        "dbUsername": "dbUsername",
        "dbPassword": "dbPassword",
        "dbName": [
        ],
        "sections": [
        ],
        "serviceType": "serviceType",
        "version": "version",
        "replicas": 0,
        "published": false,
        "status": "status",
        "runningStatus": "runningStatus",
        "brokerVpnId": 0,
        "releaseId": 0,
        "releaseCode": "releaseCode",
        "environmentId": 0,
        "suId": 0,
        "suInstanceId": 0,
        "serviceUuid": "serviceUuid",
        "currentVersionId": 0,
        "targetVersionId": 0,
        "description": "description",
        "meshRole": "meshRole",
        "svrType": "svrType",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **service - instance list - API**

## URL: /environment/v1/service/instance/list ##

## Method : Post ##

## Event ID : EnvSIList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| idList | array[uint64] |  | N |  |
| stackId | integer |  | N |  |
| packageId | integer |  | N |  |
| stackInstanceId | integer |  | N |  |
| environmentId | integer |  | N |  |
| serviceRefId | integer |  | N |  |
| serviceId | string |  | N |  |
| serviceUuid | string |  | N |  |
| name | string |  | N |  |
| code | string |  | N |  |
| brokerVpnId | integer |  | N |  |
| tenantId | string |  | N |  |
| suTypeCode | string |  | N |  |
| serviceType | string |  | N |  |
| suTypeID | integer |  | N |  |
| suId | integer |  | N |  |
| suInstanceID | integer |  | N |  |
| suTypeIdList | array[uint64] |  | N |  |
| SuIDList | array[uint64] |  | N |  |
| SuInstanceIDList | array[uint64] |  | N |  |
| releaseId | integer |  | N |  |
| description | string |  | N |  |
| serviceCodeList | array[string] |  | N |  |
| serviceNameList | array[string] |  | N |  |
| descriptionList | array[string] |  | N |  |
| -- | boolean |  | N |  |
| status | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "idList": [
    ],
    "stackId": 0,
    "packageId": 0,
    "stackInstanceId": 0,
    "environmentId": 0,
    "serviceRefId": 0,
    "serviceId": "serviceId",
    "serviceUuid": "serviceUuid",
    "name": "name",
    "code": "code",
    "brokerVpnId": 0,
    "tenantId": "tenantId",
    "suTypeCode": "suTypeCode",
    "serviceType": "serviceType",
    "suTypeID": 0,
    "suId": 0,
    "suInstanceID": 0,
    "suTypeIdList": [
    ],
    "SuIDList": [
    ],
    "SuInstanceIDList": [
    ],
    "releaseId": 0,
    "description": "description",
    "serviceCodeList": [
    ],
    "serviceNameList": [
    ],
    "descriptionList": [
    ],
    "--": false,
    "status": "status"
}
```

**struct example:**

```
type ServiceInstanceFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    IdList []uint64 `json:"idList"`
    StackId uint64 `json:"stackId"`
    PackageId uint64 `json:"packageId"`
    StackInstanceId uint64 `json:"stackInstanceId"`
    EnvironmentId uint64 `json:"environmentId"`
    ServiceRefId uint64 `json:"serviceRefId"`
    ServiceId string `json:"serviceId"`
    ServiceUuid string `json:"serviceUuid"`
    Name string `json:"name"`
    Code string `json:"code"`
    BrokerVpnId uint64 `json:"brokerVpnId"`
    TenantId string `json:"tenantId"`
    SuTypeCode string `json:"suTypeCode"`
    ServiceType string `json:"serviceType"`
    SuTypeID uint64 `json:"suTypeID"`
    SuId uint64 `json:"suId"`
    SuInstanceID uint64 `json:"suInstanceID"`
    SuTypeIdList []uint64 `json:"suTypeIdList"`
    SuIDList []uint64 `json:"SuIDList"`
    SuInstanceIDList []uint64 `json:"SuInstanceIDList"`
    ReleaseId uint64 `json:"releaseId"`
    Description string `json:"description"`
    ServiceCodeList []string `json:"serviceCodeList"`
    ServiceNameList []string `json:"serviceNameList"`
    DescriptionList []string `json:"descriptionList"`
    -- bool `json:"--"`
    Status string `json:"status"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| suInstanceName | string | N |  |
| suInstanceCode | string | N |  |
| suName | string | N |  |
| suCode | string | N |  |
| ServiceInstance | object | N |  |
| ServiceInstance.id | integer | N |  |
| ServiceInstance.refId | integer | N |  |
| ServiceInstance.stackId | integer | N |  |
| ServiceInstance.stackInstanceId | integer | N |  |
| ServiceInstance.serviceId | string | N |  |
| ServiceInstance.packageId | integer | N |  |
| ServiceInstance.appId | integer | N |  |
| ServiceInstance.suTypeId | integer | N |  |
| ServiceInstance.suTypeCode | string | N |  |
| ServiceInstance.name | string | N |  |
| ServiceInstance.code | string | N |  |
| ServiceInstance.dbClusterId | string | N |  |
| ServiceInstance.dbUsername | string | N |  |
| ServiceInstance.dbPassword | string | N |  |
| ServiceInstance.dbName | array[string] | N |  |
| ServiceInstance.sections | array[string] | N |  |
| ServiceInstance.serviceType | string | N |  |
| ServiceInstance.version | string | N |  |
| ServiceInstance.replicas | integer | N |  |
| ServiceInstance.published | boolean | N |  |
| ServiceInstance.status | string | N |  |
| ServiceInstance.runningStatus | string | N |  |
| ServiceInstance.brokerVpnId | integer | N |  |
| ServiceInstance.releaseId | integer | N |  |
| ServiceInstance.releaseCode | string | N |  |
| ServiceInstance.environmentId | integer | N |  |
| ServiceInstance.suId | integer | N |  |
| ServiceInstance.suInstanceId | integer | N |  |
| ServiceInstance.serviceUuid | string | N |  |
| ServiceInstance.currentVersionId | integer | N |  |
| ServiceInstance.targetVersionId | integer | N |  |
| ServiceInstance.description | string | N |  |
| ServiceInstance.meshRole | string | N |  |
| ServiceInstance.svrType | string | N |  |
| ServiceInstance.tenantId | string | N |  |
| ServiceInstance.updaterId | string | N |  |
| ServiceInstance.updater | string | N |  |
| ServiceInstance.creatorId | string | N |  |
| ServiceInstance.creator | string | N |  |
| ServiceInstance.createAt | string | N |  |
| ServiceInstance.updatedAt | string | N |  |
| ServiceInstance.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "suInstanceName": "suInstanceName",
    "suInstanceCode": "suInstanceCode",
    "suName": "suName",
    "suCode": "suCode",
    "ServiceInstance": {
        "id": 0,
        "refId": 0,
        "stackId": 0,
        "stackInstanceId": 0,
        "serviceId": "serviceId",
        "packageId": 0,
        "appId": 0,
        "suTypeId": 0,
        "suTypeCode": "suTypeCode",
        "name": "name",
        "code": "code",
        "dbClusterId": "dbClusterId",
        "dbUsername": "dbUsername",
        "dbPassword": "dbPassword",
        "dbName": [
        ],
        "sections": [
        ],
        "serviceType": "serviceType",
        "version": "version",
        "replicas": 0,
        "published": false,
        "status": "status",
        "runningStatus": "runningStatus",
        "brokerVpnId": 0,
        "releaseId": 0,
        "releaseCode": "releaseCode",
        "environmentId": 0,
        "suId": 0,
        "suInstanceId": 0,
        "serviceUuid": "serviceUuid",
        "currentVersionId": 0,
        "targetVersionId": 0,
        "description": "description",
        "meshRole": "meshRole",
        "svrType": "svrType",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **service - instance offline - API**

## URL: /environment/v1/service/instance/offline ##

## Method : Post ##

## Event ID : EnvSIOffline ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type serviceInstanceIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| suInstanceName | string | N |  |
| suInstanceCode | string | N |  |
| suName | string | N |  |
| suCode | string | N |  |
| ServiceInstance | object | N |  |
| ServiceInstance.id | integer | N |  |
| ServiceInstance.refId | integer | N |  |
| ServiceInstance.stackId | integer | N |  |
| ServiceInstance.stackInstanceId | integer | N |  |
| ServiceInstance.serviceId | string | N |  |
| ServiceInstance.packageId | integer | N |  |
| ServiceInstance.appId | integer | N |  |
| ServiceInstance.suTypeId | integer | N |  |
| ServiceInstance.suTypeCode | string | N |  |
| ServiceInstance.name | string | N |  |
| ServiceInstance.code | string | N |  |
| ServiceInstance.dbClusterId | string | N |  |
| ServiceInstance.dbUsername | string | N |  |
| ServiceInstance.dbPassword | string | N |  |
| ServiceInstance.dbName | array[string] | N |  |
| ServiceInstance.sections | array[string] | N |  |
| ServiceInstance.serviceType | string | N |  |
| ServiceInstance.version | string | N |  |
| ServiceInstance.replicas | integer | N |  |
| ServiceInstance.published | boolean | N |  |
| ServiceInstance.status | string | N |  |
| ServiceInstance.runningStatus | string | N |  |
| ServiceInstance.brokerVpnId | integer | N |  |
| ServiceInstance.releaseId | integer | N |  |
| ServiceInstance.releaseCode | string | N |  |
| ServiceInstance.environmentId | integer | N |  |
| ServiceInstance.suId | integer | N |  |
| ServiceInstance.suInstanceId | integer | N |  |
| ServiceInstance.serviceUuid | string | N |  |
| ServiceInstance.currentVersionId | integer | N |  |
| ServiceInstance.targetVersionId | integer | N |  |
| ServiceInstance.description | string | N |  |
| ServiceInstance.meshRole | string | N |  |
| ServiceInstance.svrType | string | N |  |
| ServiceInstance.tenantId | string | N |  |
| ServiceInstance.updaterId | string | N |  |
| ServiceInstance.updater | string | N |  |
| ServiceInstance.creatorId | string | N |  |
| ServiceInstance.creator | string | N |  |
| ServiceInstance.createAt | string | N |  |
| ServiceInstance.updatedAt | string | N |  |
| ServiceInstance.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "suInstanceName": "suInstanceName",
    "suInstanceCode": "suInstanceCode",
    "suName": "suName",
    "suCode": "suCode",
    "ServiceInstance": {
        "id": 0,
        "refId": 0,
        "stackId": 0,
        "stackInstanceId": 0,
        "serviceId": "serviceId",
        "packageId": 0,
        "appId": 0,
        "suTypeId": 0,
        "suTypeCode": "suTypeCode",
        "name": "name",
        "code": "code",
        "dbClusterId": "dbClusterId",
        "dbUsername": "dbUsername",
        "dbPassword": "dbPassword",
        "dbName": [
        ],
        "sections": [
        ],
        "serviceType": "serviceType",
        "version": "version",
        "replicas": 0,
        "published": false,
        "status": "status",
        "runningStatus": "runningStatus",
        "brokerVpnId": 0,
        "releaseId": 0,
        "releaseCode": "releaseCode",
        "environmentId": 0,
        "suId": 0,
        "suInstanceId": 0,
        "serviceUuid": "serviceUuid",
        "currentVersionId": 0,
        "targetVersionId": 0,
        "description": "description",
        "meshRole": "meshRole",
        "svrType": "svrType",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **service - instance page - API**

## URL: /environment/v1/service/instance/page ##

## Method : Post ##

## Event ID : EnvSIPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| idList | array[uint64] |  | N |  |
| stackId | integer |  | N |  |
| packageId | integer |  | N |  |
| stackInstanceId | integer |  | N |  |
| environmentId | integer |  | N |  |
| serviceRefId | integer |  | N |  |
| serviceId | string |  | N |  |
| serviceUuid | string |  | N |  |
| name | string |  | N |  |
| code | string |  | N |  |
| brokerVpnId | integer |  | N |  |
| tenantId | string |  | N |  |
| suTypeCode | string |  | N |  |
| serviceType | string |  | N |  |
| suTypeID | integer |  | N |  |
| suId | integer |  | N |  |
| suInstanceID | integer |  | N |  |
| suTypeIdList | array[uint64] |  | N |  |
| SuIDList | array[uint64] |  | N |  |
| SuInstanceIDList | array[uint64] |  | N |  |
| releaseId | integer |  | N |  |
| description | string |  | N |  |
| serviceCodeList | array[string] |  | N |  |
| serviceNameList | array[string] |  | N |  |
| descriptionList | array[string] |  | N |  |
| -- | boolean |  | N |  |
| status | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "idList": [
    ],
    "stackId": 0,
    "packageId": 0,
    "stackInstanceId": 0,
    "environmentId": 0,
    "serviceRefId": 0,
    "serviceId": "serviceId",
    "serviceUuid": "serviceUuid",
    "name": "name",
    "code": "code",
    "brokerVpnId": 0,
    "tenantId": "tenantId",
    "suTypeCode": "suTypeCode",
    "serviceType": "serviceType",
    "suTypeID": 0,
    "suId": 0,
    "suInstanceID": 0,
    "suTypeIdList": [
    ],
    "SuIDList": [
    ],
    "SuInstanceIDList": [
    ],
    "releaseId": 0,
    "description": "description",
    "serviceCodeList": [
    ],
    "serviceNameList": [
    ],
    "descriptionList": [
    ],
    "--": false,
    "status": "status"
}
```

**struct example:**

```
type ServiceInstanceFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    IdList []uint64 `json:"idList"`
    StackId uint64 `json:"stackId"`
    PackageId uint64 `json:"packageId"`
    StackInstanceId uint64 `json:"stackInstanceId"`
    EnvironmentId uint64 `json:"environmentId"`
    ServiceRefId uint64 `json:"serviceRefId"`
    ServiceId string `json:"serviceId"`
    ServiceUuid string `json:"serviceUuid"`
    Name string `json:"name"`
    Code string `json:"code"`
    BrokerVpnId uint64 `json:"brokerVpnId"`
    TenantId string `json:"tenantId"`
    SuTypeCode string `json:"suTypeCode"`
    ServiceType string `json:"serviceType"`
    SuTypeID uint64 `json:"suTypeID"`
    SuId uint64 `json:"suId"`
    SuInstanceID uint64 `json:"suInstanceID"`
    SuTypeIdList []uint64 `json:"suTypeIdList"`
    SuIDList []uint64 `json:"SuIDList"`
    SuInstanceIDList []uint64 `json:"SuInstanceIDList"`
    ReleaseId uint64 `json:"releaseId"`
    Description string `json:"description"`
    ServiceCodeList []string `json:"serviceCodeList"`
    ServiceNameList []string `json:"serviceNameList"`
    DescriptionList []string `json:"descriptionList"`
    -- bool `json:"--"`
    Status string `json:"status"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| refId | integer | N |  |
| stackId | integer | N |  |
| stackInstanceId | integer | N |  |
| serviceId | string | N |  |
| packageId | integer | N |  |
| appId | integer | N |  |
| suTypeId | integer | N |  |
| suTypeCode | string | N |  |
| name | string | N |  |
| code | string | N |  |
| dbClusterId | string | N |  |
| dbUsername | string | N |  |
| dbPassword | string | N |  |
| dbName | array[string] | N |  |
| sections | array[string] | N |  |
| serviceType | string | N |  |
| version | string | N |  |
| replicas | integer | N |  |
| published | boolean | N |  |
| status | string | N |  |
| runningStatus | string | N |  |
| brokerVpnId | integer | N |  |
| releaseId | integer | N |  |
| releaseCode | string | N |  |
| environmentId | integer | N |  |
| suId | integer | N |  |
| suInstanceId | integer | N |  |
| serviceUuid | string | N |  |
| currentVersionId | integer | N |  |
| targetVersionId | integer | N |  |
| description | string | N |  |
| meshRole | string | N |  |
| svrType | string | N |  |
| tenantId | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "refId": 0,
    "stackId": 0,
    "stackInstanceId": 0,
    "serviceId": "serviceId",
    "packageId": 0,
    "appId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "name": "name",
    "code": "code",
    "dbClusterId": "dbClusterId",
    "dbUsername": "dbUsername",
    "dbPassword": "dbPassword",
    "dbName": [
    ],
    "sections": [
    ],
    "serviceType": "serviceType",
    "version": "version",
    "replicas": 0,
    "published": false,
    "status": "status",
    "runningStatus": "runningStatus",
    "brokerVpnId": 0,
    "releaseId": 0,
    "releaseCode": "releaseCode",
    "environmentId": 0,
    "suId": 0,
    "suInstanceId": 0,
    "serviceUuid": "serviceUuid",
    "currentVersionId": 0,
    "targetVersionId": 0,
    "description": "description",
    "meshRole": "meshRole",
    "svrType": "svrType",
    "tenantId": "tenantId",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **service - instance update - API**

## URL: /environment/v1/service/instance/update ##

## Method : Post ##

## Event ID : EnvSIUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| instanceId | integer |  | Y | gt=0,required |
| serviceInstanceCode | string |  | N | omitempty,max=64 |
| note | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "instanceId": 0,
    "serviceInstanceCode": "serviceInstanceCode",
    "note": "note"
}
```

**struct example:**

```
type UpdateServiceInstanceRequest struct {
    InstanceId uint64 `json:"instanceId" validate:"gt=0,required"`
    ServiceInstanceCode string `json:"serviceInstanceCode" validate:"omitempty,max=64"`
    Note string `json:"note" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **service - instance update-replicas - API**

## URL: /environment/v1/service/instance/update-replicas ##

## Method : Post ##

## Event ID : EnvSIUpdateReplicas ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type serviceInstanceIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| suInstanceName | string | N |  |
| suInstanceCode | string | N |  |
| suName | string | N |  |
| suCode | string | N |  |
| ServiceInstance | object | N |  |
| ServiceInstance.id | integer | N |  |
| ServiceInstance.refId | integer | N |  |
| ServiceInstance.stackId | integer | N |  |
| ServiceInstance.stackInstanceId | integer | N |  |
| ServiceInstance.serviceId | string | N |  |
| ServiceInstance.packageId | integer | N |  |
| ServiceInstance.appId | integer | N |  |
| ServiceInstance.suTypeId | integer | N |  |
| ServiceInstance.suTypeCode | string | N |  |
| ServiceInstance.name | string | N |  |
| ServiceInstance.code | string | N |  |
| ServiceInstance.dbClusterId | string | N |  |
| ServiceInstance.dbUsername | string | N |  |
| ServiceInstance.dbPassword | string | N |  |
| ServiceInstance.dbName | array[string] | N |  |
| ServiceInstance.sections | array[string] | N |  |
| ServiceInstance.serviceType | string | N |  |
| ServiceInstance.version | string | N |  |
| ServiceInstance.replicas | integer | N |  |
| ServiceInstance.published | boolean | N |  |
| ServiceInstance.status | string | N |  |
| ServiceInstance.runningStatus | string | N |  |
| ServiceInstance.brokerVpnId | integer | N |  |
| ServiceInstance.releaseId | integer | N |  |
| ServiceInstance.releaseCode | string | N |  |
| ServiceInstance.environmentId | integer | N |  |
| ServiceInstance.suId | integer | N |  |
| ServiceInstance.suInstanceId | integer | N |  |
| ServiceInstance.serviceUuid | string | N |  |
| ServiceInstance.currentVersionId | integer | N |  |
| ServiceInstance.targetVersionId | integer | N |  |
| ServiceInstance.description | string | N |  |
| ServiceInstance.meshRole | string | N |  |
| ServiceInstance.svrType | string | N |  |
| ServiceInstance.tenantId | string | N |  |
| ServiceInstance.updaterId | string | N |  |
| ServiceInstance.updater | string | N |  |
| ServiceInstance.creatorId | string | N |  |
| ServiceInstance.creator | string | N |  |
| ServiceInstance.createAt | string | N |  |
| ServiceInstance.updatedAt | string | N |  |
| ServiceInstance.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "suInstanceName": "suInstanceName",
    "suInstanceCode": "suInstanceCode",
    "suName": "suName",
    "suCode": "suCode",
    "ServiceInstance": {
        "id": 0,
        "refId": 0,
        "stackId": 0,
        "stackInstanceId": 0,
        "serviceId": "serviceId",
        "packageId": 0,
        "appId": 0,
        "suTypeId": 0,
        "suTypeCode": "suTypeCode",
        "name": "name",
        "code": "code",
        "dbClusterId": "dbClusterId",
        "dbUsername": "dbUsername",
        "dbPassword": "dbPassword",
        "dbName": [
        ],
        "sections": [
        ],
        "serviceType": "serviceType",
        "version": "version",
        "replicas": 0,
        "published": false,
        "status": "status",
        "runningStatus": "runningStatus",
        "brokerVpnId": 0,
        "releaseId": 0,
        "releaseCode": "releaseCode",
        "environmentId": 0,
        "suId": 0,
        "suInstanceId": 0,
        "serviceUuid": "serviceUuid",
        "currentVersionId": 0,
        "targetVersionId": 0,
        "description": "description",
        "meshRole": "meshRole",
        "svrType": "svrType",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **service - instance update-version - API**

## URL: /environment/v1/service/instance/update-version ##

## Method : Post ##

## Event ID : EnvSIUpdateVersion ##

**Description:**

set new version for service instance

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N | gt=0 |
| releaseCode | string |  | N | omitempty,name,max=32 |
| releaseId | integer |  | N | gte=0 |
| versionId | integer |  | N | gt=0 |

**Range of values**

request example:
```
{
    "id": 0,
    "releaseCode": "releaseCode",
    "releaseId": 0,
    "versionId": 0
}
```

**struct example:**

```
type UpdateVersionRequest struct {
    Id uint64 `json:"id" validate:"gt=0"`
    ReleaseCode string `json:"releaseCode" validate:"omitempty,name,max=32"`
    ReleaseId uint64 `json:"releaseId" validate:"gte=0"`
    VersionId uint64 `json:"versionId" validate:"gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


