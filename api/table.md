
# **table - create - API**

## URL: /environment/v1/table/create ##

## Method : Post ##

## Event ID : EnvTableInstanceCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| uint64 | integer |  | N |  |
| tables | array[uint64] |  | N |  |

**Range of values**

request example:
```
{
    "uint64": 0,
    "tables": [
    ]
}
```

**struct example:**

```
type CreateTableRequest struct {
    Uint64 uint64 `json:"uint64"`
    Tables []uint64 `json:"tables"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **table - detail - API**

## URL: /environment/v1/table/detail ##

## Method : Post ##

## Event ID : EnvTableInstanceDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type tableIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| suId | integer | N |  |
| databaseId | integer | N |  |
| refId | integer | N |  |
| currentVersionId | integer | N |  |
| rdbClusterId | integer | N |  |
| dbType | string | N |  |
| name | string | N |  |
| version | string | N |  |
| uuid | string | N |  |
| ip | string | N |  |
| port | integer | N |  |
| table | object | N |  |
| TableInstance.id | integer | N |  |
| TableInstance.tableRefId | integer | N |  |
| TableInstance.tableId | integer | N |  |
| TableInstance.dbInstanceId | integer | N |  |
| TableInstance.name | string | N |  |
| TableInstance.version | string | N |  |
| TableInstance.uuid | string | N |  |
| TableInstance.currentVersionId | integer | N |  |
| TableInstance.TargetVersionId | integer | N |  |
| TableInstance.status | string | N |  |
| TableInstance.description | string | N |  |
| TableInstance.tenantId | string | N |  |
| TableInstance.creatorId | string | N |  |
| TableInstance.creator | string | N |  |
| TableInstance.updaterId | string | N |  |
| TableInstance.updater | string | N |  |
| TableInstance.createAt | string | N |  |
| TableInstance.updatedAt | string | N |  |
| TableInstance.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "suId": 0,
    "databaseId": 0,
    "refId": 0,
    "currentVersionId": 0,
    "rdbClusterId": 0,
    "dbType": "dbType",
    "name": "name",
    "version": "version",
    "uuid": "uuid",
    "ip": "ip",
    "port": 0,
    "table": {
        "id": 0,
        "tableRefId": 0,
        "tableId": 0,
        "dbInstanceId": 0,
        "name": "name",
        "version": "version",
        "uuid": "uuid",
        "currentVersionId": 0,
        "TargetVersionId": 0,
        "status": "status",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **table - instance update-version - API**

## URL: /environment/v1/table/instance/update-version ##

## Method : Post ##

## Event ID : EnvTableInstanceUpdateVersion ##

**Description:**

set new version for service instance

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N | gt=0 |
| releaseCode | string |  | N | omitempty,name,max=32 |
| releaseId | integer |  | N | gte=0 |
| versionId | integer |  | N | gt=0 |

**Range of values**

request example:
```
{
    "id": 0,
    "releaseCode": "releaseCode",
    "releaseId": 0,
    "versionId": 0
}
```

**struct example:**

```
type UpdateVersionRequest struct {
    Id uint64 `json:"id" validate:"gt=0"`
    ReleaseCode string `json:"releaseCode" validate:"omitempty,name,max=32"`
    ReleaseId uint64 `json:"releaseId" validate:"gte=0"`
    VersionId uint64 `json:"versionId" validate:"gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **table - list - API**

## URL: /environment/v1/table/list ##

## Method : Post ##

## Event ID : EnvTableInstanceList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| suId | integer |  | N |  |
| tableRefId | integer |  | N |  |
| dbInstanceId | integer |  | N |  |
| dbId | integer |  | N |  |
| tableId | integer |  | N |  |
| tenantId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "suId": 0,
    "tableRefId": 0,
    "dbInstanceId": 0,
    "dbId": 0,
    "tableId": 0,
    "tenantId": "tenantId"
}
```

**struct example:**

```
type TableInstanceFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    SuId uint64 `json:"suId"`
    TableRefId uint64 `json:"tableRefId"`
    DbInstanceId uint64 `json:"dbInstanceId"`
    DbId uint64 `json:"dbId"`
    TableId uint64 `json:"tableId"`
    TenantId string `json:"tenantId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| tableRefId | integer | N |  |
| tableId | integer | N |  |
| dbInstanceId | integer | N |  |
| name | string | N |  |
| version | string | N |  |
| uuid | string | N |  |
| currentVersionId | integer | N |  |
| TargetVersionId | integer | N |  |
| status | string | N |  |
| description | string | N |  |
| tenantId | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "tableRefId": 0,
    "tableId": 0,
    "dbInstanceId": 0,
    "name": "name",
    "version": "version",
    "uuid": "uuid",
    "currentVersionId": 0,
    "TargetVersionId": 0,
    "status": "status",
    "description": "description",
    "tenantId": "tenantId",
    "creatorId": "creatorId",
    "creator": "creator",
    "updaterId": "updaterId",
    "updater": "updater",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **table - page - API**

## URL: /environment/v1/table/page ##

## Method : Post ##

## Event ID : EnvTableInstancePage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| suId | integer |  | N |  |
| tableRefId | integer |  | N |  |
| dbInstanceId | integer |  | N |  |
| dbId | integer |  | N |  |
| tableId | integer |  | N |  |
| tenantId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "suId": 0,
    "tableRefId": 0,
    "dbInstanceId": 0,
    "dbId": 0,
    "tableId": 0,
    "tenantId": "tenantId"
}
```

**struct example:**

```
type TableInstanceFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    SuId uint64 `json:"suId"`
    TableRefId uint64 `json:"tableRefId"`
    DbInstanceId uint64 `json:"dbInstanceId"`
    DbId uint64 `json:"dbId"`
    TableId uint64 `json:"tableId"`
    TenantId string `json:"tenantId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **table - patch - API**

## URL: /environment/v1/table/patch ##

## Method : Post ##

## Event ID : EnvTIVersionPatch ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| version | string |  | N | omitempty,max=64 |
| note | string |  | N | omitempty,max=255 |
| versions | array[uint64] |  | Y | gt=0,dive,required |
| patches | array[uint64] |  | Y | gt=0,dive,required |

**Range of values**

request example:
```
{
    "version": "version",
    "note": "note",
    "versions": [
    ],
    "patches": [
    ]
}
```

**struct example:**

```
type PatchTableInstanceRequest struct {
    Version string `json:"version" validate:"omitempty,max=64"`
    Note string `json:"note" validate:"omitempty,max=255"`
    Versions []uint64 `json:"versions" validate:"gt=0,dive,required"`
    Patches []uint64 `json:"patches" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **table - update - API**

## URL: /environment/v1/table/update ##

## Method : Post ##

## Event ID : EnvTableInstanceUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "id": 0,
    "description": "description"
}
```

**struct example:**

```
type UpdateTableInstanceRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **table - version delete - API**

## URL: /environment/v1/table/version/delete ##

## Method : Post ##

## Event ID : EnvTIVersionDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type tableInstanceVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| tableId | integer | N |  |
| dbInstanceId | integer | N |  |
| suId | integer | N |  |
| databaseId | integer | N |  |
| refId | integer | N |  |
| currentVersionId | integer | N |  |
| rdbClusterId | integer | N |  |
| planId | integer | N |  |
| dbType | string | N |  |
| name | string | N |  |
| version | string | N |  |
| dbUuid | string | N |  |
| tableUuid | string | N |  |
| suTypeCode | string | N |  |
| ip | string | N |  |
| port | integer | N |  |
| TableInstanceVersion | object | N |  |
| TableInstanceVersion.id | integer | N |  |
| TableInstanceVersion.name | string | N |  |
| TableInstanceVersion.tableInstanceId | integer | N |  |
| TableInstanceVersion.lastPatchId | integer | N |  |
| TableInstanceVersion.baseVersionId | integer | N |  |
| TableInstanceVersion.linkVersionId | integer | N |  |
| TableInstanceVersion.status | string | N |  |
| TableInstanceVersion.journal | string | N |  |
| TableInstanceVersion.data | map[string]interface | N |  |
| TableInstanceVersion.createSql | string | N |  |
| TableInstanceVersion.alterSql | string | N |  |
| TableInstanceVersion.rollbackSql | string | N |  |
| TableInstanceVersion.version | string | N |  |
| TableInstanceVersion.description | string | N |  |
| TableInstanceVersion.tenantId | string | N |  |
| TableInstanceVersion.creatorId | string | N |  |
| TableInstanceVersion.creator | string | N |  |
| TableInstanceVersion.updaterId | string | N |  |
| TableInstanceVersion.updater | string | N |  |
| TableInstanceVersion.createAt | string | N |  |
| TableInstanceVersion.updatedAt | string | N |  |
| TableInstanceVersion.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "tableId": 0,
    "dbInstanceId": 0,
    "suId": 0,
    "databaseId": 0,
    "refId": 0,
    "currentVersionId": 0,
    "rdbClusterId": 0,
    "planId": 0,
    "dbType": "dbType",
    "name": "name",
    "version": "version",
    "dbUuid": "dbUuid",
    "tableUuid": "tableUuid",
    "suTypeCode": "suTypeCode",
    "ip": "ip",
    "port": 0,
    "TableInstanceVersion": {
        "id": 0,
        "name": "name",
        "tableInstanceId": 0,
        "lastPatchId": 0,
        "baseVersionId": 0,
        "linkVersionId": 0,
        "status": "status",
        "journal": "journal",
        "data": {
        },
        "createSql": "createSql",
        "alterSql": "alterSql",
        "rollbackSql": "rollbackSql",
        "version": "version",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **table - version detail - API**

## URL: /environment/v1/table/version/detail ##

## Method : Post ##

## Event ID : EnvTIVersionDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type tableInstanceVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| tableId | integer | N |  |
| dbInstanceId | integer | N |  |
| suId | integer | N |  |
| databaseId | integer | N |  |
| refId | integer | N |  |
| currentVersionId | integer | N |  |
| rdbClusterId | integer | N |  |
| planId | integer | N |  |
| dbType | string | N |  |
| name | string | N |  |
| version | string | N |  |
| dbUuid | string | N |  |
| tableUuid | string | N |  |
| suTypeCode | string | N |  |
| ip | string | N |  |
| port | integer | N |  |
| TableInstanceVersion | object | N |  |
| TableInstanceVersion.id | integer | N |  |
| TableInstanceVersion.name | string | N |  |
| TableInstanceVersion.tableInstanceId | integer | N |  |
| TableInstanceVersion.lastPatchId | integer | N |  |
| TableInstanceVersion.baseVersionId | integer | N |  |
| TableInstanceVersion.linkVersionId | integer | N |  |
| TableInstanceVersion.status | string | N |  |
| TableInstanceVersion.journal | string | N |  |
| TableInstanceVersion.data | map[string]interface | N |  |
| TableInstanceVersion.createSql | string | N |  |
| TableInstanceVersion.alterSql | string | N |  |
| TableInstanceVersion.rollbackSql | string | N |  |
| TableInstanceVersion.version | string | N |  |
| TableInstanceVersion.description | string | N |  |
| TableInstanceVersion.tenantId | string | N |  |
| TableInstanceVersion.creatorId | string | N |  |
| TableInstanceVersion.creator | string | N |  |
| TableInstanceVersion.updaterId | string | N |  |
| TableInstanceVersion.updater | string | N |  |
| TableInstanceVersion.createAt | string | N |  |
| TableInstanceVersion.updatedAt | string | N |  |
| TableInstanceVersion.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "tableId": 0,
    "dbInstanceId": 0,
    "suId": 0,
    "databaseId": 0,
    "refId": 0,
    "currentVersionId": 0,
    "rdbClusterId": 0,
    "planId": 0,
    "dbType": "dbType",
    "name": "name",
    "version": "version",
    "dbUuid": "dbUuid",
    "tableUuid": "tableUuid",
    "suTypeCode": "suTypeCode",
    "ip": "ip",
    "port": 0,
    "TableInstanceVersion": {
        "id": 0,
        "name": "name",
        "tableInstanceId": 0,
        "lastPatchId": 0,
        "baseVersionId": 0,
        "linkVersionId": 0,
        "status": "status",
        "journal": "journal",
        "data": {
        },
        "createSql": "createSql",
        "alterSql": "alterSql",
        "rollbackSql": "rollbackSql",
        "version": "version",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **table - version fields - API**

## URL: /environment/v1/table/version/fields ##

## Method : Post ##

## Event ID : EnvTIVersionFields ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| instanceVersionId | integer |  | Y | required,gt=0 |
| sectionId | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "instanceVersionId": 0,
    "sectionId": 0
}
```

**struct example:**

```
type QueryTableInstanceFieldRequest struct {
    InstanceVersionId uint64 `json:"instanceVersionId" validate:"required,gt=0"`
    SectionId uint64 `json:"sectionId" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| requestId | string | N |  |
| fields | array[FieldInfo] | N |  |
| FieldInfo.domain | string | N |  |
| FieldInfo.group | string | N |  |
| FieldInfo.label | string | N |  |
| FieldInfo.columnNote | string | N |  |
| FieldInfo.placeholder | string | N |  |
| FieldInfo.unit | string | N |  |
| FieldInfo.necessary | boolean | N |  |
| FieldInfo.columnPath | string | N |  |
| FieldInfo.type | string | N |  |
| FieldInfo.dataType | string | N |  |
| FieldInfo.columnValue | string | N |  |
| FieldInfo.min | number | N |  |
| FieldInfo.max | number | N |  |
| FieldInfo.readOnly | boolean | N |  |
| FieldInfo.hidden | boolean | N |  |
| FieldInfo.validate | string | N |  |
| FieldInfo.items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |
| FieldInfo.Field | object | N |  |
| Field.id | integer | N |  |
| Field.stackId | integer | N |  |
| Field.sectionId | integer | N |  |
| Field.columnId | string | N |  |
| Field.scope | integer | N |  |
| Field.note | string | N |  |
| Field.priority | integer | N |  |
| Field.path | string | N |  |
| Field.value | string | N |  |
| Field.tenantId | string | N |  |
| Field.updaterId | string | N |  |
| Field.updater | string | N |  |
| Field.creatorId | string | N |  |
| Field.creator | string | N |  |
| Field.createAt | string | N |  |
| Field.updatedAt | string | N |  |
| Field.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "requestId": "requestId",
    "fields": [
        {
            "domain": "domain",
            "group": "group",
            "label": "label",
            "columnNote": "columnNote",
            "placeholder": "placeholder",
            "unit": "unit",
            "necessary": false,
            "columnPath": "columnPath",
            "type": "type",
            "dataType": "dataType",
            "columnValue": "columnValue",
            "min": "min",
            "max": "max",
            "readOnly": false,
            "hidden": false,
            "validate": "validate",
            "items": [
                {
                    "value": "value",
                    "text": "text"
                }
            ],
            "Field": {
                "id": 0,
                "stackId": 0,
                "sectionId": 0,
                "columnId": "columnId",
                "scope": 0,
                "note": "note",
                "priority": 0,
                "path": "path",
                "value": "value",
                "tenantId": "tenantId",
                "updaterId": "updaterId",
                "updater": "updater",
                "creatorId": "creatorId",
                "creator": "creator",
                "createAt": "createAt",
                "updatedAt": "updatedAt",
                "deletedAt": "deletedAt"
            }
        }
    ]
}
```



# **table - version list - API**

## URL: /environment/v1/table/version/list ##

## Method : Post ##

## Event ID : EnvTIVersionList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| idList | array[uint64] |  | N |  |
| tableInstanceId | integer |  | N |  |
| tenantId | string |  | N |  |
| suId | integer |  | N |  |
| databaseList | array[string] |  | N |  |
| suIdList | array[uint64] |  | N |  |
| rdbIDList | array[uint64] |  | N |  |
| tableUuidList | array[string] |  | N |  |
| databaseUuidList | array[string] |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "idList": [
    ],
    "tableInstanceId": 0,
    "tenantId": "tenantId",
    "suId": 0,
    "databaseList": [
    ],
    "suIdList": [
    ],
    "rdbIDList": [
    ],
    "tableUuidList": [
    ],
    "databaseUuidList": [
    ]
}
```

**struct example:**

```
type TableInstanceVersionFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    IdList []uint64 `json:"idList"`
    TableInstanceId uint64 `json:"tableInstanceId"`
    TenantId string `json:"tenantId"`
    SuId uint64 `json:"suId"`
    DatabaseList []string `json:"databaseList"`
    SuIdList []uint64 `json:"suIdList"`
    RdbIDList []uint64 `json:"rdbIDList"`
    TableUuidList []string `json:"tableUuidList"`
    DatabaseUuidList []string `json:"databaseUuidList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| tableId | integer | N |  |
| dbInstanceId | integer | N |  |
| suId | integer | N |  |
| databaseId | integer | N |  |
| refId | integer | N |  |
| currentVersionId | integer | N |  |
| rdbClusterId | integer | N |  |
| planId | integer | N |  |
| dbType | string | N |  |
| name | string | N |  |
| version | string | N |  |
| dbUuid | string | N |  |
| tableUuid | string | N |  |
| suTypeCode | string | N |  |
| ip | string | N |  |
| port | integer | N |  |
| TableInstanceVersion | object | N |  |
| TableInstanceVersion.id | integer | N |  |
| TableInstanceVersion.name | string | N |  |
| TableInstanceVersion.tableInstanceId | integer | N |  |
| TableInstanceVersion.lastPatchId | integer | N |  |
| TableInstanceVersion.baseVersionId | integer | N |  |
| TableInstanceVersion.linkVersionId | integer | N |  |
| TableInstanceVersion.status | string | N |  |
| TableInstanceVersion.journal | string | N |  |
| TableInstanceVersion.data | map[string]interface | N |  |
| TableInstanceVersion.createSql | string | N |  |
| TableInstanceVersion.alterSql | string | N |  |
| TableInstanceVersion.rollbackSql | string | N |  |
| TableInstanceVersion.version | string | N |  |
| TableInstanceVersion.description | string | N |  |
| TableInstanceVersion.tenantId | string | N |  |
| TableInstanceVersion.creatorId | string | N |  |
| TableInstanceVersion.creator | string | N |  |
| TableInstanceVersion.updaterId | string | N |  |
| TableInstanceVersion.updater | string | N |  |
| TableInstanceVersion.createAt | string | N |  |
| TableInstanceVersion.updatedAt | string | N |  |
| TableInstanceVersion.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "tableId": 0,
    "dbInstanceId": 0,
    "suId": 0,
    "databaseId": 0,
    "refId": 0,
    "currentVersionId": 0,
    "rdbClusterId": 0,
    "planId": 0,
    "dbType": "dbType",
    "name": "name",
    "version": "version",
    "dbUuid": "dbUuid",
    "tableUuid": "tableUuid",
    "suTypeCode": "suTypeCode",
    "ip": "ip",
    "port": 0,
    "TableInstanceVersion": {
        "id": 0,
        "name": "name",
        "tableInstanceId": 0,
        "lastPatchId": 0,
        "baseVersionId": 0,
        "linkVersionId": 0,
        "status": "status",
        "journal": "journal",
        "data": {
        },
        "createSql": "createSql",
        "alterSql": "alterSql",
        "rollbackSql": "rollbackSql",
        "version": "version",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **table - version page - API**

## URL: /environment/v1/table/version/page ##

## Method : Post ##

## Event ID : EnvTIVersionPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| idList | array[uint64] |  | N |  |
| tableInstanceId | integer |  | N |  |
| tenantId | string |  | N |  |
| suId | integer |  | N |  |
| databaseList | array[string] |  | N |  |
| suIdList | array[uint64] |  | N |  |
| rdbIDList | array[uint64] |  | N |  |
| tableUuidList | array[string] |  | N |  |
| databaseUuidList | array[string] |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "idList": [
    ],
    "tableInstanceId": 0,
    "tenantId": "tenantId",
    "suId": 0,
    "databaseList": [
    ],
    "suIdList": [
    ],
    "rdbIDList": [
    ],
    "tableUuidList": [
    ],
    "databaseUuidList": [
    ]
}
```

**struct example:**

```
type TableInstanceVersionFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    IdList []uint64 `json:"idList"`
    TableInstanceId uint64 `json:"tableInstanceId"`
    TenantId string `json:"tenantId"`
    SuId uint64 `json:"suId"`
    DatabaseList []string `json:"databaseList"`
    SuIdList []uint64 `json:"suIdList"`
    RdbIDList []uint64 `json:"rdbIDList"`
    TableUuidList []string `json:"tableUuidList"`
    DatabaseUuidList []string `json:"databaseUuidList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **table - version revises - API**

## URL: /environment/v1/table/version/revises ##

## Method : Post ##

## Event ID : EnvTIVersionRevises ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type tableInstanceVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| tableId | integer | N |  |
| dbInstanceId | integer | N |  |
| suId | integer | N |  |
| databaseId | integer | N |  |
| refId | integer | N |  |
| currentVersionId | integer | N |  |
| rdbClusterId | integer | N |  |
| planId | integer | N |  |
| dbType | string | N |  |
| name | string | N |  |
| version | string | N |  |
| dbUuid | string | N |  |
| tableUuid | string | N |  |
| suTypeCode | string | N |  |
| ip | string | N |  |
| port | integer | N |  |
| TableInstanceVersion | object | N |  |
| TableInstanceVersion.id | integer | N |  |
| TableInstanceVersion.name | string | N |  |
| TableInstanceVersion.tableInstanceId | integer | N |  |
| TableInstanceVersion.lastPatchId | integer | N |  |
| TableInstanceVersion.baseVersionId | integer | N |  |
| TableInstanceVersion.linkVersionId | integer | N |  |
| TableInstanceVersion.status | string | N |  |
| TableInstanceVersion.journal | string | N |  |
| TableInstanceVersion.data | map[string]interface | N |  |
| TableInstanceVersion.createSql | string | N |  |
| TableInstanceVersion.alterSql | string | N |  |
| TableInstanceVersion.rollbackSql | string | N |  |
| TableInstanceVersion.version | string | N |  |
| TableInstanceVersion.description | string | N |  |
| TableInstanceVersion.tenantId | string | N |  |
| TableInstanceVersion.creatorId | string | N |  |
| TableInstanceVersion.creator | string | N |  |
| TableInstanceVersion.updaterId | string | N |  |
| TableInstanceVersion.updater | string | N |  |
| TableInstanceVersion.createAt | string | N |  |
| TableInstanceVersion.updatedAt | string | N |  |
| TableInstanceVersion.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "tableId": 0,
    "dbInstanceId": 0,
    "suId": 0,
    "databaseId": 0,
    "refId": 0,
    "currentVersionId": 0,
    "rdbClusterId": 0,
    "planId": 0,
    "dbType": "dbType",
    "name": "name",
    "version": "version",
    "dbUuid": "dbUuid",
    "tableUuid": "tableUuid",
    "suTypeCode": "suTypeCode",
    "ip": "ip",
    "port": 0,
    "TableInstanceVersion": {
        "id": 0,
        "name": "name",
        "tableInstanceId": 0,
        "lastPatchId": 0,
        "baseVersionId": 0,
        "linkVersionId": 0,
        "status": "status",
        "journal": "journal",
        "data": {
        },
        "createSql": "createSql",
        "alterSql": "alterSql",
        "rollbackSql": "rollbackSql",
        "version": "version",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **table - version update - API**

## URL: /environment/v1/table/version/update ##

## Method : Post ##

## Event ID : EnvTIVersionUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |
| note | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "id": 0,
    "note": "note"
}
```

**struct example:**

```
type UpdateTableInstanceVersionRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
    Note string `json:"note" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


