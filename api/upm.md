
# **upm - cluster deploy - API**

## URL: /environment/v1/upm/cluster/deploy ##

## Method : Post ##

## Event ID : EnvUpmDeploy ##

**Description:**

create upm cluster, create plan and deploy task

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| name | string |  | Y | required |
| version | string |  | Y | required |
| description | string |  | N |  |
| aigClusterId | integer |  | Y | gt=0,required |
| aigSignKey | string |  | Y | gt=0,required |
| aigSessionName | string |  | Y | gt=0,required |
| skmClusterId | integer |  | N | gt=0 |
| environmentId | integer |  | Y | gt=0,required |
| suInstanceId | integer |  | Y | gt=0,required |
| k8sClusterId | integer |  | N | gt=0 |
| rdbClusterId | integer |  | N | gt=0 |
| kvcClusterId | integer |  | N | gt=0 |
| vpnId | integer |  | N | gt=0 |
| meshId | integer |  | N | gt=0 |
| batchId | integer |  | N | omitempty,gte=0 |
| stackId | integer |  | N | omitempty,gte=0 |
| podReplicas | integer |  | N | gt=0 |
| logLevel | string |  | N | omitempty,oneof=error info debug |
| appLimitsCpu | string |  | N | omitempty,numeric |
| appRequestsCpu | string |  | N | omitempty,numeric |
| appLimitsMemory | string |  | N | omitempty,numeric |
| appRequestsMemory | string |  | N | omitempty,numeric |
| nodeSelectorKey | string |  | N | omitempty,oneof=az node hag |
| nodeSelectorValue | string |  | N | omitempty,name,max=16 |
| UpmConfig | object |  | N |  |
| UpmConfig.multiTenant | boolean |  | N |  |
| UpmConfig.defaultAuthenticationType | string |  | N |  |
| UpmConfig.defaultEnableGoogleAuth | boolean |  | N |  |
| UpmConfig.minPasswordLength | integer |  | N |  |
| UpmConfig.maxPasswordLength | integer |  | N |  |
| UpmConfig.passwordHistoryReqeatLimit | integer |  | N |  |
| UpmConfig.requirePeriodicPasswordChange | boolean |  | N |  |
| UpmConfig.minPeriodicPasswordChangeDays | integer |  | N |  |
| UpmConfig.enableAlertBeforePasswordExpiration | boolean |  | N |  |
| UpmConfig.alertDaysBeforePasswordExpiration | integer |  | N |  |
| UpmConfig.numOfFailedLoginAttempts | integer |  | N |  |
| UpmConfig.requireUniqueEmailAddr | boolean |  | N |  |
| UpmConfig.automateUnlock | boolean |  | N |  |
| UpmConfig.automateUnlockTime | integer |  | N |  |
| UpmConfig.automateDeactivateAccount | boolean |  | N |  |
| UpmConfig.automateDeactivateDays | integer |  | N |  |
| UpmConfig.enableChangeUsername | boolean |  | N |  |
| UpmConfig.enableChangeEmail | boolean |  | N |  |
| UpmConfig.linkExpiryTime | integer |  | N |  |
| UpmConfig.codeExpiryTime | integer |  | N |  |
| UpmConfig.enableEnforceLogout | boolean |  | N |  |
| UpmConfig.tokenExpiryTime | integer |  | N |  |
| UpmConfig.enableAddGroupIntoToken | boolean |  | N |  |
| UpmConfig.enableAddRoleIntoToken | boolean |  | N |  |
| UpmConfig.enableAddAllDataPermission | boolean |  | N |  |
| UpmConfig.SamlConfiguration | object |  | N |  |
| SamlConfiguration.samlIdpMetadataUrl | string |  | N |  |
| SamlConfiguration.samlSpRootUrl | string |  | N |  |
| SamlConfiguration.samlWantSignRequest | boolean |  | N |  |
| SamlConfiguration.samlSpKeyPairkey | string |  | N |  |
| SamlConfiguration.samlSpKeyPairCert | string |  | N |  |
| UpmConfig.LDAPConfiguration | object |  | N |  |
| LDAPConfiguration.ldapAddr | string |  | N |  |
| LDAPConfiguration.ldapBaseDN | string |  | N |  |
| LDAPConfiguration.ldapUsername | string |  | N |  |
| LDAPConfiguration.ldapPassword | string |  | N |  |
| LDAPConfiguration.userAttributes | string |  | N |  |
| LDAPConfiguration.searchFilter | string |  | N |  |

**Range of values**

request example:
```
{
    "name": "name",
    "version": "version",
    "description": "description",
    "aigClusterId": 0,
    "aigSignKey": "aigSignKey",
    "aigSessionName": "aigSessionName",
    "skmClusterId": 0,
    "environmentId": 0,
    "suInstanceId": 0,
    "k8sClusterId": 0,
    "rdbClusterId": 0,
    "kvcClusterId": 0,
    "vpnId": 0,
    "meshId": 0,
    "batchId": 0,
    "stackId": 0,
    "podReplicas": 0,
    "logLevel": "logLevel",
    "appLimitsCpu": "appLimitsCpu",
    "appRequestsCpu": "appRequestsCpu",
    "appLimitsMemory": "appLimitsMemory",
    "appRequestsMemory": "appRequestsMemory",
    "nodeSelectorKey": "nodeSelectorKey",
    "nodeSelectorValue": "nodeSelectorValue",
    "UpmConfig": {
        "multiTenant": false,
        "defaultAuthenticationType": "defaultAuthenticationType",
        "defaultEnableGoogleAuth": false,
        "minPasswordLength": 0,
        "maxPasswordLength": 0,
        "passwordHistoryReqeatLimit": 0,
        "requirePeriodicPasswordChange": false,
        "minPeriodicPasswordChangeDays": 0,
        "enableAlertBeforePasswordExpiration": false,
        "alertDaysBeforePasswordExpiration": 0,
        "numOfFailedLoginAttempts": 0,
        "requireUniqueEmailAddr": false,
        "automateUnlock": false,
        "automateUnlockTime": 0,
        "automateDeactivateAccount": false,
        "automateDeactivateDays": 0,
        "enableChangeUsername": false,
        "enableChangeEmail": false,
        "linkExpiryTime": 0,
        "codeExpiryTime": 0,
        "enableEnforceLogout": false,
        "tokenExpiryTime": 0,
        "enableAddGroupIntoToken": false,
        "enableAddRoleIntoToken": false,
        "enableAddAllDataPermission": false,
        "SamlConfiguration": {
            "samlIdpMetadataUrl": "samlIdpMetadataUrl",
            "samlSpRootUrl": "samlSpRootUrl",
            "samlWantSignRequest": false,
            "samlSpKeyPairkey": "samlSpKeyPairkey",
            "samlSpKeyPairCert": "samlSpKeyPairCert"
        },
        "LDAPConfiguration": {
            "ldapAddr": "ldapAddr",
            "ldapBaseDN": "ldapBaseDN",
            "ldapUsername": "ldapUsername",
            "ldapPassword": "ldapPassword",
            "userAttributes": "userAttributes",
            "searchFilter": "searchFilter"
        }
    }
}
```

**struct example:**

```
type DeployRequest struct {
    Name string `json:"name" validate:"required"`
    Version string `json:"version" validate:"required"`
    Description string `json:"description"`
    AigClusterId uint64 `json:"aigClusterId" validate:"gt=0,required"`
    AigSignKey string `json:"aigSignKey" validate:"gt=0,required"`
    AigSessionName string `json:"aigSessionName" validate:"gt=0,required"`
    SkmClusterId uint64 `json:"skmClusterId" validate:"gt=0"`
    EnvironmentId uint64 `json:"environmentId" validate:"gt=0,required"`
    SuInstanceId uint64 `json:"suInstanceId" validate:"gt=0,required"`
    K8sClusterId uint64 `json:"k8sClusterId" validate:"gt=0"`
    RdbClusterId uint64 `json:"rdbClusterId" validate:"gt=0"`
    KvcClusterId uint64 `json:"kvcClusterId" validate:"gt=0"`
    VpnId uint64 `json:"vpnId" validate:"gt=0"`
    MeshId uint64 `json:"meshId" validate:"gt=0"`
    BatchId uint64 `json:"batchId" validate:"omitempty,gte=0"`
    StackId uint64 `json:"stackId" validate:"omitempty,gte=0"`
    PodReplicas uint64 `json:"podReplicas" validate:"gt=0"`
    LogLevel string `json:"logLevel" validate:"omitempty,oneof=error info debug"`
    AppLimitsCpu string `json:"appLimitsCpu" validate:"omitempty,numeric"`
    AppRequestsCpu string `json:"appRequestsCpu" validate:"omitempty,numeric"`
    AppLimitsMemory string `json:"appLimitsMemory" validate:"omitempty,numeric"`
    AppRequestsMemory string `json:"appRequestsMemory" validate:"omitempty,numeric"`
    NodeSelectorKey string `json:"nodeSelectorKey" validate:"omitempty,oneof=az node hag"`
    NodeSelectorValue string `json:"nodeSelectorValue" validate:"omitempty,name,max=16"`
    UpmConfig struct {
        MultiTenant bool `json:"multiTenant"`
        DefaultAuthenticationType string `json:"defaultAuthenticationType"`
        DefaultEnableGoogleAuth bool `json:"defaultEnableGoogleAuth"`
        MinPasswordLength int `json:"minPasswordLength"`
        MaxPasswordLength int `json:"maxPasswordLength"`
        PasswordHistoryReqeatLimit int `json:"passwordHistoryReqeatLimit"`
        RequirePeriodicPasswordChange bool `json:"requirePeriodicPasswordChange"`
        MinPeriodicPasswordChangeDays int `json:"minPeriodicPasswordChangeDays"`
        EnableAlertBeforePasswordExpiration bool `json:"enableAlertBeforePasswordExpiration"`
        AlertDaysBeforePasswordExpiration int `json:"alertDaysBeforePasswordExpiration"`
        NumOfFailedLoginAttempts int `json:"numOfFailedLoginAttempts"`
        RequireUniqueEmailAddr bool `json:"requireUniqueEmailAddr"`
        AutomateUnlock bool `json:"automateUnlock"`
        AutomateUnlockTime int `json:"automateUnlockTime"`
        AutomateDeactivateAccount bool `json:"automateDeactivateAccount"`
        AutomateDeactivateDays int `json:"automateDeactivateDays"`
        EnableChangeUsername bool `json:"enableChangeUsername"`
        EnableChangeEmail bool `json:"enableChangeEmail"`
        LinkExpiryTime int `json:"linkExpiryTime"`
        CodeExpiryTime int `json:"codeExpiryTime"`
        EnableEnforceLogout bool `json:"enableEnforceLogout"`
        TokenExpiryTime int `json:"tokenExpiryTime"`
        EnableAddGroupIntoToken bool `json:"enableAddGroupIntoToken"`
        EnableAddRoleIntoToken bool `json:"enableAddRoleIntoToken"`
        EnableAddAllDataPermission bool `json:"enableAddAllDataPermission"`
        SamlConfiguration struct {
            SamlIdpMetadataUrl string `json:"samlIdpMetadataUrl"`
            SamlSpRootUrl string `json:"samlSpRootUrl"`
            SamlWantSignRequest bool `json:"samlWantSignRequest"`
            SamlSpKeyPairkey string `json:"samlSpKeyPairkey"`
            SamlSpKeyPairCert string `json:"samlSpKeyPairCert"`
        }
        LDAPConfiguration struct {
            LdapAddr string `json:"ldapAddr"`
            LdapBaseDN string `json:"ldapBaseDN"`
            LdapUsername string `json:"ldapUsername"`
            LdapPassword string `json:"ldapPassword"`
            UserAttributes string `json:"userAttributes"`
            SearchFilter string `json:"searchFilter"`
        }
    }
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **upm - cluster detail - API**

## URL: /environment/v1/upm/cluster/detail ##

## Method : Post ##

## Event ID : EnvUpmDetail ##

**Description:**

query upm cluster detail with upm services

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type ClusterDetailRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| code | string | N |  |
| name | string | N |  |
| description | string | N |  |
| version | string | N |  |
| instanceNum | string | N |  |
| cpuRequest | string | N |  |
| cpuLimit | string | N |  |
| memoryRequest | string | N |  |
| memoryLimit | string | N |  |
| logLevel | string | N |  |
| environment | object | N |  |
| Environment.id | integer | N |  |
| Environment.workspaceId | string | N |  |
| Environment.resourceGroupId | integer | N |  |
| Environment.resourceGroupName | string | N |  |
| Environment.name | string | N |  |
| Environment.code | string | N |  |
| Environment.phase | string | N |  |
| Environment.preset | string | N |  |
| Environment.serverless | string | N |  |
| Environment.wsGitPath | string | N |  |
| Environment.repoId | integer | N |  |
| Environment.repodbId | integer | N |  |
| Environment.templateGroupId | integer | N |  |
| Environment.configProjID | integer | N |  |
| Environment.upmDataId | integer | N |  |
| Environment.description | string | N |  |
| Environment.status | string | N |  |
| Environment.tenantId | string | N |  |
| Environment.updaterId | string | N |  |
| Environment.updater | string | N |  |
| Environment.creatorId | string | N |  |
| Environment.creator | string | N |  |
| Environment.createAt | string | N |  |
| Environment.updatedAt | string | N |  |
| Environment.deletedAt | string | N |  |
| suInstanceInfo | object | N |  |
| SuInstanceInfo.workspaceId | string | N |  |
| SuInstanceInfo.wsGitPath | string | N |  |
| SuInstanceInfo.resourceGroupId | integer | N |  |
| SuInstanceInfo.resourceGroupName | string | N |  |
| SuInstanceInfo.repoId | integer | N |  |
| SuInstanceInfo.repodbId | integer | N |  |
| SuInstanceInfo.environmentName | string | N |  |
| SuInstanceInfo.environmentCode | string | N |  |
| SuInstanceInfo.phase | string | N |  |
| SuInstanceInfo.stackId | integer | N |  |
| SuInstanceInfo.envId | integer | N |  |
| SuInstanceInfo.networkZoneID | integer | N |  |
| SuInstanceInfo.suTypeId | integer | N |  |
| SuInstanceInfo.suTypeCode | string | N |  |
| SuInstanceInfo.suName | string | N |  |
| SuInstanceInfo.suCode | string | N |  |
| SuInstanceInfo.suWeight | uint | N |  |
| SuInstanceInfo.suQuota | integer | N |  |
| SuInstanceInfo.objRelations | map[string]uint64 | N |  |
| SuInstanceInfo.networkZone | object | N |  |
| NetworkZoneInfo.id | integer | N |  |
| NetworkZoneInfo.taskId | integer | N |  |
| NetworkZoneInfo.specId | integer | N |  |
| NetworkZoneInfo.name | string | N |  |
| NetworkZoneInfo.code | string | N |  |
| NetworkZoneInfo.tenantRegionId | integer | N |  |
| NetworkZoneInfo.tenantVpcId | integer | N |  |
| NetworkZoneInfo.resourceGroupId | integer | N |  |
| NetworkZoneInfo.azId | integer | N |  |
| NetworkZoneInfo.serialNumber | integer | N |  |
| NetworkZoneInfo.tenantId | string | N |  |
| NetworkZoneInfo.tenantCode | string | N |  |
| NetworkZoneInfo.status | string | N |  |
| NetworkZoneInfo.description | string | N |  |
| SuInstanceInfo.SuInstance | object | N |  |
| SuInstance.id | integer | N |  |
| SuInstance.envId | integer | N |  |
| SuInstance.suId | integer | N |  |
| SuInstance.name | string | N |  |
| SuInstance.code | string | N |  |
| SuInstance.description | string | N |  |
| SuInstance.tenantId | string | N |  |
| SuInstance.creatorId | string | N |  |
| SuInstance.creator | string | N |  |
| SuInstance.createAt | string | N |  |
| SuInstance.updatedAt | string | N |  |
| SuInstance.deletedAt | string | N |  |
| services | array[Service] | N |  |
| Service.id | integer | N |  |
| Service.name | string | N |  |
| Service.clusterId | integer | N |  |
| Service.type | string | N |  |
| Service.serviceInstanceId | integer | N |  |
| Service.status | string | N |  |
| Service.runningStatus | string | N |  |
| Service.version | string | N |  |
| Service.envId | integer | N |  |
| Service.suInstanceId | integer | N |  |
| Service.instanceNum | string | N |  |
| Service.cpuRequest | string | N |  |
| Service.cpuLimit | string | N |  |
| Service.memoryRequest | string | N |  |
| Service.memoryLimit | string | N |  |
| Service.logLevel | string | N |  |
| Service.creator | string | N |  |
| Service.updater | string | N |  |
| Service.tenantId | string | N |  |
| Service.createAt | string | N |  |
| Service.updatedAt | string | N |  |
| Service.deletedAt | string | N |  |
| resource | object | N |  |
| SuInstanceResource.networkZone | object | N |  |
| SuInstanceResource.k8sCluster | object | N |  |
| ResourceK8sCluster.id | integer | N |  |
| ResourceK8sCluster.agentId | integer | N |  |
| ResourceK8sCluster.clusterCidr | string | N |  |
| ResourceK8sCluster.clusterCode | string | N |  |
| ResourceK8sCluster.clusterName | string | N |  |
| ResourceK8sCluster.harbor | string | N |  |
| ResourceK8sCluster.harborId | integer | N |  |
| ResourceK8sCluster.k8sConfigId | integer | N |  |
| ResourceK8sCluster.network | string | N |  |
| ResourceK8sCluster.networkZone | string | N |  |
| ResourceK8sCluster.nodePortRange | string | N |  |
| ResourceK8sCluster.pemId | integer | N |  |
| ResourceK8sCluster.regionId | integer | N |  |
| ResourceK8sCluster.resourceGroup | string | N |  |
| ResourceK8sCluster.securityGroup | string | N |  |
| ResourceK8sCluster.sepcId | integer | N |  |
| ResourceK8sCluster.serviceCidr | string | N |  |
| ResourceK8sCluster.status | string | N |  |
| ResourceK8sCluster.suInstance | string | N |  |
| ResourceK8sCluster.taskId | integer | N |  |
| ResourceK8sCluster.tenantCode | string | N |  |
| ResourceK8sCluster.tenantId | string | N |  |
| ResourceK8sCluster.tenantRegion | string | N |  |
| ResourceK8sCluster.type | string | N |  |
| ResourceK8sCluster.K8SNode | array[ResourceK8SNode] | N |  |
| ResourceK8SNode.id | integer | N |  |
| ResourceK8SNode.templateId | integer | N |  |
| ResourceK8SNode.templateName | string | N |  |
| ResourceK8SNode.dataVolumeSize | integer | N |  |
| ResourceK8SNode.regionName | string | N |  |
| ResourceK8SNode.regionCode | string | N |  |
| ResourceK8SNode.tenantAzName | string | N |  |
| ResourceK8SNode.azName | string | N |  |
| ResourceK8SNode.azCode | string | N |  |
| ResourceK8SNode.memory | integer | N |  |
| ResourceK8SNode.cpu | integer | N |  |
| ResourceK8SNode.rootVolumeSize | integer | N |  |
| ResourceK8SNode.disk | integer | N |  |
| ResourceK8SNode.taskId | integer | N |  |
| ResourceK8SNode.sepcId | integer | N |  |
| ResourceK8SNode.orderNumber | integer | N |  |
| ResourceK8SNode.clusterId | integer | N |  |
| ResourceK8SNode.clusterCode | string | N |  |
| ResourceK8SNode.region | string | N |  |
| ResourceK8SNode.resourceGroup | string | N |  |
| ResourceK8SNode.networkZone | string | N |  |
| ResourceK8SNode.availabilityZone | string | N |  |
| ResourceK8SNode.ip | string | N |  |
| ResourceK8SNode.status | string | N |  |
| ResourceK8SNode.role | string | N |  |
| ResourceK8SNode.drain | string | N |  |
| ResourceK8SNode.schedule | string | N |  |
| ResourceK8SNode.vmId | integer | N |  |
| ResourceK8SNode.tenantId | string | N |  |
| SuInstanceResource.cacheCluster | object | N |  |
| ResourceCacheCluster.id | integer | N |  |
| ResourceCacheCluster.clusterCode | string | N |  |
| ResourceCacheCluster.clusterName | string | N |  |
| ResourceCacheCluster.harborId | integer | N |  |
| ResourceCacheCluster.networkZone | string | N |  |
| ResourceCacheCluster.occupied | string | N |  |
| ResourceCacheCluster.password | string | N |  |
| ResourceCacheCluster.pemId | integer | N |  |
| ResourceCacheCluster.property | string | N |  |
| ResourceCacheCluster.regionId | integer | N |  |
| ResourceCacheCluster.resourceGroup | string | N |  |
| ResourceCacheCluster.sepcId | integer | N |  |
| ResourceCacheCluster.status | string | N |  |
| ResourceCacheCluster.taskId | integer | N |  |
| ResourceCacheCluster.tenantCode | string | N |  |
| ResourceCacheCluster.tenantId | string | N |  |
| ResourceCacheCluster.tenantRegion | string | N |  |
| ResourceCacheCluster.type | string | N |  |
| ResourceCacheCluster.CacheNode | array[ResourceCacheNode] | N |  |
| ResourceCacheNode.memory | integer | N |  |
| ResourceCacheNode.cpu | integer | N |  |
| ResourceCacheNode.templateId | integer | N |  |
| ResourceCacheNode.templateName | string | N |  |
| ResourceCacheNode.dataVolumeSize | integer | N |  |
| ResourceCacheNode.regionName | string | N |  |
| ResourceCacheNode.regionCode | string | N |  |
| ResourceCacheNode.tenantAzCode | string | N |  |
| ResourceCacheNode.tenantAzName | string | N |  |
| ResourceCacheNode.azName | string | N |  |
| ResourceCacheNode.azCode | string | N |  |
| ResourceCacheNode.id | integer | N |  |
| ResourceCacheNode.taskId | integer | N |  |
| ResourceCacheNode.specId | integer | N |  |
| ResourceCacheNode.clusterId | integer | N |  |
| ResourceCacheNode.ip | string | N |  |
| ResourceCacheNode.status | string | N |  |
| ResourceCacheNode.port | string | N |  |
| ResourceCacheNode.role | string | N |  |
| ResourceCacheNode.master_ip | string | N |  |
| ResourceCacheNode.master_port | string | N |  |
| ResourceCacheNode.vmId | integer | N |  |
| ResourceCacheNode.resourceGroup | string | N |  |
| ResourceCacheNode.networkZone | string | N |  |
| ResourceCacheNode.tenantId | string | N |  |
| SuInstanceResource.cacheClusterList | array[ResourceCacheCluster] | N |  |
| SuInstanceResource.pemVpn | object | N |  |
| VpnInfo.mesh | object | N |  |
| MeshSpec.agentId | integer | N |  |
| MeshSpec.code | string | N |  |
| MeshSpec.description | string | N |  |
| MeshSpec.harborId | integer | N |  |
| MeshSpec.id | integer | N |  |
| MeshSpec.maxConnection | integer | N |  |
| MeshSpec.name | string | N |  |
| MeshSpec.networkZone | string | N |  |
| MeshSpec.occupied | string | N |  |
| MeshSpec.password | string | N |  |
| MeshSpec.property | string | N |  |
| MeshSpec.resourceGroup | string | N |  |
| MeshSpec.role | string | N |  |
| MeshSpec.siteId | integer | N |  |
| MeshSpec.specId | integer | N |  |
| MeshSpec.status | string | N |  |
| MeshSpec.taskId | integer | N |  |
| MeshSpec.tenantId | string | N |  |
| MeshSpec.type | string | N |  |
| MeshSpec.username | string | N |  |
| MeshSpec.vmTemplateId | integer | N |  |
| MeshSpec.meshNode | array[MeshNodeInfo] | N |  |
| MeshNodeInfo.templateId | integer | N |  |
| MeshNodeInfo.templateName | string | N |  |
| MeshNodeInfo.dataVolumeSize | integer | N |  |
| MeshNodeInfo.regionName | string | N |  |
| MeshNodeInfo.regionCode | string | N |  |
| MeshNodeInfo.tenantAzName | string | N |  |
| MeshNodeInfo.tenantAzCode | string | N |  |
| MeshNodeInfo.azName | string | N |  |
| MeshNodeInfo.azCode | string | N |  |
| MeshNodeInfo.id | integer | N |  |
| MeshNodeInfo.taskId | integer | N |  |
| MeshNodeInfo.specId | integer | N |  |
| MeshNodeInfo.meshId | integer | N |  |
| MeshNodeInfo.meshCode | string | N |  |
| MeshNodeInfo.ip | string | N |  |
| MeshNodeInfo.role | string | N |  |
| MeshNodeInfo.adminPort | integer | N |  |
| MeshNodeInfo.workerPort | integer | N |  |
| MeshNodeInfo.tenantId | string | N |  |
| MeshNodeInfo.resourceGroup | string | N |  |
| MeshNodeInfo.vmId | integer | N |  |
| MeshNodeInfo.status | string | N |  |
| MeshNodeInfo.routerName | string | N |  |
| MeshNodeInfo.description | string | N |  |
| VpnInfo.vpn | object | N |  |
| SuInstanceResource.rdbGroup | object | N |  |
| RDBGroupInfo.rdbCluster | object | N |  |
| ResourceRdbCluster.id | integer | N |  |
| ResourceRdbCluster.clusterId | string | N |  |
| ResourceRdbCluster.clusterName | string | N |  |
| ResourceRdbCluster.clusterType | string | N |  |
| ResourceRdbCluster.networkZone | string | N |  |
| ResourceRdbCluster.occupied | string | N |  |
| ResourceRdbCluster.property | string | N |  |
| ResourceRdbCluster.region | string | N |  |
| ResourceRdbCluster.resourceGroup | string | N |  |
| ResourceRdbCluster.specId | integer | N |  |
| ResourceRdbCluster.status | string | N |  |
| ResourceRdbCluster.taskId | integer | N |  |
| ResourceRdbCluster.tenantId | string | N |  |
| ResourceRdbCluster.updater | string | N |  |
| ResourceRdbCluster.updaterId | string | N |  |
| ResourceRdbCluster.version | string | N |  |
| ResourceRdbCluster.vmTemplateId | integer | N |  |
| ResourceRdbCluster.RDBGroup | array[ResourceRdbGroup] | N |  |
| ResourceRdbGroup.id | integer | N |  |
| ResourceRdbGroup.taskId | integer | N |  |
| ResourceRdbGroup.sepcId | integer | N |  |
| ResourceRdbGroup.region | string | N |  |
| ResourceRdbGroup.resourceGroup | string | N |  |
| ResourceRdbGroup.networkZone | string | N |  |
| ResourceRdbGroup.rdbGroupCode | string | N |  |
| ResourceRdbGroup.clusterId | integer | N |  |
| ResourceRdbGroup.clusterCode | string | N |  |
| ResourceRdbGroup.harborId | integer | N |  |
| ResourceRdbGroup.pemId | integer | N |  |
| ResourceRdbGroup.vip | string | N |  |
| ResourceRdbGroup.az | string | N |  |
| ResourceRdbGroup.vport | integer | N |  |
| ResourceRdbGroup.type | string | N |  |
| ResourceRdbGroup.purpose | string | N |  |
| ResourceRdbGroup.name | string | N |  |
| ResourceRdbGroup.description | string | N |  |
| RDBGroupInfo.rdbGroup | object | N |  |
| SuInstanceResource.aemVpnList | array[VpnInfo] | N |  |
| config | object | N |  |
| UpmConfig.multiTenant | boolean | N |  |
| UpmConfig.defaultAuthenticationType | string | N |  |
| UpmConfig.defaultEnableGoogleAuth | boolean | N |  |
| UpmConfig.minPasswordLength | integer | N |  |
| UpmConfig.maxPasswordLength | integer | N |  |
| UpmConfig.passwordHistoryReqeatLimit | integer | N |  |
| UpmConfig.requirePeriodicPasswordChange | boolean | N |  |
| UpmConfig.minPeriodicPasswordChangeDays | integer | N |  |
| UpmConfig.enableAlertBeforePasswordExpiration | boolean | N |  |
| UpmConfig.alertDaysBeforePasswordExpiration | integer | N |  |
| UpmConfig.numOfFailedLoginAttempts | integer | N |  |
| UpmConfig.requireUniqueEmailAddr | boolean | N |  |
| UpmConfig.automateUnlock | boolean | N |  |
| UpmConfig.automateUnlockTime | integer | N |  |
| UpmConfig.automateDeactivateAccount | boolean | N |  |
| UpmConfig.automateDeactivateDays | integer | N |  |
| UpmConfig.enableChangeUsername | boolean | N |  |
| UpmConfig.enableChangeEmail | boolean | N |  |
| UpmConfig.linkExpiryTime | integer | N |  |
| UpmConfig.codeExpiryTime | integer | N |  |
| UpmConfig.enableEnforceLogout | boolean | N |  |
| UpmConfig.tokenExpiryTime | integer | N |  |
| UpmConfig.enableAddGroupIntoToken | boolean | N |  |
| UpmConfig.enableAddRoleIntoToken | boolean | N |  |
| UpmConfig.enableAddAllDataPermission | boolean | N |  |
| UpmConfig.SamlConfiguration | object | N |  |
| SamlConfiguration.samlIdpMetadataUrl | string | N |  |
| SamlConfiguration.samlSpRootUrl | string | N |  |
| SamlConfiguration.samlWantSignRequest | boolean | N |  |
| SamlConfiguration.samlSpKeyPairkey | string | N |  |
| SamlConfiguration.samlSpKeyPairCert | string | N |  |
| UpmConfig.LDAPConfiguration | object | N |  |
| LDAPConfiguration.ldapAddr | string | N |  |
| LDAPConfiguration.ldapBaseDN | string | N |  |
| LDAPConfiguration.ldapUsername | string | N |  |
| LDAPConfiguration.ldapPassword | string | N |  |
| LDAPConfiguration.userAttributes | string | N |  |
| LDAPConfiguration.searchFilter | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "code": "code",
    "name": "name",
    "description": "description",
    "version": "version",
    "instanceNum": "instanceNum",
    "cpuRequest": "cpuRequest",
    "cpuLimit": "cpuLimit",
    "memoryRequest": "memoryRequest",
    "memoryLimit": "memoryLimit",
    "logLevel": "logLevel",
    "environment": {
        "id": 0,
        "workspaceId": "workspaceId",
        "resourceGroupId": 0,
        "resourceGroupName": "resourceGroupName",
        "name": "name",
        "code": "code",
        "phase": "phase",
        "preset": "preset",
        "serverless": "serverless",
        "wsGitPath": "wsGitPath",
        "repoId": 0,
        "repodbId": 0,
        "templateGroupId": 0,
        "configProjID": 0,
        "upmDataId": 0,
        "description": "description",
        "status": "status",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    },
    "suInstanceInfo": {
        "workspaceId": "workspaceId",
        "wsGitPath": "wsGitPath",
        "resourceGroupId": 0,
        "resourceGroupName": "resourceGroupName",
        "repoId": 0,
        "repodbId": 0,
        "environmentName": "environmentName",
        "environmentCode": "environmentCode",
        "phase": "phase",
        "stackId": 0,
        "envId": 0,
        "networkZoneID": 0,
        "suTypeId": 0,
        "suTypeCode": "suTypeCode",
        "suName": "suName",
        "suCode": "suCode",
        "suWeight": "suWeight",
        "suQuota": 0,
        "objRelations": {
        },
        "networkZone": {
            "id": 0,
            "taskId": 0,
            "specId": 0,
            "name": "name",
            "code": "code",
            "tenantRegionId": 0,
            "tenantVpcId": 0,
            "resourceGroupId": 0,
            "azId": 0,
            "serialNumber": 0,
            "tenantId": "tenantId",
            "tenantCode": "tenantCode",
            "status": "status",
            "description": "description"
        },
        "SuInstance": {
            "id": 0,
            "envId": 0,
            "suId": 0,
            "name": "name",
            "code": "code",
            "description": "description",
            "tenantId": "tenantId",
            "creatorId": "creatorId",
            "creator": "creator",
            "createAt": "createAt",
            "updatedAt": "updatedAt",
            "deletedAt": "deletedAt"
        }
    },
    "services": [
        {
            "id": 0,
            "name": "name",
            "clusterId": 0,
            "type": "type",
            "serviceInstanceId": 0,
            "status": "status",
            "runningStatus": "runningStatus",
            "version": "version",
            "envId": 0,
            "suInstanceId": 0,
            "instanceNum": "instanceNum",
            "cpuRequest": "cpuRequest",
            "cpuLimit": "cpuLimit",
            "memoryRequest": "memoryRequest",
            "memoryLimit": "memoryLimit",
            "logLevel": "logLevel",
            "creator": "creator",
            "updater": "updater",
            "tenantId": "tenantId",
            "createAt": "createAt",
            "updatedAt": "updatedAt",
            "deletedAt": "deletedAt"
        }
    ],
    "resource": {
        "networkZone": {
        },
        "k8sCluster": {
            "id": 0,
            "agentId": 0,
            "clusterCidr": "clusterCidr",
            "clusterCode": "clusterCode",
            "clusterName": "clusterName",
            "harbor": "harbor",
            "harborId": 0,
            "k8sConfigId": 0,
            "network": "network",
            "networkZone": "networkZone",
            "nodePortRange": "nodePortRange",
            "pemId": 0,
            "regionId": 0,
            "resourceGroup": "resourceGroup",
            "securityGroup": "securityGroup",
            "sepcId": 0,
            "serviceCidr": "serviceCidr",
            "status": "status",
            "suInstance": "suInstance",
            "taskId": 0,
            "tenantCode": "tenantCode",
            "tenantId": "tenantId",
            "tenantRegion": "tenantRegion",
            "type": "type",
            "K8SNode": [
                {
                    "id": 0,
                    "templateId": 0,
                    "templateName": "templateName",
                    "dataVolumeSize": 0,
                    "regionName": "regionName",
                    "regionCode": "regionCode",
                    "tenantAzName": "tenantAzName",
                    "azName": "azName",
                    "azCode": "azCode",
                    "memory": 0,
                    "cpu": 0,
                    "rootVolumeSize": 0,
                    "disk": 0,
                    "taskId": 0,
                    "sepcId": 0,
                    "orderNumber": 0,
                    "clusterId": 0,
                    "clusterCode": "clusterCode",
                    "region": "region",
                    "resourceGroup": "resourceGroup",
                    "networkZone": "networkZone",
                    "availabilityZone": "availabilityZone",
                    "ip": "ip",
                    "status": "status",
                    "role": "role",
                    "drain": "drain",
                    "schedule": "schedule",
                    "vmId": 0,
                    "tenantId": "tenantId"
                }
            ]
        },
        "cacheCluster": {
            "id": 0,
            "clusterCode": "clusterCode",
            "clusterName": "clusterName",
            "harborId": 0,
            "networkZone": "networkZone",
            "occupied": "occupied",
            "password": "password",
            "pemId": 0,
            "property": "property",
            "regionId": 0,
            "resourceGroup": "resourceGroup",
            "sepcId": 0,
            "status": "status",
            "taskId": 0,
            "tenantCode": "tenantCode",
            "tenantId": "tenantId",
            "tenantRegion": "tenantRegion",
            "type": "type",
            "CacheNode": [
                {
                    "memory": 0,
                    "cpu": 0,
                    "templateId": 0,
                    "templateName": "templateName",
                    "dataVolumeSize": 0,
                    "regionName": "regionName",
                    "regionCode": "regionCode",
                    "tenantAzCode": "tenantAzCode",
                    "tenantAzName": "tenantAzName",
                    "azName": "azName",
                    "azCode": "azCode",
                    "id": 0,
                    "taskId": 0,
                    "specId": 0,
                    "clusterId": 0,
                    "ip": "ip",
                    "status": "status",
                    "port": "port",
                    "role": "role",
                    "master_ip": "master_ip",
                    "master_port": "master_port",
                    "vmId": 0,
                    "resourceGroup": "resourceGroup",
                    "networkZone": "networkZone",
                    "tenantId": "tenantId"
                }
            ]
        },
        "cacheClusterList": [
            {
            }
        ],
        "pemVpn": {
            "mesh": {
                "agentId": 0,
                "code": "code",
                "description": "description",
                "harborId": 0,
                "id": 0,
                "maxConnection": 0,
                "name": "name",
                "networkZone": "networkZone",
                "occupied": "occupied",
                "password": "password",
                "property": "property",
                "resourceGroup": "resourceGroup",
                "role": "role",
                "siteId": 0,
                "specId": 0,
                "status": "status",
                "taskId": 0,
                "tenantId": "tenantId",
                "type": "type",
                "username": "username",
                "vmTemplateId": 0,
                "meshNode": [
                    {
                        "templateId": 0,
                        "templateName": "templateName",
                        "dataVolumeSize": 0,
                        "regionName": "regionName",
                        "regionCode": "regionCode",
                        "tenantAzName": "tenantAzName",
                        "tenantAzCode": "tenantAzCode",
                        "azName": "azName",
                        "azCode": "azCode",
                        "id": 0,
                        "taskId": 0,
                        "specId": 0,
                        "meshId": 0,
                        "meshCode": "meshCode",
                        "ip": "ip",
                        "role": "role",
                        "adminPort": 0,
                        "workerPort": 0,
                        "tenantId": "tenantId",
                        "resourceGroup": "resourceGroup",
                        "vmId": 0,
                        "status": "status",
                        "routerName": "routerName",
                        "description": "description"
                    }
                ]
            },
            "vpn": {
            }
        },
        "rdbGroup": {
            "rdbCluster": {
                "id": 0,
                "clusterId": "clusterId",
                "clusterName": "clusterName",
                "clusterType": "clusterType",
                "networkZone": "networkZone",
                "occupied": "occupied",
                "property": "property",
                "region": "region",
                "resourceGroup": "resourceGroup",
                "specId": 0,
                "status": "status",
                "taskId": 0,
                "tenantId": "tenantId",
                "updater": "updater",
                "updaterId": "updaterId",
                "version": "version",
                "vmTemplateId": 0,
                "RDBGroup": [
                    {
                        "id": 0,
                        "taskId": 0,
                        "sepcId": 0,
                        "region": "region",
                        "resourceGroup": "resourceGroup",
                        "networkZone": "networkZone",
                        "rdbGroupCode": "rdbGroupCode",
                        "clusterId": 0,
                        "clusterCode": "clusterCode",
                        "harborId": 0,
                        "pemId": 0,
                        "vip": "vip",
                        "az": "az",
                        "vport": 0,
                        "type": "type",
                        "purpose": "purpose",
                        "name": "name",
                        "description": "description"
                    }
                ]
            },
            "rdbGroup": {
            }
        },
        "aemVpnList": [
            {
            }
        ]
    },
    "config": {
        "multiTenant": false,
        "defaultAuthenticationType": "defaultAuthenticationType",
        "defaultEnableGoogleAuth": false,
        "minPasswordLength": 0,
        "maxPasswordLength": 0,
        "passwordHistoryReqeatLimit": 0,
        "requirePeriodicPasswordChange": false,
        "minPeriodicPasswordChangeDays": 0,
        "enableAlertBeforePasswordExpiration": false,
        "alertDaysBeforePasswordExpiration": 0,
        "numOfFailedLoginAttempts": 0,
        "requireUniqueEmailAddr": false,
        "automateUnlock": false,
        "automateUnlockTime": 0,
        "automateDeactivateAccount": false,
        "automateDeactivateDays": 0,
        "enableChangeUsername": false,
        "enableChangeEmail": false,
        "linkExpiryTime": 0,
        "codeExpiryTime": 0,
        "enableEnforceLogout": false,
        "tokenExpiryTime": 0,
        "enableAddGroupIntoToken": false,
        "enableAddRoleIntoToken": false,
        "enableAddAllDataPermission": false,
        "SamlConfiguration": {
            "samlIdpMetadataUrl": "samlIdpMetadataUrl",
            "samlSpRootUrl": "samlSpRootUrl",
            "samlWantSignRequest": false,
            "samlSpKeyPairkey": "samlSpKeyPairkey",
            "samlSpKeyPairCert": "samlSpKeyPairCert"
        },
        "LDAPConfiguration": {
            "ldapAddr": "ldapAddr",
            "ldapBaseDN": "ldapBaseDN",
            "ldapUsername": "ldapUsername",
            "ldapPassword": "ldapPassword",
            "userAttributes": "userAttributes",
            "searchFilter": "searchFilter"
        }
    }
}
```



# **upm - cluster list - API**

## URL: /environment/v1/upm/cluster/list ##

## Method : Post ##

## Event ID : EnvUpmPage ##

**Description:**

query upm cluster list

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| code | string |  | N |  |
| name | string |  | N |  |
| description | string |  | N |  |
| creator | string |  | N |  |
| createdTime | string |  | N |  |
| updater | string |  | N |  |
| updatedTime | string |  | N |  |
| status | string |  | N |  |
| tenantId | string |  | N |  |
| envId | integer |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "code": "code",
    "name": "name",
    "description": "description",
    "creator": "creator",
    "createdTime": "createdTime",
    "updater": "updater",
    "updatedTime": "updatedTime",
    "status": "status",
    "tenantId": "tenantId",
    "envId": 0
}
```

**struct example:**

```
type ClusterFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    Code string `json:"code"`
    Name string `json:"name"`
    Description string `json:"description"`
    Creator string `json:"creator"`
    CreatedTime string `json:"createdTime"`
    Updater string `json:"updater"`
    UpdatedTime string `json:"updatedTime"`
    Status string `json:"status"`
    TenantId string `json:"tenantId"`
    EnvId uint64 `json:"envId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```


