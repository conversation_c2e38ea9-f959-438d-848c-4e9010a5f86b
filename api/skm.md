
# **skm - cluster detail - API**

## URL: /environment/v1/skm/cluster/detail ##

## Method : Post ##

## Event ID : EnvSkmDetail ##

**Description:**

query skm cluster detail with skm services

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type SkmClusterDetailRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| code | string | N |  |
| name | string | N |  |
| description | string | N |  |
| operate | string | N |  |
| version | string | N |  |
| instanceNum | string | N |  |
| cpuRequest | string | N |  |
| cpuLimit | string | N |  |
| memoryRequest | string | N |  |
| memoryLimit | string | N |  |
| logLevel | string | N |  |
| environment | object | N |  |
| Environment.id | integer | N |  |
| Environment.workspaceId | string | N |  |
| Environment.resourceGroupId | integer | N |  |
| Environment.resourceGroupName | string | N |  |
| Environment.name | string | N |  |
| Environment.code | string | N |  |
| Environment.phase | string | N |  |
| Environment.preset | string | N |  |
| Environment.serverless | string | N |  |
| Environment.wsGitPath | string | N |  |
| Environment.repoId | integer | N |  |
| Environment.repodbId | integer | N |  |
| Environment.templateGroupId | integer | N |  |
| Environment.configProjID | integer | N |  |
| Environment.upmDataId | integer | N |  |
| Environment.description | string | N |  |
| Environment.status | string | N |  |
| Environment.tenantId | string | N |  |
| Environment.updaterId | string | N |  |
| Environment.updater | string | N |  |
| Environment.creatorId | string | N |  |
| Environment.creator | string | N |  |
| Environment.createAt | string | N |  |
| Environment.updatedAt | string | N |  |
| Environment.deletedAt | string | N |  |
| suInstanceInfo | object | N |  |
| SuInstanceInfo.workspaceId | string | N |  |
| SuInstanceInfo.wsGitPath | string | N |  |
| SuInstanceInfo.resourceGroupId | integer | N |  |
| SuInstanceInfo.resourceGroupName | string | N |  |
| SuInstanceInfo.repoId | integer | N |  |
| SuInstanceInfo.repodbId | integer | N |  |
| SuInstanceInfo.environmentName | string | N |  |
| SuInstanceInfo.environmentCode | string | N |  |
| SuInstanceInfo.phase | string | N |  |
| SuInstanceInfo.stackId | integer | N |  |
| SuInstanceInfo.envId | integer | N |  |
| SuInstanceInfo.networkZoneID | integer | N |  |
| SuInstanceInfo.suTypeId | integer | N |  |
| SuInstanceInfo.suTypeCode | string | N |  |
| SuInstanceInfo.suName | string | N |  |
| SuInstanceInfo.suCode | string | N |  |
| SuInstanceInfo.suWeight | uint | N |  |
| SuInstanceInfo.suQuota | integer | N |  |
| SuInstanceInfo.objRelations | map[string]uint64 | N |  |
| SuInstanceInfo.networkZone | object | N |  |
| NetworkZoneInfo.id | integer | N |  |
| NetworkZoneInfo.taskId | integer | N |  |
| NetworkZoneInfo.specId | integer | N |  |
| NetworkZoneInfo.name | string | N |  |
| NetworkZoneInfo.code | string | N |  |
| NetworkZoneInfo.tenantRegionId | integer | N |  |
| NetworkZoneInfo.tenantVpcId | integer | N |  |
| NetworkZoneInfo.resourceGroupId | integer | N |  |
| NetworkZoneInfo.azId | integer | N |  |
| NetworkZoneInfo.serialNumber | integer | N |  |
| NetworkZoneInfo.tenantId | string | N |  |
| NetworkZoneInfo.tenantCode | string | N |  |
| NetworkZoneInfo.status | string | N |  |
| NetworkZoneInfo.description | string | N |  |
| SuInstanceInfo.SuInstance | object | N |  |
| SuInstance.id | integer | N |  |
| SuInstance.envId | integer | N |  |
| SuInstance.suId | integer | N |  |
| SuInstance.name | string | N |  |
| SuInstance.code | string | N |  |
| SuInstance.description | string | N |  |
| SuInstance.tenantId | string | N |  |
| SuInstance.creatorId | string | N |  |
| SuInstance.creator | string | N |  |
| SuInstance.createAt | string | N |  |
| SuInstance.updatedAt | string | N |  |
| SuInstance.deletedAt | string | N |  |
| services | array[SkmService] | N |  |
| SkmService.id | integer | N |  |
| SkmService.name | string | N |  |
| SkmService.clusterId | integer | N |  |
| SkmService.type | string | N |  |
| SkmService.envId | integer | N |  |
| SkmService.suInstanceId | integer | N |  |
| SkmService.meshId | integer | N |  |
| SkmService.meshVpnId | integer | N |  |
| SkmService.k8sId | integer | N |  |
| SkmService.status | string | N |  |
| SkmService.version | string | N |  |
| SkmService.instanceNum | string | N |  |
| SkmService.cpuRequest | string | N |  |
| SkmService.cpuLimit | string | N |  |
| SkmService.memoryRequest | string | N |  |
| SkmService.memoryLimit | string | N |  |
| SkmService.logLevel | string | N |  |
| SkmService.creator | string | N |  |
| SkmService.updater | string | N |  |
| SkmService.tenantId | string | N |  |
| SkmService.runningStatus | string | N |  |
| SkmService.createAt | string | N |  |
| SkmService.updatedAt | string | N |  |
| SkmService.deletedAt | string | N |  |
| resource | object | N |  |
| SuInstanceResource.networkZone | object | N |  |
| SuInstanceResource.k8sCluster | object | N |  |
| ResourceK8sCluster.id | integer | N |  |
| ResourceK8sCluster.agentId | integer | N |  |
| ResourceK8sCluster.clusterCidr | string | N |  |
| ResourceK8sCluster.clusterCode | string | N |  |
| ResourceK8sCluster.clusterName | string | N |  |
| ResourceK8sCluster.harbor | string | N |  |
| ResourceK8sCluster.harborId | integer | N |  |
| ResourceK8sCluster.k8sConfigId | integer | N |  |
| ResourceK8sCluster.network | string | N |  |
| ResourceK8sCluster.networkZone | string | N |  |
| ResourceK8sCluster.nodePortRange | string | N |  |
| ResourceK8sCluster.pemId | integer | N |  |
| ResourceK8sCluster.regionId | integer | N |  |
| ResourceK8sCluster.resourceGroup | string | N |  |
| ResourceK8sCluster.securityGroup | string | N |  |
| ResourceK8sCluster.sepcId | integer | N |  |
| ResourceK8sCluster.serviceCidr | string | N |  |
| ResourceK8sCluster.status | string | N |  |
| ResourceK8sCluster.suInstance | string | N |  |
| ResourceK8sCluster.taskId | integer | N |  |
| ResourceK8sCluster.tenantCode | string | N |  |
| ResourceK8sCluster.tenantId | string | N |  |
| ResourceK8sCluster.tenantRegion | string | N |  |
| ResourceK8sCluster.type | string | N |  |
| ResourceK8sCluster.K8SNode | array[ResourceK8SNode] | N |  |
| ResourceK8SNode.id | integer | N |  |
| ResourceK8SNode.templateId | integer | N |  |
| ResourceK8SNode.templateName | string | N |  |
| ResourceK8SNode.dataVolumeSize | integer | N |  |
| ResourceK8SNode.regionName | string | N |  |
| ResourceK8SNode.regionCode | string | N |  |
| ResourceK8SNode.tenantAzName | string | N |  |
| ResourceK8SNode.azName | string | N |  |
| ResourceK8SNode.azCode | string | N |  |
| ResourceK8SNode.memory | integer | N |  |
| ResourceK8SNode.cpu | integer | N |  |
| ResourceK8SNode.rootVolumeSize | integer | N |  |
| ResourceK8SNode.disk | integer | N |  |
| ResourceK8SNode.taskId | integer | N |  |
| ResourceK8SNode.sepcId | integer | N |  |
| ResourceK8SNode.orderNumber | integer | N |  |
| ResourceK8SNode.clusterId | integer | N |  |
| ResourceK8SNode.clusterCode | string | N |  |
| ResourceK8SNode.region | string | N |  |
| ResourceK8SNode.resourceGroup | string | N |  |
| ResourceK8SNode.networkZone | string | N |  |
| ResourceK8SNode.availabilityZone | string | N |  |
| ResourceK8SNode.ip | string | N |  |
| ResourceK8SNode.status | string | N |  |
| ResourceK8SNode.role | string | N |  |
| ResourceK8SNode.drain | string | N |  |
| ResourceK8SNode.schedule | string | N |  |
| ResourceK8SNode.vmId | integer | N |  |
| ResourceK8SNode.tenantId | string | N |  |
| SuInstanceResource.cacheCluster | object | N |  |
| ResourceCacheCluster.id | integer | N |  |
| ResourceCacheCluster.clusterCode | string | N |  |
| ResourceCacheCluster.clusterName | string | N |  |
| ResourceCacheCluster.harborId | integer | N |  |
| ResourceCacheCluster.networkZone | string | N |  |
| ResourceCacheCluster.occupied | string | N |  |
| ResourceCacheCluster.password | string | N |  |
| ResourceCacheCluster.pemId | integer | N |  |
| ResourceCacheCluster.property | string | N |  |
| ResourceCacheCluster.regionId | integer | N |  |
| ResourceCacheCluster.resourceGroup | string | N |  |
| ResourceCacheCluster.sepcId | integer | N |  |
| ResourceCacheCluster.status | string | N |  |
| ResourceCacheCluster.taskId | integer | N |  |
| ResourceCacheCluster.tenantCode | string | N |  |
| ResourceCacheCluster.tenantId | string | N |  |
| ResourceCacheCluster.tenantRegion | string | N |  |
| ResourceCacheCluster.type | string | N |  |
| ResourceCacheCluster.CacheNode | array[ResourceCacheNode] | N |  |
| ResourceCacheNode.memory | integer | N |  |
| ResourceCacheNode.cpu | integer | N |  |
| ResourceCacheNode.templateId | integer | N |  |
| ResourceCacheNode.templateName | string | N |  |
| ResourceCacheNode.dataVolumeSize | integer | N |  |
| ResourceCacheNode.regionName | string | N |  |
| ResourceCacheNode.regionCode | string | N |  |
| ResourceCacheNode.tenantAzCode | string | N |  |
| ResourceCacheNode.tenantAzName | string | N |  |
| ResourceCacheNode.azName | string | N |  |
| ResourceCacheNode.azCode | string | N |  |
| ResourceCacheNode.id | integer | N |  |
| ResourceCacheNode.taskId | integer | N |  |
| ResourceCacheNode.specId | integer | N |  |
| ResourceCacheNode.clusterId | integer | N |  |
| ResourceCacheNode.ip | string | N |  |
| ResourceCacheNode.status | string | N |  |
| ResourceCacheNode.port | string | N |  |
| ResourceCacheNode.role | string | N |  |
| ResourceCacheNode.master_ip | string | N |  |
| ResourceCacheNode.master_port | string | N |  |
| ResourceCacheNode.vmId | integer | N |  |
| ResourceCacheNode.resourceGroup | string | N |  |
| ResourceCacheNode.networkZone | string | N |  |
| ResourceCacheNode.tenantId | string | N |  |
| SuInstanceResource.cacheClusterList | array[ResourceCacheCluster] | N |  |
| SuInstanceResource.pemVpn | object | N |  |
| VpnInfo.mesh | object | N |  |
| MeshSpec.agentId | integer | N |  |
| MeshSpec.code | string | N |  |
| MeshSpec.description | string | N |  |
| MeshSpec.harborId | integer | N |  |
| MeshSpec.id | integer | N |  |
| MeshSpec.maxConnection | integer | N |  |
| MeshSpec.name | string | N |  |
| MeshSpec.networkZone | string | N |  |
| MeshSpec.occupied | string | N |  |
| MeshSpec.password | string | N |  |
| MeshSpec.property | string | N |  |
| MeshSpec.resourceGroup | string | N |  |
| MeshSpec.role | string | N |  |
| MeshSpec.siteId | integer | N |  |
| MeshSpec.specId | integer | N |  |
| MeshSpec.status | string | N |  |
| MeshSpec.taskId | integer | N |  |
| MeshSpec.tenantId | string | N |  |
| MeshSpec.type | string | N |  |
| MeshSpec.username | string | N |  |
| MeshSpec.vmTemplateId | integer | N |  |
| MeshSpec.meshNode | array[MeshNodeInfo] | N |  |
| MeshNodeInfo.templateId | integer | N |  |
| MeshNodeInfo.templateName | string | N |  |
| MeshNodeInfo.dataVolumeSize | integer | N |  |
| MeshNodeInfo.regionName | string | N |  |
| MeshNodeInfo.regionCode | string | N |  |
| MeshNodeInfo.tenantAzName | string | N |  |
| MeshNodeInfo.tenantAzCode | string | N |  |
| MeshNodeInfo.azName | string | N |  |
| MeshNodeInfo.azCode | string | N |  |
| MeshNodeInfo.id | integer | N |  |
| MeshNodeInfo.taskId | integer | N |  |
| MeshNodeInfo.specId | integer | N |  |
| MeshNodeInfo.meshId | integer | N |  |
| MeshNodeInfo.meshCode | string | N |  |
| MeshNodeInfo.ip | string | N |  |
| MeshNodeInfo.role | string | N |  |
| MeshNodeInfo.adminPort | integer | N |  |
| MeshNodeInfo.workerPort | integer | N |  |
| MeshNodeInfo.tenantId | string | N |  |
| MeshNodeInfo.resourceGroup | string | N |  |
| MeshNodeInfo.vmId | integer | N |  |
| MeshNodeInfo.status | string | N |  |
| MeshNodeInfo.routerName | string | N |  |
| MeshNodeInfo.description | string | N |  |
| VpnInfo.vpn | object | N |  |
| SuInstanceResource.rdbGroup | object | N |  |
| RDBGroupInfo.rdbCluster | object | N |  |
| ResourceRdbCluster.id | integer | N |  |
| ResourceRdbCluster.clusterId | string | N |  |
| ResourceRdbCluster.clusterName | string | N |  |
| ResourceRdbCluster.clusterType | string | N |  |
| ResourceRdbCluster.networkZone | string | N |  |
| ResourceRdbCluster.occupied | string | N |  |
| ResourceRdbCluster.property | string | N |  |
| ResourceRdbCluster.region | string | N |  |
| ResourceRdbCluster.resourceGroup | string | N |  |
| ResourceRdbCluster.specId | integer | N |  |
| ResourceRdbCluster.status | string | N |  |
| ResourceRdbCluster.taskId | integer | N |  |
| ResourceRdbCluster.tenantId | string | N |  |
| ResourceRdbCluster.updater | string | N |  |
| ResourceRdbCluster.updaterId | string | N |  |
| ResourceRdbCluster.version | string | N |  |
| ResourceRdbCluster.vmTemplateId | integer | N |  |
| ResourceRdbCluster.RDBGroup | array[ResourceRdbGroup] | N |  |
| ResourceRdbGroup.id | integer | N |  |
| ResourceRdbGroup.taskId | integer | N |  |
| ResourceRdbGroup.sepcId | integer | N |  |
| ResourceRdbGroup.region | string | N |  |
| ResourceRdbGroup.resourceGroup | string | N |  |
| ResourceRdbGroup.networkZone | string | N |  |
| ResourceRdbGroup.rdbGroupCode | string | N |  |
| ResourceRdbGroup.clusterId | integer | N |  |
| ResourceRdbGroup.clusterCode | string | N |  |
| ResourceRdbGroup.harborId | integer | N |  |
| ResourceRdbGroup.pemId | integer | N |  |
| ResourceRdbGroup.vip | string | N |  |
| ResourceRdbGroup.az | string | N |  |
| ResourceRdbGroup.vport | integer | N |  |
| ResourceRdbGroup.type | string | N |  |
| ResourceRdbGroup.purpose | string | N |  |
| ResourceRdbGroup.name | string | N |  |
| ResourceRdbGroup.description | string | N |  |
| RDBGroupInfo.rdbGroup | object | N |  |
| SuInstanceResource.aemVpnList | array[VpnInfo] | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "code": "code",
    "name": "name",
    "description": "description",
    "operate": "operate",
    "version": "version",
    "instanceNum": "instanceNum",
    "cpuRequest": "cpuRequest",
    "cpuLimit": "cpuLimit",
    "memoryRequest": "memoryRequest",
    "memoryLimit": "memoryLimit",
    "logLevel": "logLevel",
    "environment": {
        "id": 0,
        "workspaceId": "workspaceId",
        "resourceGroupId": 0,
        "resourceGroupName": "resourceGroupName",
        "name": "name",
        "code": "code",
        "phase": "phase",
        "preset": "preset",
        "serverless": "serverless",
        "wsGitPath": "wsGitPath",
        "repoId": 0,
        "repodbId": 0,
        "templateGroupId": 0,
        "configProjID": 0,
        "upmDataId": 0,
        "description": "description",
        "status": "status",
        "tenantId": "tenantId",
        "updaterId": "updaterId",
        "updater": "updater",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    },
    "suInstanceInfo": {
        "workspaceId": "workspaceId",
        "wsGitPath": "wsGitPath",
        "resourceGroupId": 0,
        "resourceGroupName": "resourceGroupName",
        "repoId": 0,
        "repodbId": 0,
        "environmentName": "environmentName",
        "environmentCode": "environmentCode",
        "phase": "phase",
        "stackId": 0,
        "envId": 0,
        "networkZoneID": 0,
        "suTypeId": 0,
        "suTypeCode": "suTypeCode",
        "suName": "suName",
        "suCode": "suCode",
        "suWeight": "suWeight",
        "suQuota": 0,
        "objRelations": {
        },
        "networkZone": {
            "id": 0,
            "taskId": 0,
            "specId": 0,
            "name": "name",
            "code": "code",
            "tenantRegionId": 0,
            "tenantVpcId": 0,
            "resourceGroupId": 0,
            "azId": 0,
            "serialNumber": 0,
            "tenantId": "tenantId",
            "tenantCode": "tenantCode",
            "status": "status",
            "description": "description"
        },
        "SuInstance": {
            "id": 0,
            "envId": 0,
            "suId": 0,
            "name": "name",
            "code": "code",
            "description": "description",
            "tenantId": "tenantId",
            "creatorId": "creatorId",
            "creator": "creator",
            "createAt": "createAt",
            "updatedAt": "updatedAt",
            "deletedAt": "deletedAt"
        }
    },
    "services": [
        {
            "id": 0,
            "name": "name",
            "clusterId": 0,
            "type": "type",
            "envId": 0,
            "suInstanceId": 0,
            "meshId": 0,
            "meshVpnId": 0,
            "k8sId": 0,
            "status": "status",
            "version": "version",
            "instanceNum": "instanceNum",
            "cpuRequest": "cpuRequest",
            "cpuLimit": "cpuLimit",
            "memoryRequest": "memoryRequest",
            "memoryLimit": "memoryLimit",
            "logLevel": "logLevel",
            "creator": "creator",
            "updater": "updater",
            "tenantId": "tenantId",
            "runningStatus": "runningStatus",
            "createAt": "createAt",
            "updatedAt": "updatedAt",
            "deletedAt": "deletedAt"
        }
    ],
    "resource": {
        "networkZone": {
        },
        "k8sCluster": {
            "id": 0,
            "agentId": 0,
            "clusterCidr": "clusterCidr",
            "clusterCode": "clusterCode",
            "clusterName": "clusterName",
            "harbor": "harbor",
            "harborId": 0,
            "k8sConfigId": 0,
            "network": "network",
            "networkZone": "networkZone",
            "nodePortRange": "nodePortRange",
            "pemId": 0,
            "regionId": 0,
            "resourceGroup": "resourceGroup",
            "securityGroup": "securityGroup",
            "sepcId": 0,
            "serviceCidr": "serviceCidr",
            "status": "status",
            "suInstance": "suInstance",
            "taskId": 0,
            "tenantCode": "tenantCode",
            "tenantId": "tenantId",
            "tenantRegion": "tenantRegion",
            "type": "type",
            "K8SNode": [
                {
                    "id": 0,
                    "templateId": 0,
                    "templateName": "templateName",
                    "dataVolumeSize": 0,
                    "regionName": "regionName",
                    "regionCode": "regionCode",
                    "tenantAzName": "tenantAzName",
                    "azName": "azName",
                    "azCode": "azCode",
                    "memory": 0,
                    "cpu": 0,
                    "rootVolumeSize": 0,
                    "disk": 0,
                    "taskId": 0,
                    "sepcId": 0,
                    "orderNumber": 0,
                    "clusterId": 0,
                    "clusterCode": "clusterCode",
                    "region": "region",
                    "resourceGroup": "resourceGroup",
                    "networkZone": "networkZone",
                    "availabilityZone": "availabilityZone",
                    "ip": "ip",
                    "status": "status",
                    "role": "role",
                    "drain": "drain",
                    "schedule": "schedule",
                    "vmId": 0,
                    "tenantId": "tenantId"
                }
            ]
        },
        "cacheCluster": {
            "id": 0,
            "clusterCode": "clusterCode",
            "clusterName": "clusterName",
            "harborId": 0,
            "networkZone": "networkZone",
            "occupied": "occupied",
            "password": "password",
            "pemId": 0,
            "property": "property",
            "regionId": 0,
            "resourceGroup": "resourceGroup",
            "sepcId": 0,
            "status": "status",
            "taskId": 0,
            "tenantCode": "tenantCode",
            "tenantId": "tenantId",
            "tenantRegion": "tenantRegion",
            "type": "type",
            "CacheNode": [
                {
                    "memory": 0,
                    "cpu": 0,
                    "templateId": 0,
                    "templateName": "templateName",
                    "dataVolumeSize": 0,
                    "regionName": "regionName",
                    "regionCode": "regionCode",
                    "tenantAzCode": "tenantAzCode",
                    "tenantAzName": "tenantAzName",
                    "azName": "azName",
                    "azCode": "azCode",
                    "id": 0,
                    "taskId": 0,
                    "specId": 0,
                    "clusterId": 0,
                    "ip": "ip",
                    "status": "status",
                    "port": "port",
                    "role": "role",
                    "master_ip": "master_ip",
                    "master_port": "master_port",
                    "vmId": 0,
                    "resourceGroup": "resourceGroup",
                    "networkZone": "networkZone",
                    "tenantId": "tenantId"
                }
            ]
        },
        "cacheClusterList": [
            {
            }
        ],
        "pemVpn": {
            "mesh": {
                "agentId": 0,
                "code": "code",
                "description": "description",
                "harborId": 0,
                "id": 0,
                "maxConnection": 0,
                "name": "name",
                "networkZone": "networkZone",
                "occupied": "occupied",
                "password": "password",
                "property": "property",
                "resourceGroup": "resourceGroup",
                "role": "role",
                "siteId": 0,
                "specId": 0,
                "status": "status",
                "taskId": 0,
                "tenantId": "tenantId",
                "type": "type",
                "username": "username",
                "vmTemplateId": 0,
                "meshNode": [
                    {
                        "templateId": 0,
                        "templateName": "templateName",
                        "dataVolumeSize": 0,
                        "regionName": "regionName",
                        "regionCode": "regionCode",
                        "tenantAzName": "tenantAzName",
                        "tenantAzCode": "tenantAzCode",
                        "azName": "azName",
                        "azCode": "azCode",
                        "id": 0,
                        "taskId": 0,
                        "specId": 0,
                        "meshId": 0,
                        "meshCode": "meshCode",
                        "ip": "ip",
                        "role": "role",
                        "adminPort": 0,
                        "workerPort": 0,
                        "tenantId": "tenantId",
                        "resourceGroup": "resourceGroup",
                        "vmId": 0,
                        "status": "status",
                        "routerName": "routerName",
                        "description": "description"
                    }
                ]
            },
            "vpn": {
            }
        },
        "rdbGroup": {
            "rdbCluster": {
                "id": 0,
                "clusterId": "clusterId",
                "clusterName": "clusterName",
                "clusterType": "clusterType",
                "networkZone": "networkZone",
                "occupied": "occupied",
                "property": "property",
                "region": "region",
                "resourceGroup": "resourceGroup",
                "specId": 0,
                "status": "status",
                "taskId": 0,
                "tenantId": "tenantId",
                "updater": "updater",
                "updaterId": "updaterId",
                "version": "version",
                "vmTemplateId": 0,
                "RDBGroup": [
                    {
                        "id": 0,
                        "taskId": 0,
                        "sepcId": 0,
                        "region": "region",
                        "resourceGroup": "resourceGroup",
                        "networkZone": "networkZone",
                        "rdbGroupCode": "rdbGroupCode",
                        "clusterId": 0,
                        "clusterCode": "clusterCode",
                        "harborId": 0,
                        "pemId": 0,
                        "vip": "vip",
                        "az": "az",
                        "vport": 0,
                        "type": "type",
                        "purpose": "purpose",
                        "name": "name",
                        "description": "description"
                    }
                ]
            },
            "rdbGroup": {
            }
        },
        "aemVpnList": [
            {
            }
        ]
    }
}
```



# **skm - cluster page - API**

## URL: /environment/v1/skm/cluster/page ##

## Method : Post ##

## Event ID : EnvSkmPage ##

**Description:**

query skm cluster list

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| name | string |  | N |  |
| description | string |  | N |  |
| creator | string |  | N |  |
| createdTime | string |  | N |  |
| updater | string |  | N |  |
| updatedTime | string |  | N |  |
| status | string |  | N |  |
| tenantId | string |  | N |  |
| envId | integer |  | N |  |
| code | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "name": "name",
    "description": "description",
    "creator": "creator",
    "createdTime": "createdTime",
    "updater": "updater",
    "updatedTime": "updatedTime",
    "status": "status",
    "tenantId": "tenantId",
    "envId": 0,
    "code": "code"
}
```

**struct example:**

```
type SkmClusterFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    Name string `json:"name"`
    Description string `json:"description"`
    Creator string `json:"creator"`
    CreatedTime string `json:"createdTime"`
    Updater string `json:"updater"`
    UpdatedTime string `json:"updatedTime"`
    Status string `json:"status"`
    TenantId string `json:"tenantId"`
    EnvId uint64 `json:"envId"`
    Code string `json:"code"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **skm - cluster update - API**

## URL: /environment/v1/skm/cluster/update ##

## Method : Post ##

## Event ID : EnvSkmUpdate ##

**Description:**

change skm cluster name

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |
| name | string |  | N |  |
| description | string |  | N |  |

**Range of values**

request example:
```
{
    "id": 0,
    "name": "name",
    "description": "description"
}
```

**struct example:**

```
type SkmUpdateRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
    Name string `json:"name"`
    Description string `json:"description"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


