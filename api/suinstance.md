
# **suinstance - create - API**

## URL: /environment/v1/suinstance/create ##

## Method : Post ##

## Event ID : EnvSuInstanceCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| environmentId | integer |  | N |  |
| sourceId | integer |  | N |  |
| suInstanceList | array[CreateSuInstance] |  | N |  |
| CreateSuInstance.suId | integer |  | N |  |
| CreateSuInstance.name | string |  | N |  |
| CreateSuInstance.code | string |  | N |  |
| CreateSuInstance.description | string |  | N |  |

**Range of values**

request example:
```
{
    "environmentId": 0,
    "sourceId": 0,
    "suInstanceList": [
        {
            "suId": 0,
            "name": "name",
            "code": "code",
            "description": "description"
        }
    ]
}
```

**struct example:**

```
type CreateSuInstanceRequest struct {
    EnvironmentId uint64 `json:"environmentId"`
    SourceId uint64 `json:"sourceId"`
    SuInstanceList []struct {
            SuId uint64 `json:"suId"`
            Name string `json:"name"`
            Code string `json:"code"`
            Description string `json:"description"`
        } `json:"suInstanceList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **suinstance - delete - API**

## URL: /environment/v1/suinstance/delete ##

## Method : Post ##

## Event ID : EnvSuInstanceDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type suInstanceIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **suinstance - detail - API**

## URL: /environment/v1/suinstance/detail ##

## Method : Post ##

## Event ID : EnvSuInstanceDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type suInstanceIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| workspaceId | string | N |  |
| wsGitPath | string | N |  |
| resourceGroupId | integer | N |  |
| resourceGroupName | string | N |  |
| repoId | integer | N |  |
| repodbId | integer | N |  |
| environmentName | string | N |  |
| environmentCode | string | N |  |
| phase | string | N |  |
| stackId | integer | N |  |
| envId | integer | N |  |
| networkZoneID | integer | N |  |
| suTypeId | integer | N |  |
| suTypeCode | string | N |  |
| suName | string | N |  |
| suCode | string | N |  |
| suWeight | uint | N |  |
| suQuota | integer | N |  |
| objRelations | map[string]uint64 | N |  |
| networkZone | object | N |  |
| NetworkZoneInfo.id | integer | N |  |
| NetworkZoneInfo.taskId | integer | N |  |
| NetworkZoneInfo.specId | integer | N |  |
| NetworkZoneInfo.name | string | N |  |
| NetworkZoneInfo.code | string | N |  |
| NetworkZoneInfo.tenantRegionId | integer | N |  |
| NetworkZoneInfo.tenantVpcId | integer | N |  |
| NetworkZoneInfo.resourceGroupId | integer | N |  |
| NetworkZoneInfo.azId | integer | N |  |
| NetworkZoneInfo.serialNumber | integer | N |  |
| NetworkZoneInfo.tenantId | string | N |  |
| NetworkZoneInfo.tenantCode | string | N |  |
| NetworkZoneInfo.status | string | N |  |
| NetworkZoneInfo.description | string | N |  |
| SuInstance | object | N |  |
| SuInstance.id | integer | N |  |
| SuInstance.envId | integer | N |  |
| SuInstance.suId | integer | N |  |
| SuInstance.name | string | N |  |
| SuInstance.code | string | N |  |
| SuInstance.description | string | N |  |
| SuInstance.tenantId | string | N |  |
| SuInstance.creatorId | string | N |  |
| SuInstance.creator | string | N |  |
| SuInstance.createAt | string | N |  |
| SuInstance.updatedAt | string | N |  |
| SuInstance.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "workspaceId": "workspaceId",
    "wsGitPath": "wsGitPath",
    "resourceGroupId": 0,
    "resourceGroupName": "resourceGroupName",
    "repoId": 0,
    "repodbId": 0,
    "environmentName": "environmentName",
    "environmentCode": "environmentCode",
    "phase": "phase",
    "stackId": 0,
    "envId": 0,
    "networkZoneID": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "suName": "suName",
    "suCode": "suCode",
    "suWeight": "suWeight",
    "suQuota": 0,
    "objRelations": {
    },
    "networkZone": {
        "id": 0,
        "taskId": 0,
        "specId": 0,
        "name": "name",
        "code": "code",
        "tenantRegionId": 0,
        "tenantVpcId": 0,
        "resourceGroupId": 0,
        "azId": 0,
        "serialNumber": 0,
        "tenantId": "tenantId",
        "tenantCode": "tenantCode",
        "status": "status",
        "description": "description"
    },
    "SuInstance": {
        "id": 0,
        "envId": 0,
        "suId": 0,
        "name": "name",
        "code": "code",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **suinstance - page - API**

## URL: /environment/v1/suinstance/page ##

## Method : Post ##

## Event ID : EnvSuInstancePage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| tenantId | string |  | N |  |
| environmentId | integer |  | N |  |
| suId | integer |  | N |  |
| code | string |  | N |  |
| name | string |  | N |  |
| suIdList | array[uint64] |  | N |  |
| suInstanceIdList | array[uint64] |  | N |  |
| suTypeCode | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "tenantId": "tenantId",
    "environmentId": 0,
    "suId": 0,
    "code": "code",
    "name": "name",
    "suIdList": [
    ],
    "suInstanceIdList": [
    ],
    "suTypeCode": "suTypeCode"
}
```

**struct example:**

```
type SuInstanceFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    TenantId string `json:"tenantId"`
    EnvironmentId uint64 `json:"environmentId"`
    SuId uint64 `json:"suId"`
    Code string `json:"code"`
    Name string `json:"name"`
    SuIdList []uint64 `json:"suIdList"`
    SuInstanceIdList []uint64 `json:"suInstanceIdList"`
    SuTypeCode string `json:"suTypeCode"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **suinstance - resource - API**

## URL: /environment/v1/suinstance/resource ##

## Method : Post ##

## Event ID : EnvSuInstanceResource ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required |
| emptyFault | boolean |  | N |  |

**Range of values**

request example:
```
{
    "id": 0,
    "emptyFault": false
}
```

**struct example:**

```
type SuInstanceResourceRequest struct {
    Id uint64 `json:"id" validate:"required"`
    EmptyFault bool `json:"emptyFault"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| networkZone | object | N |  |
| NetworkZoneInfo.id | integer | N |  |
| NetworkZoneInfo.taskId | integer | N |  |
| NetworkZoneInfo.specId | integer | N |  |
| NetworkZoneInfo.name | string | N |  |
| NetworkZoneInfo.code | string | N |  |
| NetworkZoneInfo.tenantRegionId | integer | N |  |
| NetworkZoneInfo.tenantVpcId | integer | N |  |
| NetworkZoneInfo.resourceGroupId | integer | N |  |
| NetworkZoneInfo.azId | integer | N |  |
| NetworkZoneInfo.serialNumber | integer | N |  |
| NetworkZoneInfo.tenantId | string | N |  |
| NetworkZoneInfo.tenantCode | string | N |  |
| NetworkZoneInfo.status | string | N |  |
| NetworkZoneInfo.description | string | N |  |
| k8sCluster | object | N |  |
| ResourceK8sCluster.id | integer | N |  |
| ResourceK8sCluster.agentId | integer | N |  |
| ResourceK8sCluster.clusterCidr | string | N |  |
| ResourceK8sCluster.clusterCode | string | N |  |
| ResourceK8sCluster.clusterName | string | N |  |
| ResourceK8sCluster.harbor | string | N |  |
| ResourceK8sCluster.harborId | integer | N |  |
| ResourceK8sCluster.k8sConfigId | integer | N |  |
| ResourceK8sCluster.network | string | N |  |
| ResourceK8sCluster.networkZone | string | N |  |
| ResourceK8sCluster.nodePortRange | string | N |  |
| ResourceK8sCluster.pemId | integer | N |  |
| ResourceK8sCluster.regionId | integer | N |  |
| ResourceK8sCluster.resourceGroup | string | N |  |
| ResourceK8sCluster.securityGroup | string | N |  |
| ResourceK8sCluster.sepcId | integer | N |  |
| ResourceK8sCluster.serviceCidr | string | N |  |
| ResourceK8sCluster.status | string | N |  |
| ResourceK8sCluster.suInstance | string | N |  |
| ResourceK8sCluster.taskId | integer | N |  |
| ResourceK8sCluster.tenantCode | string | N |  |
| ResourceK8sCluster.tenantId | string | N |  |
| ResourceK8sCluster.tenantRegion | string | N |  |
| ResourceK8sCluster.type | string | N |  |
| ResourceK8sCluster.K8SNode | array[ResourceK8SNode] | N |  |
| ResourceK8SNode.id | integer | N |  |
| ResourceK8SNode.templateId | integer | N |  |
| ResourceK8SNode.templateName | string | N |  |
| ResourceK8SNode.dataVolumeSize | integer | N |  |
| ResourceK8SNode.regionName | string | N |  |
| ResourceK8SNode.regionCode | string | N |  |
| ResourceK8SNode.tenantAzName | string | N |  |
| ResourceK8SNode.azName | string | N |  |
| ResourceK8SNode.azCode | string | N |  |
| ResourceK8SNode.memory | integer | N |  |
| ResourceK8SNode.cpu | integer | N |  |
| ResourceK8SNode.rootVolumeSize | integer | N |  |
| ResourceK8SNode.disk | integer | N |  |
| ResourceK8SNode.taskId | integer | N |  |
| ResourceK8SNode.sepcId | integer | N |  |
| ResourceK8SNode.orderNumber | integer | N |  |
| ResourceK8SNode.clusterId | integer | N |  |
| ResourceK8SNode.clusterCode | string | N |  |
| ResourceK8SNode.region | string | N |  |
| ResourceK8SNode.resourceGroup | string | N |  |
| ResourceK8SNode.networkZone | string | N |  |
| ResourceK8SNode.availabilityZone | string | N |  |
| ResourceK8SNode.ip | string | N |  |
| ResourceK8SNode.status | string | N |  |
| ResourceK8SNode.role | string | N |  |
| ResourceK8SNode.drain | string | N |  |
| ResourceK8SNode.schedule | string | N |  |
| ResourceK8SNode.vmId | integer | N |  |
| ResourceK8SNode.tenantId | string | N |  |
| cacheCluster | object | N |  |
| ResourceCacheCluster.id | integer | N |  |
| ResourceCacheCluster.clusterCode | string | N |  |
| ResourceCacheCluster.clusterName | string | N |  |
| ResourceCacheCluster.harborId | integer | N |  |
| ResourceCacheCluster.networkZone | string | N |  |
| ResourceCacheCluster.occupied | string | N |  |
| ResourceCacheCluster.password | string | N |  |
| ResourceCacheCluster.pemId | integer | N |  |
| ResourceCacheCluster.property | string | N |  |
| ResourceCacheCluster.regionId | integer | N |  |
| ResourceCacheCluster.resourceGroup | string | N |  |
| ResourceCacheCluster.sepcId | integer | N |  |
| ResourceCacheCluster.status | string | N |  |
| ResourceCacheCluster.taskId | integer | N |  |
| ResourceCacheCluster.tenantCode | string | N |  |
| ResourceCacheCluster.tenantId | string | N |  |
| ResourceCacheCluster.tenantRegion | string | N |  |
| ResourceCacheCluster.type | string | N |  |
| ResourceCacheCluster.CacheNode | array[ResourceCacheNode] | N |  |
| ResourceCacheNode.memory | integer | N |  |
| ResourceCacheNode.cpu | integer | N |  |
| ResourceCacheNode.templateId | integer | N |  |
| ResourceCacheNode.templateName | string | N |  |
| ResourceCacheNode.dataVolumeSize | integer | N |  |
| ResourceCacheNode.regionName | string | N |  |
| ResourceCacheNode.regionCode | string | N |  |
| ResourceCacheNode.tenantAzCode | string | N |  |
| ResourceCacheNode.tenantAzName | string | N |  |
| ResourceCacheNode.azName | string | N |  |
| ResourceCacheNode.azCode | string | N |  |
| ResourceCacheNode.id | integer | N |  |
| ResourceCacheNode.taskId | integer | N |  |
| ResourceCacheNode.specId | integer | N |  |
| ResourceCacheNode.clusterId | integer | N |  |
| ResourceCacheNode.ip | string | N |  |
| ResourceCacheNode.status | string | N |  |
| ResourceCacheNode.port | string | N |  |
| ResourceCacheNode.role | string | N |  |
| ResourceCacheNode.master_ip | string | N |  |
| ResourceCacheNode.master_port | string | N |  |
| ResourceCacheNode.vmId | integer | N |  |
| ResourceCacheNode.resourceGroup | string | N |  |
| ResourceCacheNode.networkZone | string | N |  |
| ResourceCacheNode.tenantId | string | N |  |
| cacheClusterList | array[ResourceCacheCluster] | N |  |
| pemVpn | object | N |  |
| VpnInfo.mesh | object | N |  |
| MeshSpec.agentId | integer | N |  |
| MeshSpec.code | string | N |  |
| MeshSpec.description | string | N |  |
| MeshSpec.harborId | integer | N |  |
| MeshSpec.id | integer | N |  |
| MeshSpec.maxConnection | integer | N |  |
| MeshSpec.name | string | N |  |
| MeshSpec.networkZone | string | N |  |
| MeshSpec.occupied | string | N |  |
| MeshSpec.password | string | N |  |
| MeshSpec.property | string | N |  |
| MeshSpec.resourceGroup | string | N |  |
| MeshSpec.role | string | N |  |
| MeshSpec.siteId | integer | N |  |
| MeshSpec.specId | integer | N |  |
| MeshSpec.status | string | N |  |
| MeshSpec.taskId | integer | N |  |
| MeshSpec.tenantId | string | N |  |
| MeshSpec.type | string | N |  |
| MeshSpec.username | string | N |  |
| MeshSpec.vmTemplateId | integer | N |  |
| MeshSpec.meshNode | array[MeshNodeInfo] | N |  |
| MeshNodeInfo.templateId | integer | N |  |
| MeshNodeInfo.templateName | string | N |  |
| MeshNodeInfo.dataVolumeSize | integer | N |  |
| MeshNodeInfo.regionName | string | N |  |
| MeshNodeInfo.regionCode | string | N |  |
| MeshNodeInfo.tenantAzName | string | N |  |
| MeshNodeInfo.tenantAzCode | string | N |  |
| MeshNodeInfo.azName | string | N |  |
| MeshNodeInfo.azCode | string | N |  |
| MeshNodeInfo.id | integer | N |  |
| MeshNodeInfo.taskId | integer | N |  |
| MeshNodeInfo.specId | integer | N |  |
| MeshNodeInfo.meshId | integer | N |  |
| MeshNodeInfo.meshCode | string | N |  |
| MeshNodeInfo.ip | string | N |  |
| MeshNodeInfo.role | string | N |  |
| MeshNodeInfo.adminPort | integer | N |  |
| MeshNodeInfo.workerPort | integer | N |  |
| MeshNodeInfo.tenantId | string | N |  |
| MeshNodeInfo.resourceGroup | string | N |  |
| MeshNodeInfo.vmId | integer | N |  |
| MeshNodeInfo.status | string | N |  |
| MeshNodeInfo.routerName | string | N |  |
| MeshNodeInfo.description | string | N |  |
| VpnInfo.vpn | object | N |  |
| rdbGroup | object | N |  |
| RDBGroupInfo.rdbCluster | object | N |  |
| ResourceRdbCluster.id | integer | N |  |
| ResourceRdbCluster.clusterId | string | N |  |
| ResourceRdbCluster.clusterName | string | N |  |
| ResourceRdbCluster.clusterType | string | N |  |
| ResourceRdbCluster.networkZone | string | N |  |
| ResourceRdbCluster.occupied | string | N |  |
| ResourceRdbCluster.property | string | N |  |
| ResourceRdbCluster.region | string | N |  |
| ResourceRdbCluster.resourceGroup | string | N |  |
| ResourceRdbCluster.specId | integer | N |  |
| ResourceRdbCluster.status | string | N |  |
| ResourceRdbCluster.taskId | integer | N |  |
| ResourceRdbCluster.tenantId | string | N |  |
| ResourceRdbCluster.updater | string | N |  |
| ResourceRdbCluster.updaterId | string | N |  |
| ResourceRdbCluster.version | string | N |  |
| ResourceRdbCluster.vmTemplateId | integer | N |  |
| ResourceRdbCluster.RDBGroup | array[ResourceRdbGroup] | N |  |
| ResourceRdbGroup.id | integer | N |  |
| ResourceRdbGroup.taskId | integer | N |  |
| ResourceRdbGroup.sepcId | integer | N |  |
| ResourceRdbGroup.region | string | N |  |
| ResourceRdbGroup.resourceGroup | string | N |  |
| ResourceRdbGroup.networkZone | string | N |  |
| ResourceRdbGroup.rdbGroupCode | string | N |  |
| ResourceRdbGroup.clusterId | integer | N |  |
| ResourceRdbGroup.clusterCode | string | N |  |
| ResourceRdbGroup.harborId | integer | N |  |
| ResourceRdbGroup.pemId | integer | N |  |
| ResourceRdbGroup.vip | string | N |  |
| ResourceRdbGroup.az | string | N |  |
| ResourceRdbGroup.vport | integer | N |  |
| ResourceRdbGroup.type | string | N |  |
| ResourceRdbGroup.purpose | string | N |  |
| ResourceRdbGroup.name | string | N |  |
| ResourceRdbGroup.description | string | N |  |
| RDBGroupInfo.rdbGroup | object | N |  |
| aemVpnList | array[VpnInfo] | N |  |

**Range of values**

response example:

```
{
    "networkZone": {
        "id": 0,
        "taskId": 0,
        "specId": 0,
        "name": "name",
        "code": "code",
        "tenantRegionId": 0,
        "tenantVpcId": 0,
        "resourceGroupId": 0,
        "azId": 0,
        "serialNumber": 0,
        "tenantId": "tenantId",
        "tenantCode": "tenantCode",
        "status": "status",
        "description": "description"
    },
    "k8sCluster": {
        "id": 0,
        "agentId": 0,
        "clusterCidr": "clusterCidr",
        "clusterCode": "clusterCode",
        "clusterName": "clusterName",
        "harbor": "harbor",
        "harborId": 0,
        "k8sConfigId": 0,
        "network": "network",
        "networkZone": "networkZone",
        "nodePortRange": "nodePortRange",
        "pemId": 0,
        "regionId": 0,
        "resourceGroup": "resourceGroup",
        "securityGroup": "securityGroup",
        "sepcId": 0,
        "serviceCidr": "serviceCidr",
        "status": "status",
        "suInstance": "suInstance",
        "taskId": 0,
        "tenantCode": "tenantCode",
        "tenantId": "tenantId",
        "tenantRegion": "tenantRegion",
        "type": "type",
        "K8SNode": [
            {
                "id": 0,
                "templateId": 0,
                "templateName": "templateName",
                "dataVolumeSize": 0,
                "regionName": "regionName",
                "regionCode": "regionCode",
                "tenantAzName": "tenantAzName",
                "azName": "azName",
                "azCode": "azCode",
                "memory": 0,
                "cpu": 0,
                "rootVolumeSize": 0,
                "disk": 0,
                "taskId": 0,
                "sepcId": 0,
                "orderNumber": 0,
                "clusterId": 0,
                "clusterCode": "clusterCode",
                "region": "region",
                "resourceGroup": "resourceGroup",
                "networkZone": "networkZone",
                "availabilityZone": "availabilityZone",
                "ip": "ip",
                "status": "status",
                "role": "role",
                "drain": "drain",
                "schedule": "schedule",
                "vmId": 0,
                "tenantId": "tenantId"
            }
        ]
    },
    "cacheCluster": {
        "id": 0,
        "clusterCode": "clusterCode",
        "clusterName": "clusterName",
        "harborId": 0,
        "networkZone": "networkZone",
        "occupied": "occupied",
        "password": "password",
        "pemId": 0,
        "property": "property",
        "regionId": 0,
        "resourceGroup": "resourceGroup",
        "sepcId": 0,
        "status": "status",
        "taskId": 0,
        "tenantCode": "tenantCode",
        "tenantId": "tenantId",
        "tenantRegion": "tenantRegion",
        "type": "type",
        "CacheNode": [
            {
                "memory": 0,
                "cpu": 0,
                "templateId": 0,
                "templateName": "templateName",
                "dataVolumeSize": 0,
                "regionName": "regionName",
                "regionCode": "regionCode",
                "tenantAzCode": "tenantAzCode",
                "tenantAzName": "tenantAzName",
                "azName": "azName",
                "azCode": "azCode",
                "id": 0,
                "taskId": 0,
                "specId": 0,
                "clusterId": 0,
                "ip": "ip",
                "status": "status",
                "port": "port",
                "role": "role",
                "master_ip": "master_ip",
                "master_port": "master_port",
                "vmId": 0,
                "resourceGroup": "resourceGroup",
                "networkZone": "networkZone",
                "tenantId": "tenantId"
            }
        ]
    },
    "cacheClusterList": [
        {
        }
    ],
    "pemVpn": {
        "mesh": {
            "agentId": 0,
            "code": "code",
            "description": "description",
            "harborId": 0,
            "id": 0,
            "maxConnection": 0,
            "name": "name",
            "networkZone": "networkZone",
            "occupied": "occupied",
            "password": "password",
            "property": "property",
            "resourceGroup": "resourceGroup",
            "role": "role",
            "siteId": 0,
            "specId": 0,
            "status": "status",
            "taskId": 0,
            "tenantId": "tenantId",
            "type": "type",
            "username": "username",
            "vmTemplateId": 0,
            "meshNode": [
                {
                    "templateId": 0,
                    "templateName": "templateName",
                    "dataVolumeSize": 0,
                    "regionName": "regionName",
                    "regionCode": "regionCode",
                    "tenantAzName": "tenantAzName",
                    "tenantAzCode": "tenantAzCode",
                    "azName": "azName",
                    "azCode": "azCode",
                    "id": 0,
                    "taskId": 0,
                    "specId": 0,
                    "meshId": 0,
                    "meshCode": "meshCode",
                    "ip": "ip",
                    "role": "role",
                    "adminPort": 0,
                    "workerPort": 0,
                    "tenantId": "tenantId",
                    "resourceGroup": "resourceGroup",
                    "vmId": 0,
                    "status": "status",
                    "routerName": "routerName",
                    "description": "description"
                }
            ]
        },
        "vpn": {
        }
    },
    "rdbGroup": {
        "rdbCluster": {
            "id": 0,
            "clusterId": "clusterId",
            "clusterName": "clusterName",
            "clusterType": "clusterType",
            "networkZone": "networkZone",
            "occupied": "occupied",
            "property": "property",
            "region": "region",
            "resourceGroup": "resourceGroup",
            "specId": 0,
            "status": "status",
            "taskId": 0,
            "tenantId": "tenantId",
            "updater": "updater",
            "updaterId": "updaterId",
            "version": "version",
            "vmTemplateId": 0,
            "RDBGroup": [
                {
                    "id": 0,
                    "taskId": 0,
                    "sepcId": 0,
                    "region": "region",
                    "resourceGroup": "resourceGroup",
                    "networkZone": "networkZone",
                    "rdbGroupCode": "rdbGroupCode",
                    "clusterId": 0,
                    "clusterCode": "clusterCode",
                    "harborId": 0,
                    "pemId": 0,
                    "vip": "vip",
                    "az": "az",
                    "vport": 0,
                    "type": "type",
                    "purpose": "purpose",
                    "name": "name",
                    "description": "description"
                }
            ]
        },
        "rdbGroup": {
        }
    },
    "aemVpnList": [
        {
        }
    ]
}
```



# **suinstance - update - API**

## URL: /environment/v1/suinstance/update ##

## Method : Post ##

## Event ID : EnvSuInstanceUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |
| suId | integer |  | N |  |
| name | string |  | N |  |
| code | string |  | N |  |
| description | string |  | N |  |
| relations | array[ModifyObjRelation] |  | N |  |
| ModifyObjRelation.operate | integer |  | N |  |
| ModifyObjRelation.id | integer |  | Y | required_without:SrcCiName,SrcObjId,TarCiName,TarObjId |
| ModifyObjRelation.srcCiName | string |  | Y | required_if:Operate,0 |
| ModifyObjRelation.srcObjId | integer |  | Y | required_if:Operate,0 |
| ModifyObjRelation.tarCiName | string |  | Y | required_if:Operate,0 |
| ModifyObjRelation.tarObjId | integer |  | Y | required_if:Operate,0 |
| ModifyObjRelation.relationMod | integer |  | N |  |
| ModifyObjRelation.data | string |  | N |  |

**Range of values**

request example:
```
{
    "id": 0,
    "suId": 0,
    "name": "name",
    "code": "code",
    "description": "description",
    "relations": [
        {
            "operate": 0,
            "id": 0,
            "srcCiName": "srcCiName",
            "srcObjId": 0,
            "tarCiName": "tarCiName",
            "tarObjId": 0,
            "relationMod": 0,
            "data": "data"
        }
    ]
}
```

**struct example:**

```
type UpdateSuInstanceRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
    SuId uint64 `json:"suId"`
    Name string `json:"name"`
    Code string `json:"code"`
    Description string `json:"description"`
    Relations []struct {
            Operate int `json:"operate"`
            Id uint64 `json:"id" validate:"required_without:SrcCiName,SrcObjId,TarCiName,TarObjId"`
            SrcCiName string `json:"srcCiName" validate:"required_if:Operate,0"`
            SrcObjId uint64 `json:"srcObjId" validate:"required_if:Operate,0"`
            TarCiName string `json:"tarCiName" validate:"required_if:Operate,0"`
            TarObjId uint64 `json:"tarObjId" validate:"required_if:Operate,0"`
            RelationMod uint64 `json:"relationMod"`
            Data string `json:"data"`
        } `json:"relations"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


