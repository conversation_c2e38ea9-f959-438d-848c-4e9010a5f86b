
# **metadata - init - API**

## URL: /environment/v1/metadata/init ##

## Method : Post ##

## Event ID : EnvMetadataInit ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| workspaceId | string |  | N | omitempty,name,max=64 |
| packageId | integer |  | N | omitempty,gte=0 |
| code | string |  | N | name,max=64 |
| name | string |  | N | omitempty,max=64 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "workspaceId": "workspaceId",
    "packageId": 0,
    "code": "code",
    "name": "name",
    "description": "description"
}
```

**struct example:**

```
type InitMetadataRequest struct {
    WorkspaceId string `json:"workspaceId" validate:"omitempty,name,max=64"`
    PackageId uint64 `json:"packageId" validate:"omitempty,gte=0"`
    Code string `json:"code" validate:"name,max=64"`
    Name string `json:"name" validate:"omitempty,max=64"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **metadata - reload - API**

## URL: /environment/v1/metadata/reload ##

## Method : Post ##

## Event ID : EnvMetadataReload ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| workspaceId | string |  | N | omitempty,name,max=64 |
| packageId | integer |  | N | omitempty,gte=0 |
| code | string |  | N | name,max=64 |
| name | string |  | N | omitempty,max=64 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "workspaceId": "workspaceId",
    "packageId": 0,
    "code": "code",
    "name": "name",
    "description": "description"
}
```

**struct example:**

```
type InitMetadataRequest struct {
    WorkspaceId string `json:"workspaceId" validate:"omitempty,name,max=64"`
    PackageId uint64 `json:"packageId" validate:"omitempty,gte=0"`
    Code string `json:"code" validate:"name,max=64"`
    Name string `json:"name" validate:"omitempty,max=64"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


