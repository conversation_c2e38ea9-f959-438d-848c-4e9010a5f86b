
# **plan - autoscan - API**

## URL: /environment/v1/plan/autoscan ##

## Method : Post ##

## Event ID : EnvPlanAutoScan ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |
| suTypeID | integer |  | Y | gt=0,required |
| suTypeCode | string |  | Y | required |
| serviceType | string |  | N |  |
| suID | integer |  | Y | gt=0,required |
| suInstanceID | integer |  | Y | gt=0,required |
| suTypeIdList | array[uint64] |  | N |  |
| suIdList | array[uint64] |  | N |  |
| suInstanceIdList | array[uint64] |  | N |  |

**Range of values**

request example:
```
{
    "id": 0,
    "suTypeID": 0,
    "suTypeCode": "suTypeCode",
    "serviceType": "serviceType",
    "suID": 0,
    "suInstanceID": 0,
    "suTypeIdList": [
    ],
    "suIdList": [
    ],
    "suInstanceIdList": [
    ]
}
```

**struct example:**

```
type PlanAutoScanRequest struct {
    Id uint64 `json:"id"`
    SuTypeID uint64 `json:"suTypeID" validate:"gt=0,required"`
    SuTypeCode string `json:"suTypeCode" validate:"required"`
    ServiceType string `json:"serviceType"`
    SuID uint64 `json:"suID" validate:"gt=0,required"`
    SuInstanceID uint64 `json:"suInstanceID" validate:"gt=0,required"`
    SuTypeIdList []uint64 `json:"suTypeIdList"`
    SuIdList []uint64 `json:"suIdList"`
    SuInstanceIdList []uint64 `json:"suInstanceIdList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - create - API**

## URL: /environment/v1/plan/create ##

## Method : Post ##

## Event ID : EnvPlanCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| batchId | integer |  | Y | required,gte=0 |
| versionId | integer |  | N | omitempty,gte=0 |
| version | string |  | N | omitempty,gte=0 |
| stackId | integer |  | Y | required,gt=0 |
| environmentId | integer |  | N | omitempty,gte=0 |
| suId | integer |  | N | omitempty,gte=0 |
| suTypeId | integer |  | N | omitempty,gte=0 |
| serviceType | string |  | N | omitempty,name,max=32 |
| suInstanceId | integer |  | N | omitempty,gte=0 |
| suTypeCode | string |  | N | omitempty,name,max=32 |
| name | string |  | N | max=64 |
| type | string |  | N | name,max=32 |
| mode | string |  | N | name,max=32 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "batchId": 0,
    "versionId": 0,
    "version": "version",
    "stackId": 0,
    "environmentId": 0,
    "suId": 0,
    "suTypeId": 0,
    "serviceType": "serviceType",
    "suInstanceId": 0,
    "suTypeCode": "suTypeCode",
    "name": "name",
    "type": "type",
    "mode": "mode",
    "description": "description"
}
```

**struct example:**

```
type CreatePlanRequest struct {
    BatchId uint64 `json:"batchId" validate:"required,gte=0"`
    VersionId uint64 `json:"versionId" validate:"omitempty,gte=0"`
    Version string `json:"version" validate:"omitempty,gte=0"`
    StackId uint64 `json:"stackId" validate:"required,gt=0"`
    EnvironmentId uint64 `json:"environmentId" validate:"omitempty,gte=0"`
    SuId uint64 `json:"suId" validate:"omitempty,gte=0"`
    SuTypeId uint64 `json:"suTypeId" validate:"omitempty,gte=0"`
    ServiceType string `json:"serviceType" validate:"omitempty,name,max=32"`
    SuInstanceId uint64 `json:"suInstanceId" validate:"omitempty,gte=0"`
    SuTypeCode string `json:"suTypeCode" validate:"omitempty,name,max=32"`
    Name string `json:"name" validate:"max=64"`
    Type string `json:"type" validate:"name,max=32"`
    Mode string `json:"mode" validate:"name,max=32"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - db add - API**

## URL: /environment/v1/plan/db/add ##

## Method : Post ##

## Event ID : EnvPlanAddDatabases ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| planId | integer |  | Y | gt=0,required |
| suTypeId | integer |  | Y | gt=0,required |
| suTypeCode | string |  | Y | required |
| suId | integer |  | Y | gt=0,required |
| suCode | string |  | Y | required |
| operate | string |  | N |  |
| dbs | array[DatabaseVersionInfo] |  | Y | gt=0,dive,required |
| DatabaseVersionInfo.id | integer |  | N |  |
| DatabaseVersionInfo.name | string |  | N | name,max=64 |
| DatabaseVersionInfo.uuid | string |  | Y | required |
| DatabaseVersionInfo.version | string |  | N | max=64 |
| DatabaseVersionInfo.tables | array[TableVersionInfo] |  | N |  |
| TableVersionInfo.id | integer |  | N |  |
| TableVersionInfo.name | string |  | N | name,max=64 |
| TableVersionInfo.uuid | string |  | Y | required |
| TableVersionInfo.version | string |  | N | max=64 |

**Range of values**

request example:
```
{
    "planId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "suId": 0,
    "suCode": "suCode",
    "operate": "operate",
    "dbs": [
        {
            "id": 0,
            "name": "name",
            "uuid": "uuid",
            "version": "version",
            "tables": [
                {
                    "id": 0,
                    "name": "name",
                    "uuid": "uuid",
                    "version": "version"
                }
            ]
        }
    ]
}
```

**struct example:**

```
type AddDatabaseRequest struct {
    PlanId uint64 `json:"planId" validate:"gt=0,required"`
    SuTypeId uint64 `json:"suTypeId" validate:"gt=0,required"`
    SuTypeCode string `json:"suTypeCode" validate:"required"`
    SuId uint64 `json:"suId" validate:"gt=0,required"`
    SuCode string `json:"suCode" validate:"required"`
    Operate string `json:"operate"`
    Dbs []struct {
            Id uint64 `json:"id"`
            Name string `json:"name" validate:"name,max=64"`
            Uuid string `json:"uuid" validate:"required"`
            Version string `json:"version" validate:"max=64"`
            Tables []struct {
                    Id uint64 `json:"id"`
                    Name string `json:"name" validate:"name,max=64"`
                    Uuid string `json:"uuid" validate:"required"`
                    Version string `json:"version" validate:"max=64"`
                } `json:"tables"`
        } `json:"dbs" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - db delete - API**

## URL: /environment/v1/plan/db/delete ##

## Method : Post ##

## Event ID : EnvPlanDeleteDatabases ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| databases | array[uint64] |  | Y | gt=0,dive,required |

**Range of values**

request example:
```
{
    "databases": [
    ]
}
```

**struct example:**

```
type DeleteDBRequest struct {
    Databases []uint64 `json:"databases" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - db deploy - API**

## URL: /environment/v1/plan/db/deploy ##

## Method : Post ##

## Event ID : EnvPlanDbDeploy ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| environmentId | integer |  | Y | required,gte=0 |
| mode | string |  | N | omitempty,name,max=32 |
| tableList | array[DbTableElement] |  | Y | gt=0,dive,required |
| DbTableElement.suId | integer |  | Y | required |
| DbTableElement.suCode | string |  | Y | required |
| DbTableElement.suTypeCode | string |  | Y | required |
| DbTableElement.rdbClusterId | integer |  | Y | required |
| DbTableElement.rdbClusterCode | string |  | Y | required |
| DbTableElement.databaseId | integer |  | Y | required |
| DbTableElement.database | string |  | Y | required |
| DbTableElement.tableId | integer |  | N | omitempty |
| DbTableElement.table | string |  | N | omitempty |
| DbTableElement.databaseUuid | string |  | Y | required |
| DbTableElement.tableUuid | string |  | N | omitempty |
| DbTableElement.lastSql | string |  | N | omitempty |
| DbTableElement.currentSql | string |  | N | omitempty |
| DbTableElement.alterSql | string |  | N | omitempty |
| DbTableElement.rollbackSql | string |  | N | omitempty |
| DbTableElement.tableDescription | string |  | N | omitempty |
| operate | string |  | N |  |
| executeFlag | boolean |  | N |  |

**Range of values**

request example:
```
{
    "environmentId": 0,
    "mode": "mode",
    "tableList": [
        {
            "suId": 0,
            "suCode": "suCode",
            "suTypeCode": "suTypeCode",
            "rdbClusterId": 0,
            "rdbClusterCode": "rdbClusterCode",
            "databaseId": 0,
            "database": "database",
            "tableId": 0,
            "table": "table",
            "databaseUuid": "databaseUuid",
            "tableUuid": "tableUuid",
            "lastSql": "lastSql",
            "currentSql": "currentSql",
            "alterSql": "alterSql",
            "rollbackSql": "rollbackSql",
            "tableDescription": "tableDescription"
        }
    ],
    "operate": "operate",
    "executeFlag": false
}
```

**struct example:**

```
type DeployPlanDbTablesRequest struct {
    EnvironmentId uint64 `json:"environmentId" validate:"required,gte=0"`
    Mode string `json:"mode" validate:"omitempty,name,max=32"`
    TableList []struct {
            SuId uint64 `json:"suId" validate:"required"`
            SuCode string `json:"suCode" validate:"required"`
            SuTypeCode string `json:"suTypeCode" validate:"required"`
            RdbClusterId uint64 `json:"rdbClusterId" validate:"required"`
            RdbClusterCode string `json:"rdbClusterCode" validate:"required"`
            DatabaseId uint64 `json:"databaseId" validate:"required"`
            Database string `json:"database" validate:"required"`
            TableId uint64 `json:"tableId" validate:"omitempty"`
            Table string `json:"table" validate:"omitempty"`
            DatabaseUuid string `json:"databaseUuid" validate:"required"`
            TableUuid string `json:"tableUuid" validate:"omitempty"`
            LastSql string `json:"lastSql" validate:"omitempty"`
            CurrentSql string `json:"currentSql" validate:"omitempty"`
            AlterSql string `json:"alterSql" validate:"omitempty"`
            RollbackSql string `json:"rollbackSql" validate:"omitempty"`
            TableDescription string `json:"tableDescription" validate:"omitempty"`
        } `json:"tableList" validate:"gt=0,dive,required"`
    Operate string `json:"operate"`
    ExecuteFlag bool `json:"executeFlag"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - db list - API**

## URL: /environment/v1/plan/db/list ##

## Method : Post ##

## Event ID : EnvPlanDatabases ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| planId | integer |  | N |  |
| tenantId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "planId": 0,
    "tenantId": "tenantId"
}
```

**struct example:**

```
type DatabaseRefFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    PlanId uint64 `json:"planId"`
    TenantId string `json:"tenantId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **plan - db page - API**

## URL: /environment/v1/plan/db/page ##

## Method : Post ##

## Event ID : EnvPlanDbPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| environmentId | integer |  | N |  |
| suId | integer |  | N |  |
| suIdList | array[uint64] |  | N |  |
| dbName | string |  | N |  |
| tableName | string |  | N |  |
| type | string |  | N |  |
| orderBy | string |  | N | omitempty,max=128 |
| sort | integer |  | N | oneof=0 1 |
| operate | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "environmentId": 0,
    "suId": 0,
    "suIdList": [
    ],
    "dbName": "dbName",
    "tableName": "tableName",
    "type": "type",
    "orderBy": "orderBy",
    "sort": 0,
    "operate": "operate"
}
```

**struct example:**

```
type DbTableFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    EnvironmentId uint64 `json:"environmentId"`
    SuId uint64 `json:"suId"`
    SuIdList []uint64 `json:"suIdList"`
    DbName string `json:"dbName"`
    TableName string `json:"tableName"`
    Type string `json:"type"`
    OrderBy string `json:"orderBy" validate:"omitempty,max=128"`
    Sort int `json:"sort" validate:"oneof=0 1"`
    Operate string `json:"operate"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - dbi add - API**

## URL: /environment/v1/plan/dbi/add ##

## Method : Post ##

## Event ID : EnvPlanAddDBInstance ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| planId | integer |  | Y | gt=0,required |
| suTypeId | integer |  | Y | gt=0,required |
| suTypeCode | string |  | Y | required |
| suId | integer |  | Y | gt=0,required |
| suCode | string |  | Y | required |
| operate | string |  | N |  |
| dbs | array[DatabaseVersionInfo] |  | Y | gt=0,dive,required |
| DatabaseVersionInfo.id | integer |  | N |  |
| DatabaseVersionInfo.name | string |  | N | name,max=64 |
| DatabaseVersionInfo.uuid | string |  | Y | required |
| DatabaseVersionInfo.version | string |  | N | max=64 |
| DatabaseVersionInfo.tables | array[TableVersionInfo] |  | N |  |
| TableVersionInfo.id | integer |  | N |  |
| TableVersionInfo.name | string |  | N | name,max=64 |
| TableVersionInfo.uuid | string |  | Y | required |
| TableVersionInfo.version | string |  | N | max=64 |

**Range of values**

request example:
```
{
    "planId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "suId": 0,
    "suCode": "suCode",
    "operate": "operate",
    "dbs": [
        {
            "id": 0,
            "name": "name",
            "uuid": "uuid",
            "version": "version",
            "tables": [
                {
                    "id": 0,
                    "name": "name",
                    "uuid": "uuid",
                    "version": "version"
                }
            ]
        }
    ]
}
```

**struct example:**

```
type AddDatabaseRequest struct {
    PlanId uint64 `json:"planId" validate:"gt=0,required"`
    SuTypeId uint64 `json:"suTypeId" validate:"gt=0,required"`
    SuTypeCode string `json:"suTypeCode" validate:"required"`
    SuId uint64 `json:"suId" validate:"gt=0,required"`
    SuCode string `json:"suCode" validate:"required"`
    Operate string `json:"operate"`
    Dbs []struct {
            Id uint64 `json:"id"`
            Name string `json:"name" validate:"name,max=64"`
            Uuid string `json:"uuid" validate:"required"`
            Version string `json:"version" validate:"max=64"`
            Tables []struct {
                    Id uint64 `json:"id"`
                    Name string `json:"name" validate:"name,max=64"`
                    Uuid string `json:"uuid" validate:"required"`
                    Version string `json:"version" validate:"max=64"`
                } `json:"tables"`
        } `json:"dbs" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - delete - API**

## URL: /environment/v1/plan/delete ##

## Method : Post ##

## Event ID : EnvPlanDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type planIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - deploy - API**

## URL: /environment/v1/plan/deploy ##

## Method : Post ##

## Event ID : EnvPlanDeploy ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| environmentId | integer |  | Y | required,gte=0 |
| mode | string |  | N | omitempty,name,max=32 |
| serviceList | array[DeployElement] |  | Y | gt=0,dive,required |
| DeployElement.suId | integer |  | N | omitempty,gte=0 |
| DeployElement.serviceInstanceID | integer |  | N |  |
| DeployElement.releaseID | integer |  | N |  |
| DeployElement.category | string |  | N | omitempty,name,max=32 |
| DeployElement.suInstanceId | integer |  | N | omitempty,gte=0 |
| DeployElement.suInstanceCode | string |  | N | omitempty,name,max=32 |
| DeployElement.suTypeCode | string |  | N | omitempty,name,max=32 |
| DeployElement.entity | string |  | N | max=64 |
| DeployElement.entityType | string |  | N | name,max=32 |
| DeployElement.imageName | string |  | N |  |
| DeployElement.imageVersion | string |  | N |  |
| DeployElement.description | string |  | N | omitempty,max=255 |
| DeployElement.datas | array[RuntimeConfigElement] |  | N |  |
| RuntimeConfigElement.path | string |  | N | min=2,max=128 |
| RuntimeConfigElement.value | string |  | N | omitempty,max=255 |
| RuntimeConfigElement.label | string |  | N | omitempty,max=255 |
| gitlabVersion | string |  | N |  |
| operate | string |  | N |  |
| executeFlag | boolean |  | N |  |
| redeployFlag | boolean |  | N |  |

**Range of values**

request example:
```
{
    "environmentId": 0,
    "mode": "mode",
    "serviceList": [
        {
            "suId": 0,
            "serviceInstanceID": 0,
            "releaseID": 0,
            "category": "category",
            "suInstanceId": 0,
            "suInstanceCode": "suInstanceCode",
            "suTypeCode": "suTypeCode",
            "entity": "entity",
            "entityType": "entityType",
            "imageName": "imageName",
            "imageVersion": "imageVersion",
            "description": "description",
            "datas": [
                {
                    "path": "path",
                    "value": "value",
                    "label": "label"
                }
            ]
        }
    ],
    "gitlabVersion": "gitlabVersion",
    "operate": "operate",
    "executeFlag": false,
    "redeployFlag": false
}
```

**struct example:**

```
type DeployPlanServicesRequest struct {
    EnvironmentId uint64 `json:"environmentId" validate:"required,gte=0"`
    Mode string `json:"mode" validate:"omitempty,name,max=32"`
    ServiceList []struct {
            SuId uint64 `json:"suId" validate:"omitempty,gte=0"`
            ServiceInstanceID uint64 `json:"serviceInstanceID"`
            ReleaseID uint64 `json:"releaseID"`
            Category string `json:"category" validate:"omitempty,name,max=32"`
            SuInstanceId uint64 `json:"suInstanceId" validate:"omitempty,gte=0"`
            SuInstanceCode string `json:"suInstanceCode" validate:"omitempty,name,max=32"`
            SuTypeCode string `json:"suTypeCode" validate:"omitempty,name,max=32"`
            Entity string `json:"entity" validate:"max=64"`
            EntityType string `json:"entityType" validate:"name,max=32"`
            ImageName string `json:"imageName"`
            ImageVersion string `json:"imageVersion"`
            Description string `json:"description" validate:"omitempty,max=255"`
            Datas []struct {
                    Path string `json:"path" validate:"min=2,max=128"`
                    Value string `json:"value" validate:"omitempty,max=255"`
                    Label string `json:"label" validate:"omitempty,max=255"`
                } `json:"datas"`
        } `json:"serviceList" validate:"gt=0,dive,required"`
    GitlabVersion string `json:"gitlabVersion"`
    Operate string `json:"operate"`
    ExecuteFlag bool `json:"executeFlag"`
    RedeployFlag bool `json:"redeployFlag"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - detail - API**

## URL: /environment/v1/plan/detail ##

## Method : Post ##

## Event ID : EnvPlanDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type planIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| batchId | integer | N |  |
| versionId | integer | N |  |
| stackId | integer | N |  |
| packageId | integer | N |  |
| envId | integer | N |  |
| suTypeId | integer | N |  |
| suTypeCode | string | N |  |
| serviceType | string | N |  |
| suId | integer | N |  |
| suInstanceId | integer | N |  |
| code | string | N |  |
| name | string | N |  |
| type | string | N |  |
| matchMode | string | N |  |
| status | string | N |  |
| version | string | N |  |
| imageVersion | string | N |  |
| parameter | string | N |  |
| description | string | N |  |
| tenantId | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "batchId": 0,
    "versionId": 0,
    "stackId": 0,
    "packageId": 0,
    "envId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "serviceType": "serviceType",
    "suId": 0,
    "suInstanceId": 0,
    "code": "code",
    "name": "name",
    "type": "type",
    "matchMode": "matchMode",
    "status": "status",
    "version": "version",
    "imageVersion": "imageVersion",
    "parameter": "parameter",
    "description": "description",
    "tenantId": "tenantId",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **plan - end - API**

## URL: /environment/v1/plan/end ##

## Method : Post ##

## Event ID : EnvPlanEnd ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N | gt=0 |
| stackInstanceId | integer |  | N | gte=0 |
| stackCode | string |  | N | name,max=64 |
| serviceInstanceList | array[uint64] |  | Y | gt=0,dive,required |

**Range of values**

request example:
```
{
    "id": 0,
    "stackInstanceId": 0,
    "stackCode": "stackCode",
    "serviceInstanceList": [
    ]
}
```

**struct example:**

```
type EndPlanRequest struct {
    Id uint64 `json:"id" validate:"gt=0"`
    StackInstanceId uint64 `json:"stackInstanceId" validate:"gte=0"`
    StackCode string `json:"stackCode" validate:"name,max=64"`
    ServiceInstanceList []uint64 `json:"serviceInstanceList" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - execute - API**

## URL: /environment/v1/plan/execute ##

## Method : Post ##

## Event ID : EnvPlanExecute ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type planIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - list - API**

## URL: /environment/v1/plan/list ##

## Method : Post ##

## Event ID : EnvPlanList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| batchId | integer |  | N |  |
| versionId | integer |  | N |  |
| stackId | integer |  | N |  |
| code | string |  | N |  |
| tenantId | string |  | N |  |
| envId | integer |  | N |  |
| planType | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "batchId": 0,
    "versionId": 0,
    "stackId": 0,
    "code": "code",
    "tenantId": "tenantId",
    "envId": 0,
    "planType": "planType"
}
```

**struct example:**

```
type PlanFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    BatchId uint64 `json:"batchId"`
    VersionId uint64 `json:"versionId"`
    StackId uint64 `json:"stackId"`
    Code string `json:"code"`
    TenantId string `json:"tenantId"`
    EnvId uint64 `json:"envId"`
    PlanType string `json:"planType"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| batchId | integer | N |  |
| versionId | integer | N |  |
| stackId | integer | N |  |
| packageId | integer | N |  |
| envId | integer | N |  |
| suTypeId | integer | N |  |
| suTypeCode | string | N |  |
| serviceType | string | N |  |
| suId | integer | N |  |
| suInstanceId | integer | N |  |
| code | string | N |  |
| name | string | N |  |
| type | string | N |  |
| matchMode | string | N |  |
| status | string | N |  |
| version | string | N |  |
| imageVersion | string | N |  |
| parameter | string | N |  |
| description | string | N |  |
| tenantId | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "batchId": 0,
    "versionId": 0,
    "stackId": 0,
    "packageId": 0,
    "envId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "serviceType": "serviceType",
    "suId": 0,
    "suInstanceId": 0,
    "code": "code",
    "name": "name",
    "type": "type",
    "matchMode": "matchMode",
    "status": "status",
    "version": "version",
    "imageVersion": "imageVersion",
    "parameter": "parameter",
    "description": "description",
    "tenantId": "tenantId",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **plan - mesh add - API**

## URL: /environment/v1/plan/mesh/add ##

## Method : Post ##

## Event ID : EnvPlanAddMesh ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| planId | integer |  | Y | gt=0,required |
| meshList | array[MeshInfo] |  | Y | gt=0,dive,required |
| MeshInfo.id | integer |  | Y | gt=0,required |
| MeshInfo.code | string |  | N | name,max=32 |
| MeshInfo.description | string |  | N | omitempty,max=255 |
| MeshInfo.role | string |  | N |  |

**Range of values**

request example:
```
{
    "planId": 0,
    "meshList": [
        {
            "id": 0,
            "code": "code",
            "description": "description",
            "role": "role"
        }
    ]
}
```

**struct example:**

```
type AddMeshRequest struct {
    PlanId uint64 `json:"planId" validate:"gt=0,required"`
    MeshList []struct {
            Id uint64 `json:"id" validate:"gt=0,required"`
            Code string `json:"code" validate:"name,max=32"`
            Description string `json:"description" validate:"omitempty,max=255"`
            Role string `json:"role"`
        } `json:"meshList" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - mesh delete - API**

## URL: /environment/v1/plan/mesh/delete ##

## Method : Post ##

## Event ID : EnvPlanDeleteMesh ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| meshes | array[uint64] |  | Y | gt=0,dive,required |

**Range of values**

request example:
```
{
    "meshes": [
    ]
}
```

**struct example:**

```
type DeleteMeshRequest struct {
    Meshes []uint64 `json:"meshes" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - mesh list - API**

## URL: /environment/v1/plan/mesh/list ##

## Method : Post ##

## Event ID : EnvPlanMeshes ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| planId | integer |  | N |  |
| status | string |  | N |  |
| tenantId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "planId": 0,
    "status": "status",
    "tenantId": "tenantId"
}
```

**struct example:**

```
type MeshRefFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    PlanId uint64 `json:"planId"`
    Status string `json:"status"`
    TenantId string `json:"tenantId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **plan - page - API**

## URL: /environment/v1/plan/page ##

## Method : Post ##

## Event ID : EnvPlanPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| batchId | integer |  | N |  |
| versionId | integer |  | N |  |
| stackId | integer |  | N |  |
| code | string |  | N |  |
| tenantId | string |  | N |  |
| envId | integer |  | N |  |
| planType | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "batchId": 0,
    "versionId": 0,
    "stackId": 0,
    "code": "code",
    "tenantId": "tenantId",
    "envId": 0,
    "planType": "planType"
}
```

**struct example:**

```
type PlanFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    BatchId uint64 `json:"batchId"`
    VersionId uint64 `json:"versionId"`
    StackId uint64 `json:"stackId"`
    Code string `json:"code"`
    TenantId string `json:"tenantId"`
    EnvId uint64 `json:"envId"`
    PlanType string `json:"planType"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **plan - process - API**

## URL: /environment/v1/plan/process ##

## Method : Post ##

## Event ID : EnvPlanProcess ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |
| suTypeID | integer |  | Y | gt=0,required |
| suTypeCode | string |  | Y | required |
| serviceType | string |  | N |  |
| suID | integer |  | Y | gt=0,required |
| suInstanceID | integer |  | Y | gt=0,required |
| suTypeIdList | array[uint64] |  | N |  |
| suIdList | array[uint64] |  | N |  |
| suInstanceIdList | array[uint64] |  | N |  |

**Range of values**

request example:
```
{
    "id": 0,
    "suTypeID": 0,
    "suTypeCode": "suTypeCode",
    "serviceType": "serviceType",
    "suID": 0,
    "suInstanceID": 0,
    "suTypeIdList": [
    ],
    "suIdList": [
    ],
    "suInstanceIdList": [
    ]
}
```

**struct example:**

```
type PlanAutoScanRequest struct {
    Id uint64 `json:"id"`
    SuTypeID uint64 `json:"suTypeID" validate:"gt=0,required"`
    SuTypeCode string `json:"suTypeCode" validate:"required"`
    ServiceType string `json:"serviceType"`
    SuID uint64 `json:"suID" validate:"gt=0,required"`
    SuInstanceID uint64 `json:"suInstanceID" validate:"gt=0,required"`
    SuTypeIdList []uint64 `json:"suTypeIdList"`
    SuIdList []uint64 `json:"suIdList"`
    SuInstanceIdList []uint64 `json:"suInstanceIdList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - rollback - API**

## URL: /environment/v1/plan/rollback ##

## Method : Post ##

## Event ID : EnvPlanRollback ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type planIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - scan - API**

## URL: /environment/v1/plan/scan ##

## Method : Post ##

## Event ID : EnvPlanScan ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type planIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| name | string | N |  |
| planType | string | N |  |
| matchMode | string | N |  |
| stackName | string | N |  |
| patch | integer | N |  |
| service | integer | N |  |
| publish | integer | N |  |
| instance | integer | N |  |
| upgrade | integer | N |  |

**Range of values**

response example:

```
{
    "name": "name",
    "planType": "planType",
    "matchMode": "matchMode",
    "stackName": "stackName",
    "patch": 0,
    "service": 0,
    "publish": 0,
    "instance": 0,
    "upgrade": 0
}
```



# **plan - service add - API**

## URL: /environment/v1/plan/service/add ##

## Method : Post ##

## Event ID : EnvPlanAddServices ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| planId | integer |  | Y | gt=0,required |
| suTypeId | integer |  | Y | gt=0,required |
| suTypeCode | string |  | Y | required |
| serviceType | string |  | N |  |
| suId | integer |  | Y | gt=0,required |
| suInstanceId | integer |  | Y | gt=0,required |
| services | array[string] |  | N |  |

**Range of values**

request example:
```
{
    "planId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "serviceType": "serviceType",
    "suId": 0,
    "suInstanceId": 0,
    "services": [
    ]
}
```

**struct example:**

```
type AddServicesRequest struct {
    PlanId uint64 `json:"planId" validate:"gt=0,required"`
    SuTypeId uint64 `json:"suTypeId" validate:"gt=0,required"`
    SuTypeCode string `json:"suTypeCode" validate:"required"`
    ServiceType string `json:"serviceType"`
    SuId uint64 `json:"suId" validate:"gt=0,required"`
    SuInstanceId uint64 `json:"suInstanceId" validate:"gt=0,required"`
    Services []string `json:"services"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| planId | integer | N |  |
| stackId | integer | N |  |
| packageId | integer | N |  |
| appId | integer | N |  |
| suTypeId | integer | N |  |
| serviceId | string | N |  |
| service_type | string | N |  |
| meshRole | string | N |  |
| svrType | string | N |  |
| dbUsername | string | N |  |
| dbPassword | string | N |  |
| dbName | array[string] | N |  |
| name | string | N |  |
| code | string | N |  |
| sections | array[string] | N |  |
| publishes | array[WorkspacePublishVersion] | N |  |
| WorkspacePublishVersion.uuid | string | N |  |
| WorkspacePublishVersion.event_uuid | string | N |  |
| WorkspacePublishVersion.event_version | string | N |  |
| WorkspacePublishVersion.mode | string | N |  |
| WorkspacePublishVersion.retry | integer | N |  |
| WorkspacePublishVersion.call_timeout | integer | N |  |
| WorkspacePublishVersion.total_timeout | integer | N |  |
| subscribes | array[WorkspaceUuidVersion] | N |  |
| WorkspaceUuidVersion.uuid | string | N |  |
| WorkspaceUuidVersion.version | string | N |  |
| storages | array[WorkspaceStorage] | N |  |
| WorkspaceStorage.storage_type | string | N |  |
| WorkspaceStorage.database_uuid | string | N |  |
| WorkspaceStorage.tables | array[WorkspaceUuidVersion] | N |  |
| errorcodes | array[WorkspaceUuidVersion] | N |  |
| imageName | string | N |  |
| serviceVersion | string | N |  |
| serviceInstanceId | integer | N |  |
| serviceUuid | string | N |  |
| serviceInstanceVersionId | integer | N |  |
| TargetVersionId | integer | N |  |
| joinMode | string | N |  |
| configure | map[string]interface | N |  |
| status | string | N |  |
| description | string | N |  |
| tenantId | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| Time | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "planId": 0,
    "stackId": 0,
    "packageId": 0,
    "appId": 0,
    "suTypeId": 0,
    "serviceId": "serviceId",
    "service_type": "service_type",
    "meshRole": "meshRole",
    "svrType": "svrType",
    "dbUsername": "dbUsername",
    "dbPassword": "dbPassword",
    "dbName": [
    ],
    "name": "name",
    "code": "code",
    "sections": [
    ],
    "publishes": [
        {
            "uuid": "uuid",
            "event_uuid": "event_uuid",
            "event_version": "event_version",
            "mode": "mode",
            "retry": 0,
            "call_timeout": 0,
            "total_timeout": 0
        }
    ],
    "subscribes": [
        {
            "uuid": "uuid",
            "version": "version"
        }
    ],
    "storages": [
        {
            "storage_type": "storage_type",
            "database_uuid": "database_uuid",
            "tables": [
                {
                }
            ]
        }
    ],
    "errorcodes": [
        {
        }
    ],
    "imageName": "imageName",
    "serviceVersion": "serviceVersion",
    "serviceInstanceId": 0,
    "serviceUuid": "serviceUuid",
    "serviceInstanceVersionId": 0,
    "TargetVersionId": 0,
    "joinMode": "joinMode",
    "configure": {
    },
    "status": "status",
    "description": "description",
    "tenantId": "tenantId",
    "creatorId": "creatorId",
    "creator": "creator",
    "updaterId": "updaterId",
    "updater": "updater",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "Time": "Time"
}
```



# **plan - si add - API**

## URL: /environment/v1/plan/si/add ##

## Method : Post ##

## Event ID : EnvPlanAddSvcInstances ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| planId | integer |  | Y | gt=0,required |
| suTypeId | integer |  | Y | gt=0,required |
| suTypeCode | string |  | Y | required |
| serviceType | string |  | N |  |
| suId | integer |  | Y | gt=0,required |
| suInstanceId | integer |  | Y | gt=0,required |
| instances | array[ServiceInstanceVersion] |  | N |  |
| ServiceInstanceVersion.id | integer |  | Y | gt=0,required |
| ServiceInstanceVersion.version | integer |  | Y | gt=0,required |

**Range of values**

request example:
```
{
    "planId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "serviceType": "serviceType",
    "suId": 0,
    "suInstanceId": 0,
    "instances": [
        {
            "id": 0,
            "version": 0
        }
    ]
}
```

**struct example:**

```
type AddServiceInstancesRequest struct {
    PlanId uint64 `json:"planId" validate:"gt=0,required"`
    SuTypeId uint64 `json:"suTypeId" validate:"gt=0,required"`
    SuTypeCode string `json:"suTypeCode" validate:"required"`
    ServiceType string `json:"serviceType"`
    SuId uint64 `json:"suId" validate:"gt=0,required"`
    SuInstanceId uint64 `json:"suInstanceId" validate:"gt=0,required"`
    Instances []struct {
            Id uint64 `json:"id" validate:"gt=0,required"`
            Version uint64 `json:"version" validate:"gt=0,required"`
        } `json:"instances"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| planId | integer | N |  |
| stackId | integer | N |  |
| packageId | integer | N |  |
| appId | integer | N |  |
| suTypeId | integer | N |  |
| serviceId | string | N |  |
| service_type | string | N |  |
| meshRole | string | N |  |
| svrType | string | N |  |
| dbUsername | string | N |  |
| dbPassword | string | N |  |
| dbName | array[string] | N |  |
| name | string | N |  |
| code | string | N |  |
| sections | array[string] | N |  |
| publishes | array[WorkspacePublishVersion] | N |  |
| WorkspacePublishVersion.uuid | string | N |  |
| WorkspacePublishVersion.event_uuid | string | N |  |
| WorkspacePublishVersion.event_version | string | N |  |
| WorkspacePublishVersion.mode | string | N |  |
| WorkspacePublishVersion.retry | integer | N |  |
| WorkspacePublishVersion.call_timeout | integer | N |  |
| WorkspacePublishVersion.total_timeout | integer | N |  |
| subscribes | array[WorkspaceUuidVersion] | N |  |
| WorkspaceUuidVersion.uuid | string | N |  |
| WorkspaceUuidVersion.version | string | N |  |
| storages | array[WorkspaceStorage] | N |  |
| WorkspaceStorage.storage_type | string | N |  |
| WorkspaceStorage.database_uuid | string | N |  |
| WorkspaceStorage.tables | array[WorkspaceUuidVersion] | N |  |
| errorcodes | array[WorkspaceUuidVersion] | N |  |
| imageName | string | N |  |
| serviceVersion | string | N |  |
| serviceInstanceId | integer | N |  |
| serviceUuid | string | N |  |
| serviceInstanceVersionId | integer | N |  |
| TargetVersionId | integer | N |  |
| joinMode | string | N |  |
| configure | map[string]interface | N |  |
| status | string | N |  |
| description | string | N |  |
| tenantId | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| Time | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "planId": 0,
    "stackId": 0,
    "packageId": 0,
    "appId": 0,
    "suTypeId": 0,
    "serviceId": "serviceId",
    "service_type": "service_type",
    "meshRole": "meshRole",
    "svrType": "svrType",
    "dbUsername": "dbUsername",
    "dbPassword": "dbPassword",
    "dbName": [
    ],
    "name": "name",
    "code": "code",
    "sections": [
    ],
    "publishes": [
        {
            "uuid": "uuid",
            "event_uuid": "event_uuid",
            "event_version": "event_version",
            "mode": "mode",
            "retry": 0,
            "call_timeout": 0,
            "total_timeout": 0
        }
    ],
    "subscribes": [
        {
            "uuid": "uuid",
            "version": "version"
        }
    ],
    "storages": [
        {
            "storage_type": "storage_type",
            "database_uuid": "database_uuid",
            "tables": [
                {
                }
            ]
        }
    ],
    "errorcodes": [
        {
        }
    ],
    "imageName": "imageName",
    "serviceVersion": "serviceVersion",
    "serviceInstanceId": 0,
    "serviceUuid": "serviceUuid",
    "serviceInstanceVersionId": 0,
    "TargetVersionId": 0,
    "joinMode": "joinMode",
    "configure": {
    },
    "status": "status",
    "description": "description",
    "tenantId": "tenantId",
    "creatorId": "creatorId",
    "creator": "creator",
    "updaterId": "updaterId",
    "updater": "updater",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "Time": "Time"
}
```



# **plan - si delete - API**

## URL: /environment/v1/plan/si/delete ##

## Method : Post ##

## Event ID : EnvPlanDeleteSvcInstances ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| instances | array[uint64] |  | Y | gt=0,dive,required |

**Range of values**

request example:
```
{
    "instances": [
    ]
}
```

**struct example:**

```
type DeleteServiceInstancesRequest struct {
    Instances []uint64 `json:"instances" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - si list - API**

## URL: /environment/v1/plan/si/list ##

## Method : Post ##

## Event ID : EnvPlanSvcInstances ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| planId | integer |  | N |  |
| appId | integer |  | N |  |
| serviceId | string |  | N |  |
| serviceType | string |  | N |  |
| serviceInstanceId | integer |  | N |  |
| tenantId | string |  | N |  |
| name | string |  | N |  |
| code | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "planId": 0,
    "appId": 0,
    "serviceId": "serviceId",
    "serviceType": "serviceType",
    "serviceInstanceId": 0,
    "tenantId": "tenantId",
    "name": "name",
    "code": "code"
}
```

**struct example:**

```
type ServiceInstanceRefFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    PlanId uint64 `json:"planId"`
    AppId uint64 `json:"appId"`
    ServiceId string `json:"serviceId"`
    ServiceType string `json:"serviceType"`
    ServiceInstanceId uint64 `json:"serviceInstanceId"`
    TenantId string `json:"tenantId"`
    Name string `json:"name"`
    Code string `json:"code"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **plan - table add - API**

## URL: /environment/v1/plan/table/add ##

## Method : Post ##

## Event ID : EnvPlanAddTables ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| planId | integer |  | Y | gt=0,required |
| dbRefId | integer |  | Y | gt=0,required |
| suTypeId | integer |  | N |  |
| suTypeCode | string |  | N |  |
| serviceType | string |  | N |  |
| suId | integer |  | N |  |
| suInstanceId | integer |  | N |  |
| tables | array[TableVersionInfo] |  | Y | gt=0,dive,required |
| TableVersionInfo.id | integer |  | N |  |
| TableVersionInfo.name | string |  | N | name,max=64 |
| TableVersionInfo.uuid | string |  | Y | required |
| TableVersionInfo.version | string |  | N | max=64 |

**Range of values**

request example:
```
{
    "planId": 0,
    "dbRefId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "serviceType": "serviceType",
    "suId": 0,
    "suInstanceId": 0,
    "tables": [
        {
            "id": 0,
            "name": "name",
            "uuid": "uuid",
            "version": "version"
        }
    ]
}
```

**struct example:**

```
type AddTableRequest struct {
    PlanId uint64 `json:"planId" validate:"gt=0,required"`
    DbRefId uint64 `json:"dbRefId" validate:"gt=0,required"`
    SuTypeId uint64 `json:"suTypeId"`
    SuTypeCode string `json:"suTypeCode"`
    ServiceType string `json:"serviceType"`
    SuId uint64 `json:"suId"`
    SuInstanceId uint64 `json:"suInstanceId"`
    Tables []struct {
            Id uint64 `json:"id"`
            Name string `json:"name" validate:"name,max=64"`
            Uuid string `json:"uuid" validate:"required"`
            Version string `json:"version" validate:"max=64"`
        } `json:"tables" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - table delete - API**

## URL: /environment/v1/plan/table/delete ##

## Method : Post ##

## Event ID : EnvPlanDeleteTables ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| databases | array[uint64] |  | Y | gt=0,dive,required |

**Range of values**

request example:
```
{
    "databases": [
    ]
}
```

**struct example:**

```
type DeleteTableRequest struct {
    Databases []uint64 `json:"databases" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - table list - API**

## URL: /environment/v1/plan/table/list ##

## Method : Post ##

## Event ID : EnvPlanTables ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| planId | integer |  | N |  |
| dbRefId | integer |  | N |  |
| tenantId | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "planId": 0,
    "dbRefId": 0,
    "tenantId": "tenantId"
}
```

**struct example:**

```
type TableRefFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    PlanId uint64 `json:"planId"`
    DbRefId uint64 `json:"dbRefId"`
    TenantId string `json:"tenantId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **plan - ti add - API**

## URL: /environment/v1/plan/ti/add ##

## Method : Post ##

## Event ID : EnvPlanAddTableInstance ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| planId | integer |  | Y | gt=0,required |
| suTypeId | integer |  | N |  |
| suTypeCode | string |  | N |  |
| serviceType | string |  | N |  |
| suId | integer |  | N |  |
| suInstanceId | integer |  | N |  |
| dbInstanceId | integer |  | Y | gt=0,required |
| instances | array[uint64] |  | Y | gt=0,dive,required |

**Range of values**

request example:
```
{
    "planId": 0,
    "suTypeId": 0,
    "suTypeCode": "suTypeCode",
    "serviceType": "serviceType",
    "suId": 0,
    "suInstanceId": 0,
    "dbInstanceId": 0,
    "instances": [
    ]
}
```

**struct example:**

```
type AddTableInstancesRequest struct {
    PlanId uint64 `json:"planId" validate:"gt=0,required"`
    SuTypeId uint64 `json:"suTypeId"`
    SuTypeCode string `json:"suTypeCode"`
    ServiceType string `json:"serviceType"`
    SuId uint64 `json:"suId"`
    SuInstanceId uint64 `json:"suInstanceId"`
    DbInstanceId uint64 `json:"dbInstanceId" validate:"gt=0,required"`
    Instances []uint64 `json:"instances" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **plan - update - API**

## URL: /environment/v1/plan/update ##

## Method : Post ##

## Event ID : EnvPlanUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| planId | integer |  | Y | gt=0,required |
| name | string |  | N | max=64 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "planId": 0,
    "name": "name",
    "description": "description"
}
```

**struct example:**

```
type UpdatePlanRequest struct {
    PlanId uint64 `json:"planId" validate:"gt=0,required"`
    Name string `json:"name" validate:"max=64"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


