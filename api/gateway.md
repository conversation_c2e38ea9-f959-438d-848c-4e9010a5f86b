
# **gateway - config - API**

## URL: /environment/v1/gateway/config ##

## Method : Post ##

## Event ID : EnvGatewayConfig ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| gatewayId | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "gatewayId": 0
}
```

**struct example:**

```
type OperateGatewayRequest struct {
    GatewayId uint64 `json:"gatewayId" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| key | string | N |  |
| value | object | N |  |
| map_string_Fields.key | string | N |  |
| map_string_Fields.value | array[ConfigureColumn] | N |  |
| ConfigureColumn.id | string | N |  |
| ConfigureColumn.fieldId | integer | N |  |
| ConfigureColumn.domain | string | N |  |
| ConfigureColumn.group | string | N |  |
| ConfigureColumn.section | string | N |  |
| ConfigureColumn.label | string | N |  |
| ConfigureColumn.note | string | N |  |
| ConfigureColumn.placeholder | string | N |  |
| ConfigureColumn.unit | string | N |  |
| ConfigureColumn.priority | integer | N |  |
| ConfigureColumn.necessary | boolean | N |  |
| ConfigureColumn.path | string | N |  |
| ConfigureColumn.dataType | string | N |  |
| ConfigureColumn.type | string | N |  |
| ConfigureColumn.runtimeValue | object | N |  |
| ConfigureColumn.value | object | N |  |
| ConfigureColumn.min | number | N |  |
| ConfigureColumn.max | number | N |  |
| ConfigureColumn.readOnly | boolean | N |  |
| ConfigureColumn.hidden | boolean | N |  |
| ConfigureColumn.validate | string | N |  |
| ConfigureColumn.items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |

**Range of values**

response example:

```
{
    "key": "key",
    "value": {
        "key": "key",
        "value": [
            {
                "id": "id",
                "fieldId": 0,
                "domain": "domain",
                "group": "group",
                "section": "section",
                "label": "label",
                "note": "note",
                "placeholder": "placeholder",
                "unit": "unit",
                "priority": 0,
                "necessary": false,
                "path": "path",
                "dataType": "dataType",
                "type": "type",
                "runtimeValue": {
                },
                "value": {
                },
                "min": "min",
                "max": "max",
                "readOnly": false,
                "hidden": false,
                "validate": "validate",
                "items": [
                    {
                        "value": "value",
                        "text": "text"
                    }
                ]
            }
        ]
    }
}
```



# **gateway - config save - API**

## URL: /environment/v1/gateway/config/save ##

## Method : Post ##

## Event ID : EnvGatewaySaveConfig ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| gatewayId | integer |  | Y | required,gt=0 |
| upgrade | boolean |  | N | omitempty |
| configure | map[string]interface |  | Y | gt=0,dive,required |
| version | string |  | N | omitempty,name,max=64 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "gatewayId": 0,
    "upgrade": false,
    "configure": {
    },
    "version": "version",
    "description": "description"
}
```

**struct example:**

```
type SaveConfigRequest struct {
    GatewayId uint64 `json:"gatewayId" validate:"required,gt=0"`
    Upgrade bool `json:"upgrade" validate:"omitempty"`
    Configure map[string]interface{} `json:"configure" validate:"gt=0,dive,required"`
    Version string `json:"version" validate:"omitempty,name,max=64"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **gateway - deploy - API**

## URL: /environment/v1/gateway/deploy ##

## Method : Post ##

## Event ID : EnvGatewayDeploy ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| PlanBase | object |  | N |  |
| PlanBase.RequestBase | object |  | N |  |
| RequestBase.ws | string |  | N | omitempty,name,max=32 |
| RequestBase.envId | integer |  | N | omitempty |
| RequestBase.env | string |  | N | omitempty,name,max=32 |
| PlanBase.batchId | integer |  | N | omitempty,gte=0 |
| PlanBase.versionId | integer |  | N | omitempty,gte=0 |
| PlanBase.stackId | integer |  | N | omitempty,gte=0 |
| setName | string |  | N | omitempty,name,max=64 |
| setId | integer |  | N | gte=0 |
| name | string |  | N | omitempty,name,max=64 |
| packageType | string |  | N | oneof=ingress outgress |
| version | string |  | N | omitempty,name,max=64 |
| suInstanceId | integer |  | N | gt=0 |
| k8sClusterId | integer |  | N | gte=0 |
| kvcClusterId | integer |  | N | gte=0 |
| cephBucketId | integer |  | N | gte=0 |
| brokerVpnId | integer |  | N | gte=0 |
| description | string |  | N | omitempty,max=255 |
| parameters | map[string]interface |  | Y | gte=0,dive,required |
| DeployParameter | object |  | N |  |
| DeployParameter.podReplicas | integer |  | N | gt=0 |
| DeployParameter.logLevel | string |  | N | omitempty,oneof=error info debug |
| DeployParameter.appLimitsCpu | string |  | N | omitempty,numeric |
| DeployParameter.appRequestsCpu | string |  | N | omitempty,numeric |
| DeployParameter.appLimitsMemory | string |  | N | omitempty,numeric |
| DeployParameter.appRequestsMemory | string |  | N | omitempty,numeric |
| DeployParameter.nodeSelectorKey | string |  | N | omitempty,oneof=az node hag |
| DeployParameter.nodeSelectorValue | string |  | N | omitempty,name,max=16 |
| DeployParameter.appTag | string |  | N | omitempty,name,max=32 |
| DeployParameter.serviceVersion | string |  | N | omitempty,name,max=32 |
| DeployParameter.servicePort | integer |  | N | gte=0 |
| DeployParameter.servicePortType | string |  | N | omitempty,oneof=Automatic Customize |
| DeployParameter.pluginMode | string |  | N |  |

**Range of values**

request example:
```
{
    "PlanBase": {
        "RequestBase": {
            "ws": "ws",
            "envId": 0,
            "env": "env"
        },
        "batchId": 0,
        "versionId": 0,
        "stackId": 0
    },
    "setName": "setName",
    "setId": 0,
    "name": "name",
    "packageType": "packageType",
    "version": "version",
    "suInstanceId": 0,
    "k8sClusterId": 0,
    "kvcClusterId": 0,
    "cephBucketId": 0,
    "brokerVpnId": 0,
    "description": "description",
    "parameters": {
    },
    "DeployParameter": {
        "podReplicas": 0,
        "logLevel": "logLevel",
        "appLimitsCpu": "appLimitsCpu",
        "appRequestsCpu": "appRequestsCpu",
        "appLimitsMemory": "appLimitsMemory",
        "appRequestsMemory": "appRequestsMemory",
        "nodeSelectorKey": "nodeSelectorKey",
        "nodeSelectorValue": "nodeSelectorValue",
        "appTag": "appTag",
        "serviceVersion": "serviceVersion",
        "servicePort": 0,
        "servicePortType": "servicePortType",
        "pluginMode": "pluginMode"
    }
}
```

**struct example:**

```
type DeployGatewayRequest struct {
    PlanBase struct {
        RequestBase struct {
            Ws string `json:"ws" validate:"omitempty,name,max=32"`
            EnvId  `json:"envId" validate:"omitempty"`
            Env string `json:"env" validate:"omitempty,name,max=32"`
        }
        BatchId uint64 `json:"batchId" validate:"omitempty,gte=0"`
        VersionId uint64 `json:"versionId" validate:"omitempty,gte=0"`
        StackId uint64 `json:"stackId" validate:"omitempty,gte=0"`
    }
    SetName string `json:"setName" validate:"omitempty,name,max=64"`
    SetId uint64 `json:"setId" validate:"gte=0"`
    Name string `json:"name" validate:"omitempty,name,max=64"`
    PackageType string `json:"packageType" validate:"oneof=ingress outgress"`
    Version string `json:"version" validate:"omitempty,name,max=64"`
    SuInstanceId uint64 `json:"suInstanceId" validate:"gt=0"`
    K8sClusterId uint64 `json:"k8sClusterId" validate:"gte=0"`
    KvcClusterId uint64 `json:"kvcClusterId" validate:"gte=0"`
    CephBucketId uint64 `json:"cephBucketId" validate:"gte=0"`
    BrokerVpnId uint64 `json:"brokerVpnId" validate:"gte=0"`
    Description string `json:"description" validate:"omitempty,max=255"`
    Parameters map[string]interface{} `json:"parameters" validate:"gte=0,dive,required"`
    DeployParameter struct {
        PodReplicas uint64 `json:"podReplicas" validate:"gt=0"`
        LogLevel string `json:"logLevel" validate:"omitempty,oneof=error info debug"`
        AppLimitsCpu string `json:"appLimitsCpu" validate:"omitempty,numeric"`
        AppRequestsCpu string `json:"appRequestsCpu" validate:"omitempty,numeric"`
        AppLimitsMemory string `json:"appLimitsMemory" validate:"omitempty,numeric"`
        AppRequestsMemory string `json:"appRequestsMemory" validate:"omitempty,numeric"`
        NodeSelectorKey string `json:"nodeSelectorKey" validate:"omitempty,oneof=az node hag"`
        NodeSelectorValue string `json:"nodeSelectorValue" validate:"omitempty,name,max=16"`
        AppTag string `json:"appTag" validate:"omitempty,name,max=32"`
        ServiceVersion string `json:"serviceVersion" validate:"omitempty,name,max=32"`
        ServicePort uint64 `json:"servicePort" validate:"gte=0"`
        ServicePortType string `json:"servicePortType" validate:"omitempty,oneof=Automatic Customize"`
        PluginMode string `json:"pluginMode"`
    }
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **gateway - detail - API**

## URL: /environment/v1/gateway/detail ##

## Method : Post ##

## Event ID : EnvGatewayDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type Request struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| setInfo | object | N |  |
| PluginSet.setName | string | N |  |
| PluginSet.setType | string | N |  |
| PluginSet.setId | integer | N |  |
| PluginSet.envId | integer | N |  |
| PluginSet.gatewayType | string | N |  |
| PluginSet.plugins | array[PluginConfig] | N |  |
| PluginConfig.name | string | N |  |
| PluginConfig.code | string | N |  |
| PluginConfig.kind | string | N |  |
| PluginConfig.type | string | N |  |
| PluginConfig.desc | string | N |  |
| PluginConfig.priority | integer | N |  |
| PluginConfig.order | string | N |  |
| PluginConfig.version | string | N |  |
| PluginConfig.required | array[string] | N |  |
| PluginConfig.section | array[string] | N |  |
| PluginConfig.columns | array[PluginColumn] | N |  |
| PluginColumn.keyName | string | N |  |
| PluginColumn.keyValue | object | N |  |
| PluginColumn.keyValueType | string | N |  |
| PluginColumn.keyPath | string | N |  |
| PluginColumn.keyGroup | string | N |  |
| PluginConfig.routePluginColumns | array[PluginColumn] | N |  |
| PluginConfig.routePluginConfig | array[RoutePluginConfig] | N |  |
| RoutePluginConfig.config | array[PluginColumn] | N |  |
| PluginConfig.dataType | string | N |  |
| PluginConfig.pluginId | integer | N |  |
| configure | map[string]interface | N |  |
| Gateway | object | N |  |
| Gateway.id | integer | N |  |
| Gateway.stackId | integer | N |  |
| Gateway.planId | integer | N |  |
| Gateway.environmentId | integer | N |  |
| Gateway.suId | integer | N |  |
| Gateway.suInstanceId | integer | N |  |
| Gateway.serviceInstanceId | integer | N |  |
| Gateway.pluginSetId | integer | N |  |
| Gateway.replica | integer | N |  |
| Gateway.gatewayType | string | N |  |
| Gateway.name | string | N |  |
| Gateway.servicePort | integer | N |  |
| Gateway.servicePortType | string | N |  |
| Gateway.version | string | N |  |
| Gateway.k8sId | integer | N |  |
| Gateway.cacheId | integer | N |  |
| Gateway.status | string | N |  |
| Gateway.runningStatus | string | N |  |
| Gateway.clusterId | string | N |  |
| Gateway.clusterName | string | N |  |
| Gateway.description | string | N |  |
| Gateway.tenantId | string | N |  |
| Gateway.config | map[string]interface | N |  |
| Gateway.creatorId | string | N |  |
| Gateway.creator | string | N |  |
| Gateway.updaterId | string | N |  |
| Gateway.updater | string | N |  |
| Gateway.createAt | string | N |  |
| Gateway.updatedAt | string | N |  |
| Gateway.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "setInfo": {
        "setName": "setName",
        "setType": "setType",
        "setId": 0,
        "envId": 0,
        "gatewayType": "gatewayType",
        "plugins": [
            {
                "name": "name",
                "code": "code",
                "kind": "kind",
                "type": "type",
                "desc": "desc",
                "priority": 0,
                "order": "order",
                "version": "version",
                "required": [
                ],
                "section": [
                ],
                "columns": [
                    {
                        "keyName": "keyName",
                        "keyValue": {
                        },
                        "keyValueType": "keyValueType",
                        "keyPath": "keyPath",
                        "keyGroup": "keyGroup"
                    }
                ],
                "routePluginColumns": [
                    {
                    }
                ],
                "routePluginConfig": [
                    {
                        "config": [
                            {
                            }
                        ]
                    }
                ],
                "dataType": "dataType",
                "pluginId": 0
            }
        ]
    },
    "configure": {
    },
    "Gateway": {
        "id": 0,
        "stackId": 0,
        "planId": 0,
        "environmentId": 0,
        "suId": 0,
        "suInstanceId": 0,
        "serviceInstanceId": 0,
        "pluginSetId": 0,
        "replica": 0,
        "gatewayType": "gatewayType",
        "name": "name",
        "servicePort": 0,
        "servicePortType": "servicePortType",
        "version": "version",
        "k8sId": 0,
        "cacheId": 0,
        "status": "status",
        "runningStatus": "runningStatus",
        "clusterId": "clusterId",
        "clusterName": "clusterName",
        "description": "description",
        "tenantId": "tenantId",
        "config": {
        },
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **gateway - images - API**

## URL: /environment/v1/gateway/images ##

## Method : Post ##

## Event ID : EnvGatewayImages ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| suInstanceId | integer |  | N | gt=0 |
| repoName | string |  | N | omitempty,max=64 |

**Range of values**

request example:
```
{
    "suInstanceId": 0,
    "repoName": "repoName"
}
```

**struct example:**

```
type GetGatewayImagesRequest struct {
    SuInstanceId uint64 `json:"suInstanceId" validate:"gt=0"`
    RepoName string `json:"repoName" validate:"omitempty,max=64"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| version | array[string] | N |  |

**Range of values**

response example:

```
{
    "version": [
    ]
}
```



# **gateway - list - API**

## URL: /environment/v1/gateway/list ##

## Method : Post ##

## Event ID : EnvGatewayList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| name | array[string] |  | N |  |
| planId | integer |  | N |  |
| envId | integer |  | N |  |
| tenantId | string |  | N |  |
| gatewayType | string |  | N |  |
| serviceInstanceId | integer |  | N |  |
| suId | integer |  | N |  |
| suInstanceId | integer |  | N |  |
| k8sClusterId | integer |  | N |  |
| type | string |  | N |  |
| version | array[string] |  | N |  |
| servicePort | array[uint64] |  | N |  |
| replica | array[uint64] |  | N |  |
| clusterCode | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "name": [
    ],
    "planId": 0,
    "envId": 0,
    "tenantId": "tenantId",
    "gatewayType": "gatewayType",
    "serviceInstanceId": 0,
    "suId": 0,
    "suInstanceId": 0,
    "k8sClusterId": 0,
    "type": "type",
    "version": [
    ],
    "servicePort": [
    ],
    "replica": [
    ],
    "clusterCode": "clusterCode"
}
```

**struct example:**

```
type GatewayFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    Name []string `json:"name"`
    PlanId uint64 `json:"planId"`
    EnvId uint64 `json:"envId"`
    TenantId string `json:"tenantId"`
    GatewayType string `json:"gatewayType"`
    ServiceInstanceId uint64 `json:"serviceInstanceId"`
    SuId uint64 `json:"suId"`
    SuInstanceId uint64 `json:"suInstanceId"`
    K8sClusterId uint64 `json:"k8sClusterId"`
    Type string `json:"type"`
    Version []string `json:"version"`
    ServicePort []uint64 `json:"servicePort"`
    Replica []uint64 `json:"replica"`
    ClusterCode string `json:"clusterCode"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| setInfo | object | N |  |
| PluginSet.setName | string | N |  |
| PluginSet.setType | string | N |  |
| PluginSet.setId | integer | N |  |
| PluginSet.envId | integer | N |  |
| PluginSet.gatewayType | string | N |  |
| PluginSet.plugins | array[PluginConfig] | N |  |
| PluginConfig.name | string | N |  |
| PluginConfig.code | string | N |  |
| PluginConfig.kind | string | N |  |
| PluginConfig.type | string | N |  |
| PluginConfig.desc | string | N |  |
| PluginConfig.priority | integer | N |  |
| PluginConfig.order | string | N |  |
| PluginConfig.version | string | N |  |
| PluginConfig.required | array[string] | N |  |
| PluginConfig.section | array[string] | N |  |
| PluginConfig.columns | array[PluginColumn] | N |  |
| PluginColumn.keyName | string | N |  |
| PluginColumn.keyValue | object | N |  |
| PluginColumn.keyValueType | string | N |  |
| PluginColumn.keyPath | string | N |  |
| PluginColumn.keyGroup | string | N |  |
| PluginConfig.routePluginColumns | array[PluginColumn] | N |  |
| PluginConfig.routePluginConfig | array[RoutePluginConfig] | N |  |
| RoutePluginConfig.config | array[PluginColumn] | N |  |
| PluginConfig.dataType | string | N |  |
| PluginConfig.pluginId | integer | N |  |
| configure | map[string]interface | N |  |
| Gateway | object | N |  |
| Gateway.id | integer | N |  |
| Gateway.stackId | integer | N |  |
| Gateway.planId | integer | N |  |
| Gateway.environmentId | integer | N |  |
| Gateway.suId | integer | N |  |
| Gateway.suInstanceId | integer | N |  |
| Gateway.serviceInstanceId | integer | N |  |
| Gateway.pluginSetId | integer | N |  |
| Gateway.replica | integer | N |  |
| Gateway.gatewayType | string | N |  |
| Gateway.name | string | N |  |
| Gateway.servicePort | integer | N |  |
| Gateway.servicePortType | string | N |  |
| Gateway.version | string | N |  |
| Gateway.k8sId | integer | N |  |
| Gateway.cacheId | integer | N |  |
| Gateway.status | string | N |  |
| Gateway.runningStatus | string | N |  |
| Gateway.clusterId | string | N |  |
| Gateway.clusterName | string | N |  |
| Gateway.description | string | N |  |
| Gateway.tenantId | string | N |  |
| Gateway.config | map[string]interface | N |  |
| Gateway.creatorId | string | N |  |
| Gateway.creator | string | N |  |
| Gateway.updaterId | string | N |  |
| Gateway.updater | string | N |  |
| Gateway.createAt | string | N |  |
| Gateway.updatedAt | string | N |  |
| Gateway.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "setInfo": {
        "setName": "setName",
        "setType": "setType",
        "setId": 0,
        "envId": 0,
        "gatewayType": "gatewayType",
        "plugins": [
            {
                "name": "name",
                "code": "code",
                "kind": "kind",
                "type": "type",
                "desc": "desc",
                "priority": 0,
                "order": "order",
                "version": "version",
                "required": [
                ],
                "section": [
                ],
                "columns": [
                    {
                        "keyName": "keyName",
                        "keyValue": {
                        },
                        "keyValueType": "keyValueType",
                        "keyPath": "keyPath",
                        "keyGroup": "keyGroup"
                    }
                ],
                "routePluginColumns": [
                    {
                    }
                ],
                "routePluginConfig": [
                    {
                        "config": [
                            {
                            }
                        ]
                    }
                ],
                "dataType": "dataType",
                "pluginId": 0
            }
        ]
    },
    "configure": {
    },
    "Gateway": {
        "id": 0,
        "stackId": 0,
        "planId": 0,
        "environmentId": 0,
        "suId": 0,
        "suInstanceId": 0,
        "serviceInstanceId": 0,
        "pluginSetId": 0,
        "replica": 0,
        "gatewayType": "gatewayType",
        "name": "name",
        "servicePort": 0,
        "servicePortType": "servicePortType",
        "version": "version",
        "k8sId": 0,
        "cacheId": 0,
        "status": "status",
        "runningStatus": "runningStatus",
        "clusterId": "clusterId",
        "clusterName": "clusterName",
        "description": "description",
        "tenantId": "tenantId",
        "config": {
        },
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **gateway - offline - API**

## URL: /environment/v1/gateway/offline ##

## Method : Post ##

## Event ID : EnvGatewayOffline ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type gatewayIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **gateway - page - API**

## URL: /environment/v1/gateway/page ##

## Method : Post ##

## Event ID : EnvGatewayPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| name | array[string] |  | N |  |
| planId | integer |  | N |  |
| envId | integer |  | N |  |
| tenantId | string |  | N |  |
| gatewayType | string |  | N |  |
| serviceInstanceId | integer |  | N |  |
| suId | integer |  | N |  |
| suInstanceId | integer |  | N |  |
| k8sClusterId | integer |  | N |  |
| type | string |  | N |  |
| version | array[string] |  | N |  |
| servicePort | array[uint64] |  | N |  |
| replica | array[uint64] |  | N |  |
| clusterCode | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "name": [
    ],
    "planId": 0,
    "envId": 0,
    "tenantId": "tenantId",
    "gatewayType": "gatewayType",
    "serviceInstanceId": 0,
    "suId": 0,
    "suInstanceId": 0,
    "k8sClusterId": 0,
    "type": "type",
    "version": [
    ],
    "servicePort": [
    ],
    "replica": [
    ],
    "clusterCode": "clusterCode"
}
```

**struct example:**

```
type GatewayFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    Name []string `json:"name"`
    PlanId uint64 `json:"planId"`
    EnvId uint64 `json:"envId"`
    TenantId string `json:"tenantId"`
    GatewayType string `json:"gatewayType"`
    ServiceInstanceId uint64 `json:"serviceInstanceId"`
    SuId uint64 `json:"suId"`
    SuInstanceId uint64 `json:"suInstanceId"`
    K8sClusterId uint64 `json:"k8sClusterId"`
    Type string `json:"type"`
    Version []string `json:"version"`
    ServicePort []uint64 `json:"servicePort"`
    Replica []uint64 `json:"replica"`
    ClusterCode string `json:"clusterCode"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **gateway - plugin-create - API**

## URL: /environment/v1/gateway/plugin-create ##

## Method : Post ##

## Event ID : EnvGatewayPluginCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| suInstanceId | integer |  | N | gt=0 |
| repoName | string |  | N | omitempty,max=64 |

**Range of values**

request example:
```
{
    "suInstanceId": 0,
    "repoName": "repoName"
}
```

**struct example:**

```
type GetGatewayImagesRequest struct {
    SuInstanceId uint64 `json:"suInstanceId" validate:"gt=0"`
    RepoName string `json:"repoName" validate:"omitempty,max=64"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| version | array[string] | N |  |

**Range of values**

response example:

```
{
    "version": [
    ]
}
```



# **gateway - set-create - API**

## URL: /environment/v1/gateway/set-create ##

## Method : Post ##

## Event ID : EnvGatewaySetCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| suInstanceId | integer |  | N | gt=0 |
| repoName | string |  | N | omitempty,max=64 |

**Range of values**

request example:
```
{
    "suInstanceId": 0,
    "repoName": "repoName"
}
```

**struct example:**

```
type GetGatewayImagesRequest struct {
    SuInstanceId uint64 `json:"suInstanceId" validate:"gt=0"`
    RepoName string `json:"repoName" validate:"omitempty,max=64"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| version | array[string] | N |  |

**Range of values**

response example:

```
{
    "version": [
    ]
}
```



# **gateway - set-list - API**

## URL: /environment/v1/gateway/set-list ##

## Method : Post ##

## Event ID : EnvGatewaySetList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| setId | integer |  | N | gte=0 |
| setName | string |  | N | omitempty,max=64 |
| setType | string |  | N | omitempty,oneof=platform customer |
| envId | integer |  | N | gte=0 |
| gatewayId | integer |  | N | gte=0 |
| gatewayType | string |  | N | omitempty,oneof=ingress outgress |
| pluginId | integer |  | N |  |

**Range of values**

request example:
```
{
    "setId": 0,
    "setName": "setName",
    "setType": "setType",
    "envId": 0,
    "gatewayId": 0,
    "gatewayType": "gatewayType",
    "pluginId": 0
}
```

**struct example:**

```
type GetGatewaySetsRequest struct {
    SetId uint64 `json:"setId" validate:"gte=0"`
    SetName string `json:"setName" validate:"omitempty,max=64"`
    SetType string `json:"setType" validate:"omitempty,oneof=platform customer"`
    EnvId uint64 `json:"envId" validate:"gte=0"`
    GatewayId uint64 `json:"gatewayId" validate:"gte=0"`
    GatewayType string `json:"gatewayType" validate:"omitempty,oneof=ingress outgress"`
    PluginId uint64 `json:"pluginId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| version | array[string] | N |  |

**Range of values**

response example:

```
{
    "version": [
    ]
}
```



# **gateway - set-update - API**

## URL: /environment/v1/gateway/set-update ##

## Method : Post ##

## Event ID : EnvGatewaySetUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| suInstanceId | integer |  | N | gt=0 |
| repoName | string |  | N | omitempty,max=64 |

**Range of values**

request example:
```
{
    "suInstanceId": 0,
    "repoName": "repoName"
}
```

**struct example:**

```
type GetGatewayImagesRequest struct {
    SuInstanceId uint64 `json:"suInstanceId" validate:"gt=0"`
    RepoName string `json:"repoName" validate:"omitempty,max=64"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| version | array[string] | N |  |

**Range of values**

response example:

```
{
    "version": [
    ]
}
```



# **gateway - update - API**

## URL: /environment/v1/gateway/update ##

## Method : Post ##

## Event ID : EnvGatewayUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |
| name | string |  | N | omitempty,name,max=64 |
| description | string |  | N | omitempty,max=255 |
| status | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "id": 0,
    "name": "name",
    "description": "description",
    "status": "status"
}
```

**struct example:**

```
type UpdateGatewayRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
    Name string `json:"name" validate:"omitempty,name,max=64"`
    Description string `json:"description" validate:"omitempty,max=255"`
    Status string `json:"status" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **gateway - upgrade - API**

## URL: /environment/v1/gateway/upgrade ##

## Method : Post ##

## Event ID : EnvGatewayUpgrade ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| PlanBase | object |  | N |  |
| PlanBase.RequestBase | object |  | N |  |
| RequestBase.ws | string |  | N | omitempty,name,max=32 |
| RequestBase.envId | integer |  | N | omitempty |
| RequestBase.env | string |  | N | omitempty,name,max=32 |
| PlanBase.batchId | integer |  | N | omitempty,gte=0 |
| PlanBase.versionId | integer |  | N | omitempty,gte=0 |
| PlanBase.stackId | integer |  | N | omitempty,gte=0 |
| operate | string |  | N | omitempty,name,max=64 |
| gatewayId | integer |  | N | gt=0 |
| name | string |  | N | omitempty,name,max=64 |
| serviceInstanceId | integer |  | N | gte=0 |
| version | string |  | N | omitempty,name,max=64 |
| description | string |  | N | omitempty,max=255 |
| parameters | map[string]interface |  | Y | gte=0,dive,required |
| apiUuidList | array[string] |  | Y | gte=0,dive,required |
| configure | map[string]interface |  | Y | gte=0,dive,required |
| setting | map[string]interface |  | Y | gte=0,dive,required |
| DeployParameter | object |  | N |  |
| DeployParameter.podReplicas | integer |  | N | gt=0 |
| DeployParameter.logLevel | string |  | N | omitempty,oneof=error info debug |
| DeployParameter.appLimitsCpu | string |  | N | omitempty,numeric |
| DeployParameter.appRequestsCpu | string |  | N | omitempty,numeric |
| DeployParameter.appLimitsMemory | string |  | N | omitempty,numeric |
| DeployParameter.appRequestsMemory | string |  | N | omitempty,numeric |
| DeployParameter.nodeSelectorKey | string |  | N | omitempty,oneof=az node hag |
| DeployParameter.nodeSelectorValue | string |  | N | omitempty,name,max=16 |
| DeployParameter.appTag | string |  | N | omitempty,name,max=32 |
| DeployParameter.serviceVersion | string |  | N | omitempty,name,max=32 |
| DeployParameter.servicePort | integer |  | N | gte=0 |
| DeployParameter.servicePortType | string |  | N | omitempty,oneof=Automatic Customize |
| DeployParameter.pluginMode | string |  | N |  |

**Range of values**

request example:
```
{
    "PlanBase": {
        "RequestBase": {
            "ws": "ws",
            "envId": 0,
            "env": "env"
        },
        "batchId": 0,
        "versionId": 0,
        "stackId": 0
    },
    "operate": "operate",
    "gatewayId": 0,
    "name": "name",
    "serviceInstanceId": 0,
    "version": "version",
    "description": "description",
    "parameters": {
    },
    "apiUuidList": [
    ],
    "configure": {
    },
    "setting": {
    },
    "DeployParameter": {
        "podReplicas": 0,
        "logLevel": "logLevel",
        "appLimitsCpu": "appLimitsCpu",
        "appRequestsCpu": "appRequestsCpu",
        "appLimitsMemory": "appLimitsMemory",
        "appRequestsMemory": "appRequestsMemory",
        "nodeSelectorKey": "nodeSelectorKey",
        "nodeSelectorValue": "nodeSelectorValue",
        "appTag": "appTag",
        "serviceVersion": "serviceVersion",
        "servicePort": 0,
        "servicePortType": "servicePortType",
        "pluginMode": "pluginMode"
    }
}
```

**struct example:**

```
type UpgradeGatewayRequest struct {
    PlanBase struct {
        RequestBase struct {
            Ws string `json:"ws" validate:"omitempty,name,max=32"`
            EnvId  `json:"envId" validate:"omitempty"`
            Env string `json:"env" validate:"omitempty,name,max=32"`
        }
        BatchId uint64 `json:"batchId" validate:"omitempty,gte=0"`
        VersionId uint64 `json:"versionId" validate:"omitempty,gte=0"`
        StackId uint64 `json:"stackId" validate:"omitempty,gte=0"`
    }
    Operate string `json:"operate" validate:"omitempty,name,max=64"`
    GatewayId uint64 `json:"gatewayId" validate:"gt=0"`
    Name string `json:"name" validate:"omitempty,name,max=64"`
    ServiceInstanceId uint64 `json:"serviceInstanceId" validate:"gte=0"`
    Version string `json:"version" validate:"omitempty,name,max=64"`
    Description string `json:"description" validate:"omitempty,max=255"`
    Parameters map[string]interface{} `json:"parameters" validate:"gte=0,dive,required"`
    ApiUuidList []string `json:"apiUuidList" validate:"gte=0,dive,required"`
    Configure map[string]interface{} `json:"configure" validate:"gte=0,dive,required"`
    Setting map[string]interface{} `json:"setting" validate:"gte=0,dive,required"`
    DeployParameter struct {
        PodReplicas uint64 `json:"podReplicas" validate:"gt=0"`
        LogLevel string `json:"logLevel" validate:"omitempty,oneof=error info debug"`
        AppLimitsCpu string `json:"appLimitsCpu" validate:"omitempty,numeric"`
        AppRequestsCpu string `json:"appRequestsCpu" validate:"omitempty,numeric"`
        AppLimitsMemory string `json:"appLimitsMemory" validate:"omitempty,numeric"`
        AppRequestsMemory string `json:"appRequestsMemory" validate:"omitempty,numeric"`
        NodeSelectorKey string `json:"nodeSelectorKey" validate:"omitempty,oneof=az node hag"`
        NodeSelectorValue string `json:"nodeSelectorValue" validate:"omitempty,name,max=16"`
        AppTag string `json:"appTag" validate:"omitempty,name,max=32"`
        ServiceVersion string `json:"serviceVersion" validate:"omitempty,name,max=32"`
        ServicePort uint64 `json:"servicePort" validate:"gte=0"`
        ServicePortType string `json:"servicePortType" validate:"omitempty,oneof=Automatic Customize"`
        PluginMode string `json:"pluginMode"`
    }
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| sopId | integer | N |  |
| jobId | integer | N |  |
| taskId | integer | N |  |

**Range of values**

response example:

```
{
    "sopId": 0,
    "jobId": 0,
    "taskId": 0
}
```


