
# **configure - list - API**

## URL: /environment/v1/configure/list ##

## Method : Post ##

## Event ID : EnvConfigList ##

**Description:**



### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| version | string |  | N |  |
| projectId | integer |  | N |  |

**Range of values**

request example:
```
{
    "version": "version",
    "projectId": 0
}
```

**struct example:**

```
type ListRequest struct {
    Version string `json:"version"`
    ProjectId int `json:"projectId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| key | string | N |  |
| value | array[ConfigureColumn] | N |  |
| ConfigureColumn.id | string | N |  |
| ConfigureColumn.fieldId | integer | N |  |
| ConfigureColumn.domain | string | N |  |
| ConfigureColumn.group | string | N |  |
| ConfigureColumn.section | string | N |  |
| ConfigureColumn.label | string | N |  |
| ConfigureColumn.note | string | N |  |
| ConfigureColumn.placeholder | string | N |  |
| ConfigureColumn.unit | string | N |  |
| ConfigureColumn.priority | integer | N |  |
| ConfigureColumn.necessary | boolean | N |  |
| ConfigureColumn.path | string | N |  |
| ConfigureColumn.dataType | string | N |  |
| ConfigureColumn.type | string | N |  |
| ConfigureColumn.runtimeValue | object | N |  |
| ConfigureColumn.value | object | N |  |
| ConfigureColumn.min | number | N |  |
| ConfigureColumn.max | number | N |  |
| ConfigureColumn.readOnly | boolean | N |  |
| ConfigureColumn.hidden | boolean | N |  |
| ConfigureColumn.validate | string | N |  |
| ConfigureColumn.items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |

**Range of values**

response example:

```
{
    "key": "key",
    "value": [
        {
            "id": "id",
            "fieldId": 0,
            "domain": "domain",
            "group": "group",
            "section": "section",
            "label": "label",
            "note": "note",
            "placeholder": "placeholder",
            "unit": "unit",
            "priority": 0,
            "necessary": false,
            "path": "path",
            "dataType": "dataType",
            "type": "type",
            "runtimeValue": {
            },
            "value": {
            },
            "min": "min",
            "max": "max",
            "readOnly": false,
            "hidden": false,
            "validate": "validate",
            "items": [
                {
                    "value": "value",
                    "text": "text"
                }
            ]
        }
    ]
}
```



# **configure - query - API**

## URL: /environment/v1/configure/query ##

## Method : Post ##

## Event ID : EnvConfigQuery ##

**Description:**



### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | string |  | N |  |
| parentId | string |  | N |  |
| label | string |  | N |  |
| type | string |  | N |  |
| notice | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": "id",
    "parentId": "parentId",
    "label": "label",
    "type": "type",
    "notice": "notice"
}
```

**struct example:**

```
type ConfigFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id string `json:"id"`
    ParentId string `json:"parentId"`
    Label string `json:"label"`
    Type string `json:"type"`
    Notice string `json:"notice"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **configure - update - API**

## URL: /environment/v1/configure/update ##

## Method : Post ##

## Event ID : EnvConfigUpdate ##

**Description:**



### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| version | string |  | N |  |
| projectId | integer |  | N |  |

**Range of values**

request example:
```
{
    "version": "version",
    "projectId": 0
}
```

**struct example:**

```
type ListRequest struct {
    Version string `json:"version"`
    ProjectId int `json:"projectId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **configure - versions - API**

## URL: /environment/v1/configure/versions ##

## Method : Post ##

## Event ID : EnvConfigVersions ##

**Description:**



### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| projectId | integer |  | N |  |
| columns | object |  | N |  |
| map_string_Fields.key | string |  | N |  |
| map_string_Fields.value | array[ConfigureColumn] |  | N |  |
| ConfigureColumn.id | string |  | N |  |
| ConfigureColumn.fieldId | integer |  | N |  |
| ConfigureColumn.domain | string |  | N |  |
| ConfigureColumn.group | string |  | N |  |
| ConfigureColumn.section | string |  | N |  |
| ConfigureColumn.label | string |  | N |  |
| ConfigureColumn.note | string |  | N |  |
| ConfigureColumn.placeholder | string |  | N |  |
| ConfigureColumn.unit | string |  | N |  |
| ConfigureColumn.priority | integer |  | N |  |
| ConfigureColumn.necessary | boolean |  | N |  |
| ConfigureColumn.path | string |  | N |  |
| ConfigureColumn.dataType | string |  | N |  |
| ConfigureColumn.type | string |  | N |  |
| ConfigureColumn.runtimeValue | object |  | N |  |
| ConfigureColumn.value | object |  | N |  |
| ConfigureColumn.min | number |  | N |  |
| ConfigureColumn.max | number |  | N |  |
| ConfigureColumn.readOnly | boolean |  | N |  |
| ConfigureColumn.hidden | boolean |  | N |  |
| ConfigureColumn.validate | string |  | N |  |
| ConfigureColumn.items | array[Option] |  | N |  |
| Option.value | string |  | Y | required |
| Option.text | string |  | N | name,max=64 |

**Range of values**

request example:
```
{
    "projectId": 0,
    "columns": {
        "key": "key",
        "value": [
            {
                "id": "id",
                "fieldId": 0,
                "domain": "domain",
                "group": "group",
                "section": "section",
                "label": "label",
                "note": "note",
                "placeholder": "placeholder",
                "unit": "unit",
                "priority": 0,
                "necessary": false,
                "path": "path",
                "dataType": "dataType",
                "type": "type",
                "runtimeValue": {
                },
                "value": {
                },
                "min": "min",
                "max": "max",
                "readOnly": false,
                "hidden": false,
                "validate": "validate",
                "items": [
                    {
                        "value": "value",
                        "text": "text"
                    }
                ]
            }
        ]
    }
}
```

**struct example:**

```
type UpdateRequest struct {
    ProjectId int `json:"projectId"`
    Columns struct {
        Key string `json:"key"`
        Value []struct {
                Id string `json:"id"`
                FieldId uint64 `json:"fieldId"`
                Domain string `json:"domain"`
                Group string `json:"group"`
                Section string `json:"section"`
                Label string `json:"label"`
                Note string `json:"note"`
                Placeholder string `json:"placeholder"`
                Unit string `json:"unit"`
                Priority int `json:"priority"`
                Necessary bool `json:"necessary"`
                Path string `json:"path"`
                DataType string `json:"dataType"`
                Type string `json:"type"`
                RuntimeValue struct {
                } `json:"runtimeValue"`
                Value struct {
                } `json:"value"`
                Min number `json:"min"`
                Max number `json:"max"`
                ReadOnly bool `json:"readOnly"`
                Hidden bool `json:"hidden"`
                Validate string `json:"validate"`
                Items []struct {
                        Value string `json:"value" validate:"required"`
                        Text string `json:"text" validate:"name,max=64"`
                    } `json:"items"`
            } `json:"value"`
    } `json:"columns"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


