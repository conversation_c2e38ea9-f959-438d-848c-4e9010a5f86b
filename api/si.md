
# **si - patch - API**

## URL: /environment/v1/si/patch ##

## Method : Post ##

## Event ID : EnvSIVersionPatch ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| version | string |  | N | omitempty,max=64 |
| note | string |  | N | omitempty,max=255 |
| versions | array[uint64] |  | Y | gt=0,dive,required |
| patches | array[uint64] |  | Y | gt=0,dive,required |

**Range of values**

request example:
```
{
    "version": "version",
    "note": "note",
    "versions": [
    ],
    "patches": [
    ]
}
```

**struct example:**

```
type PatchServiceInstanceRequest struct {
    Version string `json:"version" validate:"omitempty,max=64"`
    Note string `json:"note" validate:"omitempty,max=255"`
    Versions []uint64 `json:"versions" validate:"gt=0,dive,required"`
    Patches []uint64 `json:"patches" validate:"gt=0,dive,required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **si - version delete - API**

## URL: /environment/v1/si/version/delete ##

## Method : Post ##

## Event ID : EnvSIVersionDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type serviceInstanceVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **si - version detail - API**

## URL: /environment/v1/si/version/detail ##

## Method : Post ##

## Event ID : EnvSIVersionDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type serviceInstanceVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| serviceId | string | N |  |
| packageId | integer | N |  |
| appId | integer | N |  |
| suTypeId | integer | N |  |
| name | string | N |  |
| code | string | N |  |
| dbClusterId | string | N |  |
| dbUsername | string | N |  |
| dbPassword | string | N |  |
| dbName | array[string] | N |  |
| serviceType | string | N |  |
| runningStatus | string | N |  |
| serviceUuid | string | N |  |
| status | string | N |  |
| serviceInstanceCode | string | N |  |
| releaseId | integer | N |  |
| suId | integer | N |  |
| suInstanceId | integer | N |  |
| brokerVpnId | integer | N |  |
| meshRole | string | N |  |
| svrType | string | N |  |
| ServiceInstanceVersion | object | N |  |
| ServiceInstanceVersion.id | integer | N |  |
| ServiceInstanceVersion.stackId | integer | N |  |
| ServiceInstanceVersion.envId | integer | N |  |
| ServiceInstanceVersion.instanceId | integer | N |  |
| ServiceInstanceVersion.lastPatchId | integer | N |  |
| ServiceInstanceVersion.baseVersionId | integer | N |  |
| ServiceInstanceVersion.linkVersionId | integer | N |  |
| ServiceInstanceVersion.status | string | N |  |
| ServiceInstanceVersion.journal | string | N |  |
| ServiceInstanceVersion.data | map[string]interface | N |  |
| ServiceInstanceVersion.errorCode | object | N |  |
| map_string_array_ErrorCodes.key | string | N |  |
| map_string_array_ErrorCodes.value | array[ErrorCodes] | N |  |
| ErrorCodes.Code | string | N |  |
| ErrorCodes.Message | string | N |  |
| ServiceInstanceVersion.version | string | N |  |
| ServiceInstanceVersion.description | string | N |  |
| ServiceInstanceVersion.tenantId | string | N |  |
| ServiceInstanceVersion.creatorId | string | N |  |
| ServiceInstanceVersion.creator | string | N |  |
| ServiceInstanceVersion.updaterId | string | N |  |
| ServiceInstanceVersion.updater | string | N |  |
| ServiceInstanceVersion.createAt | string | N |  |
| ServiceInstanceVersion.updatedAt | string | N |  |
| ServiceInstanceVersion.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "serviceId": "serviceId",
    "packageId": 0,
    "appId": 0,
    "suTypeId": 0,
    "name": "name",
    "code": "code",
    "dbClusterId": "dbClusterId",
    "dbUsername": "dbUsername",
    "dbPassword": "dbPassword",
    "dbName": [
    ],
    "serviceType": "serviceType",
    "runningStatus": "runningStatus",
    "serviceUuid": "serviceUuid",
    "status": "status",
    "serviceInstanceCode": "serviceInstanceCode",
    "releaseId": 0,
    "suId": 0,
    "suInstanceId": 0,
    "brokerVpnId": 0,
    "meshRole": "meshRole",
    "svrType": "svrType",
    "ServiceInstanceVersion": {
        "id": 0,
        "stackId": 0,
        "envId": 0,
        "instanceId": 0,
        "lastPatchId": 0,
        "baseVersionId": 0,
        "linkVersionId": 0,
        "status": "status",
        "journal": "journal",
        "data": {
        },
        "errorCode": {
            "key": "key",
            "value": [
                {
                    "Code": "Code",
                    "Message": "Message"
                }
            ]
        },
        "version": "version",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **si - version fields - API**

## URL: /environment/v1/si/version/fields ##

## Method : Post ##

## Event ID : EnvSIVersionFields ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| instanceVersionId | integer |  | Y | required,gt=0 |
| sectionId | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "instanceVersionId": 0,
    "sectionId": 0
}
```

**struct example:**

```
type QueryServiceInstanceFieldRequest struct {
    InstanceVersionId uint64 `json:"instanceVersionId" validate:"required,gt=0"`
    SectionId uint64 `json:"sectionId" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| requestId | string | N |  |
| fields | array[FieldInfo] | N |  |
| FieldInfo.domain | string | N |  |
| FieldInfo.group | string | N |  |
| FieldInfo.label | string | N |  |
| FieldInfo.columnNote | string | N |  |
| FieldInfo.placeholder | string | N |  |
| FieldInfo.unit | string | N |  |
| FieldInfo.necessary | boolean | N |  |
| FieldInfo.columnPath | string | N |  |
| FieldInfo.type | string | N |  |
| FieldInfo.dataType | string | N |  |
| FieldInfo.columnValue | string | N |  |
| FieldInfo.min | number | N |  |
| FieldInfo.max | number | N |  |
| FieldInfo.readOnly | boolean | N |  |
| FieldInfo.hidden | boolean | N |  |
| FieldInfo.validate | string | N |  |
| FieldInfo.items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |
| FieldInfo.Field | object | N |  |
| Field.id | integer | N |  |
| Field.stackId | integer | N |  |
| Field.sectionId | integer | N |  |
| Field.columnId | string | N |  |
| Field.scope | integer | N |  |
| Field.note | string | N |  |
| Field.priority | integer | N |  |
| Field.path | string | N |  |
| Field.value | string | N |  |
| Field.tenantId | string | N |  |
| Field.updaterId | string | N |  |
| Field.updater | string | N |  |
| Field.creatorId | string | N |  |
| Field.creator | string | N |  |
| Field.createAt | string | N |  |
| Field.updatedAt | string | N |  |
| Field.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "requestId": "requestId",
    "fields": [
        {
            "domain": "domain",
            "group": "group",
            "label": "label",
            "columnNote": "columnNote",
            "placeholder": "placeholder",
            "unit": "unit",
            "necessary": false,
            "columnPath": "columnPath",
            "type": "type",
            "dataType": "dataType",
            "columnValue": "columnValue",
            "min": "min",
            "max": "max",
            "readOnly": false,
            "hidden": false,
            "validate": "validate",
            "items": [
                {
                    "value": "value",
                    "text": "text"
                }
            ],
            "Field": {
                "id": 0,
                "stackId": 0,
                "sectionId": 0,
                "columnId": "columnId",
                "scope": 0,
                "note": "note",
                "priority": 0,
                "path": "path",
                "value": "value",
                "tenantId": "tenantId",
                "updaterId": "updaterId",
                "updater": "updater",
                "creatorId": "creatorId",
                "creator": "creator",
                "createAt": "createAt",
                "updatedAt": "updatedAt",
                "deletedAt": "deletedAt"
            }
        }
    ]
}
```



# **si - version list - API**

## URL: /environment/v1/si/version/list ##

## Method : Post ##

## Event ID : EnvSIVersionList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| tenantId | string |  | N |  |
| latestFlag | boolean |  | N |  |
| serviceInstanceId | integer |  | N |  |
| group | array[SVIGroupFilter] |  | N |  |
| SVIGroupFilter.environmentId | integer |  | N |  |
| SVIGroupFilter.suId | integer |  | N |  |
| SVIGroupFilter.suInstanceId | integer |  | N |  |
| SVIGroupFilter.serviceId | string |  | N |  |
| SVIGroupFilter.serviceType | string |  | N |  |
| environmentId | integer |  | N |  |
| suId | integer |  | N |  |
| suInstanceId | integer |  | N |  |
| serviceCode | string |  | N |  |
| serviceType | string |  | N |  |
| -- | boolean |  | N |  |
| SuIDList | array[uint64] |  | N |  |
| SuInstanceIDList | array[uint64] |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "tenantId": "tenantId",
    "latestFlag": false,
    "serviceInstanceId": 0,
    "group": [
        {
            "environmentId": 0,
            "suId": 0,
            "suInstanceId": 0,
            "serviceId": "serviceId",
            "serviceType": "serviceType"
        }
    ],
    "environmentId": 0,
    "suId": 0,
    "suInstanceId": 0,
    "serviceCode": "serviceCode",
    "serviceType": "serviceType",
    "--": false,
    "SuIDList": [
    ],
    "SuInstanceIDList": [
    ]
}
```

**struct example:**

```
type ServiceInstanceVersionFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    TenantId string `json:"tenantId"`
    LatestFlag bool `json:"latestFlag"`
    ServiceInstanceId uint64 `json:"serviceInstanceId"`
    Group []struct {
            EnvironmentId uint64 `json:"environmentId"`
            SuId uint64 `json:"suId"`
            SuInstanceId uint64 `json:"suInstanceId"`
            ServiceId string `json:"serviceId"`
            ServiceType string `json:"serviceType"`
        } `json:"group"`
    EnvironmentId uint64 `json:"environmentId"`
    SuId uint64 `json:"suId"`
    SuInstanceId uint64 `json:"suInstanceId"`
    ServiceCode string `json:"serviceCode"`
    ServiceType string `json:"serviceType"`
    -- bool `json:"--"`
    SuIDList []uint64 `json:"SuIDList"`
    SuInstanceIDList []uint64 `json:"SuInstanceIDList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| serviceId | string | N |  |
| packageId | integer | N |  |
| appId | integer | N |  |
| suTypeId | integer | N |  |
| name | string | N |  |
| code | string | N |  |
| dbClusterId | string | N |  |
| dbUsername | string | N |  |
| dbPassword | string | N |  |
| dbName | array[string] | N |  |
| serviceType | string | N |  |
| runningStatus | string | N |  |
| serviceUuid | string | N |  |
| status | string | N |  |
| serviceInstanceCode | string | N |  |
| releaseId | integer | N |  |
| suId | integer | N |  |
| suInstanceId | integer | N |  |
| brokerVpnId | integer | N |  |
| meshRole | string | N |  |
| svrType | string | N |  |
| ServiceInstanceVersion | object | N |  |
| ServiceInstanceVersion.id | integer | N |  |
| ServiceInstanceVersion.stackId | integer | N |  |
| ServiceInstanceVersion.envId | integer | N |  |
| ServiceInstanceVersion.instanceId | integer | N |  |
| ServiceInstanceVersion.lastPatchId | integer | N |  |
| ServiceInstanceVersion.baseVersionId | integer | N |  |
| ServiceInstanceVersion.linkVersionId | integer | N |  |
| ServiceInstanceVersion.status | string | N |  |
| ServiceInstanceVersion.journal | string | N |  |
| ServiceInstanceVersion.data | map[string]interface | N |  |
| ServiceInstanceVersion.errorCode | object | N |  |
| map_string_array_ErrorCodes.key | string | N |  |
| map_string_array_ErrorCodes.value | array[ErrorCodes] | N |  |
| ErrorCodes.Code | string | N |  |
| ErrorCodes.Message | string | N |  |
| ServiceInstanceVersion.version | string | N |  |
| ServiceInstanceVersion.description | string | N |  |
| ServiceInstanceVersion.tenantId | string | N |  |
| ServiceInstanceVersion.creatorId | string | N |  |
| ServiceInstanceVersion.creator | string | N |  |
| ServiceInstanceVersion.updaterId | string | N |  |
| ServiceInstanceVersion.updater | string | N |  |
| ServiceInstanceVersion.createAt | string | N |  |
| ServiceInstanceVersion.updatedAt | string | N |  |
| ServiceInstanceVersion.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "serviceId": "serviceId",
    "packageId": 0,
    "appId": 0,
    "suTypeId": 0,
    "name": "name",
    "code": "code",
    "dbClusterId": "dbClusterId",
    "dbUsername": "dbUsername",
    "dbPassword": "dbPassword",
    "dbName": [
    ],
    "serviceType": "serviceType",
    "runningStatus": "runningStatus",
    "serviceUuid": "serviceUuid",
    "status": "status",
    "serviceInstanceCode": "serviceInstanceCode",
    "releaseId": 0,
    "suId": 0,
    "suInstanceId": 0,
    "brokerVpnId": 0,
    "meshRole": "meshRole",
    "svrType": "svrType",
    "ServiceInstanceVersion": {
        "id": 0,
        "stackId": 0,
        "envId": 0,
        "instanceId": 0,
        "lastPatchId": 0,
        "baseVersionId": 0,
        "linkVersionId": 0,
        "status": "status",
        "journal": "journal",
        "data": {
        },
        "errorCode": {
            "key": "key",
            "value": [
                {
                    "Code": "Code",
                    "Message": "Message"
                }
            ]
        },
        "version": "version",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **si - version page - API**

## URL: /environment/v1/si/version/page ##

## Method : Post ##

## Event ID : EnvSIVersionPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| tenantId | string |  | N |  |
| latestFlag | boolean |  | N |  |
| serviceInstanceId | integer |  | N |  |
| group | array[SVIGroupFilter] |  | N |  |
| SVIGroupFilter.environmentId | integer |  | N |  |
| SVIGroupFilter.suId | integer |  | N |  |
| SVIGroupFilter.suInstanceId | integer |  | N |  |
| SVIGroupFilter.serviceId | string |  | N |  |
| SVIGroupFilter.serviceType | string |  | N |  |
| environmentId | integer |  | N |  |
| suId | integer |  | N |  |
| suInstanceId | integer |  | N |  |
| serviceCode | string |  | N |  |
| serviceType | string |  | N |  |
| -- | boolean |  | N |  |
| SuIDList | array[uint64] |  | N |  |
| SuInstanceIDList | array[uint64] |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "tenantId": "tenantId",
    "latestFlag": false,
    "serviceInstanceId": 0,
    "group": [
        {
            "environmentId": 0,
            "suId": 0,
            "suInstanceId": 0,
            "serviceId": "serviceId",
            "serviceType": "serviceType"
        }
    ],
    "environmentId": 0,
    "suId": 0,
    "suInstanceId": 0,
    "serviceCode": "serviceCode",
    "serviceType": "serviceType",
    "--": false,
    "SuIDList": [
    ],
    "SuInstanceIDList": [
    ]
}
```

**struct example:**

```
type ServiceInstanceVersionFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    TenantId string `json:"tenantId"`
    LatestFlag bool `json:"latestFlag"`
    ServiceInstanceId uint64 `json:"serviceInstanceId"`
    Group []struct {
            EnvironmentId uint64 `json:"environmentId"`
            SuId uint64 `json:"suId"`
            SuInstanceId uint64 `json:"suInstanceId"`
            ServiceId string `json:"serviceId"`
            ServiceType string `json:"serviceType"`
        } `json:"group"`
    EnvironmentId uint64 `json:"environmentId"`
    SuId uint64 `json:"suId"`
    SuInstanceId uint64 `json:"suInstanceId"`
    ServiceCode string `json:"serviceCode"`
    ServiceType string `json:"serviceType"`
    -- bool `json:"--"`
    SuIDList []uint64 `json:"SuIDList"`
    SuInstanceIDList []uint64 `json:"SuInstanceIDList"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **si - version parameters - API**

## URL: /environment/v1/si/version/parameters ##

## Method : Post ##

## Event ID : EnvSIVersionParameters ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| instanceVersionId | integer |  | Y | required,gt=0 |
| sectionId | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "instanceVersionId": 0,
    "sectionId": 0
}
```

**struct example:**

```
type QueryServiceInstanceFieldRequest struct {
    InstanceVersionId uint64 `json:"instanceVersionId" validate:"required,gt=0"`
    SectionId uint64 `json:"sectionId" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| requestId | string | N |  |
| fields | array[FieldInfo] | N |  |
| FieldInfo.domain | string | N |  |
| FieldInfo.group | string | N |  |
| FieldInfo.label | string | N |  |
| FieldInfo.columnNote | string | N |  |
| FieldInfo.placeholder | string | N |  |
| FieldInfo.unit | string | N |  |
| FieldInfo.necessary | boolean | N |  |
| FieldInfo.columnPath | string | N |  |
| FieldInfo.type | string | N |  |
| FieldInfo.dataType | string | N |  |
| FieldInfo.columnValue | string | N |  |
| FieldInfo.min | number | N |  |
| FieldInfo.max | number | N |  |
| FieldInfo.readOnly | boolean | N |  |
| FieldInfo.hidden | boolean | N |  |
| FieldInfo.validate | string | N |  |
| FieldInfo.items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |
| FieldInfo.Field | object | N |  |
| Field.id | integer | N |  |
| Field.stackId | integer | N |  |
| Field.sectionId | integer | N |  |
| Field.columnId | string | N |  |
| Field.scope | integer | N |  |
| Field.note | string | N |  |
| Field.priority | integer | N |  |
| Field.path | string | N |  |
| Field.value | string | N |  |
| Field.tenantId | string | N |  |
| Field.updaterId | string | N |  |
| Field.updater | string | N |  |
| Field.creatorId | string | N |  |
| Field.creator | string | N |  |
| Field.createAt | string | N |  |
| Field.updatedAt | string | N |  |
| Field.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "requestId": "requestId",
    "fields": [
        {
            "domain": "domain",
            "group": "group",
            "label": "label",
            "columnNote": "columnNote",
            "placeholder": "placeholder",
            "unit": "unit",
            "necessary": false,
            "columnPath": "columnPath",
            "type": "type",
            "dataType": "dataType",
            "columnValue": "columnValue",
            "min": "min",
            "max": "max",
            "readOnly": false,
            "hidden": false,
            "validate": "validate",
            "items": [
                {
                    "value": "value",
                    "text": "text"
                }
            ],
            "Field": {
                "id": 0,
                "stackId": 0,
                "sectionId": 0,
                "columnId": "columnId",
                "scope": 0,
                "note": "note",
                "priority": 0,
                "path": "path",
                "value": "value",
                "tenantId": "tenantId",
                "updaterId": "updaterId",
                "updater": "updater",
                "creatorId": "creatorId",
                "creator": "creator",
                "createAt": "createAt",
                "updatedAt": "updatedAt",
                "deletedAt": "deletedAt"
            }
        }
    ]
}
```



# **si - version revises - API**

## URL: /environment/v1/si/version/revises ##

## Method : Post ##

## Event ID : EnvSIVersionRevises ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type serviceInstanceVersionIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| serviceId | string | N |  |
| serviceVersionId | integer | N |  |
| fieldId | integer | N |  |
| operate | string | N |  |
| oldValue | string | N |  |
| newValue | string | N |  |
| tenantId | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "serviceId": "serviceId",
    "serviceVersionId": 0,
    "fieldId": 0,
    "operate": "operate",
    "oldValue": "oldValue",
    "newValue": "newValue",
    "tenantId": "tenantId",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **si - version update - API**

## URL: /environment/v1/si/version/update ##

## Method : Post ##

## Event ID : EnvSIVersionUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| instanceVersionId | integer |  | Y | gt=0,required |
| note | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "instanceVersionId": 0,
    "note": "note"
}
```

**struct example:**

```
type UpdateServiceInstanceVersionRequest struct {
    InstanceVersionId uint64 `json:"instanceVersionId" validate:"gt=0,required"`
    Note string `json:"note" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


