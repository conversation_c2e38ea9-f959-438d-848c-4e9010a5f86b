
# **queue - add - API**

## URL: /environment/v1/queue/add ##

## Method : Post ##

## Event ID : EnvQueueAdd ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| instanceId | integer |  | Y | required,gt=0 |
| vpnCode | string |  | N | name,max=32 |
| meshId | integer |  | Y | gt=0,required |
| meshCode | string |  | N | name,max=32 |
| username | string |  | N | name,max=32 |
| password | string |  | N | name,max=32 |
| ttl | integer |  | Y | gt=0,required |
| depth | integer |  | Y | gt=0,required |
| maxUnAckMsg | integer |  | Y | gt=0,required |
| queueName | string |  | Y | required,max=255 |
| eventId | integer |  | Y | gt=0,required |
| eventCode | string |  | N | name,max=32 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "instanceId": 0,
    "vpnCode": "vpnCode",
    "meshId": 0,
    "meshCode": "meshCode",
    "username": "username",
    "password": "password",
    "ttl": 0,
    "depth": 0,
    "maxUnAckMsg": 0,
    "queueName": "queueName",
    "eventId": 0,
    "eventCode": "eventCode",
    "description": "description"
}
```

**struct example:**

```
type AddQueueRequest struct {
    InstanceId uint64 `json:"instanceId" validate:"required,gt=0"`
    VpnCode string `json:"vpnCode" validate:"name,max=32"`
    MeshId uint64 `json:"meshId" validate:"gt=0,required"`
    MeshCode string `json:"meshCode" validate:"name,max=32"`
    Username string `json:"username" validate:"name,max=32"`
    Password string `json:"password" validate:"name,max=32"`
    Ttl int `json:"ttl" validate:"gt=0,required"`
    Depth int `json:"depth" validate:"gt=0,required"`
    MaxUnAckMsg int `json:"maxUnAckMsg" validate:"gt=0,required"`
    QueueName string `json:"queueName" validate:"required,max=255"`
    EventId uint64 `json:"eventId" validate:"gt=0,required"`
    EventCode string `json:"eventCode" validate:"name,max=32"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **queue - delete - API**

## URL: /environment/v1/queue/delete ##

## Method : Post ##

## Event ID : EnvQueueDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type stackIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **queue - detail - API**

## URL: /environment/v1/queue/detail ##

## Method : Post ##

## Event ID : EnvQueueDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type stackIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| stackId | integer | N |  |
| serviceId | string | N |  |
| packageId | integer | N |  |
| appId | integer | N |  |
| suTypeId | integer | N |  |
| name | string | N |  |
| code | string | N |  |
| serviceType | string | N |  |
| version | string | N |  |
| status | string | N |  |
| serviceInstanceCode | string | N |  |
| environmentId | integer | N |  |
| suId | integer | N |  |
| suInstanceId | integer | N |  |
| Queue | object | N |  |
| Queue.id | integer | N |  |
| Queue.instanceId | integer | N |  |
| Queue.vpnCode | string | N |  |
| Queue.meshId | integer | N |  |
| Queue.meshCode | string | N |  |
| Queue.username | string | N |  |
| Queue.password | string | N |  |
| Queue.ttl | integer | N |  |
| Queue.depth | integer | N |  |
| Queue.maxUnAckMsg | integer | N |  |
| Queue.eventId | integer | N |  |
| Queue.eventCode | string | N |  |
| Queue.queueName | string | N |  |
| Queue.status | string | N |  |
| Queue.subscribeStatus | string | N |  |
| Queue.description | string | N |  |
| Queue.tenantId | string | N |  |
| Queue.creatorId | string | N |  |
| Queue.creator | string | N |  |
| Queue.updaterId | string | N |  |
| Queue.updater | string | N |  |
| Queue.createAt | string | N |  |
| Queue.updatedAt | string | N |  |
| Queue.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "stackId": 0,
    "serviceId": "serviceId",
    "packageId": 0,
    "appId": 0,
    "suTypeId": 0,
    "name": "name",
    "code": "code",
    "serviceType": "serviceType",
    "version": "version",
    "status": "status",
    "serviceInstanceCode": "serviceInstanceCode",
    "environmentId": 0,
    "suId": 0,
    "suInstanceId": 0,
    "Queue": {
        "id": 0,
        "instanceId": 0,
        "vpnCode": "vpnCode",
        "meshId": 0,
        "meshCode": "meshCode",
        "username": "username",
        "password": "password",
        "ttl": 0,
        "depth": 0,
        "maxUnAckMsg": 0,
        "eventId": 0,
        "eventCode": "eventCode",
        "queueName": "queueName",
        "status": "status",
        "subscribeStatus": "subscribeStatus",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **queue - list - API**

## URL: /environment/v1/queue/list ##

## Method : Post ##

## Event ID : EnvQueueList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| meshId | integer |  | N |  |
| instanceId | integer |  | N |  |
| tenantId | string |  | N |  |
| vpnCode | string |  | N |  |
| eventCode | string |  | N |  |
| environmentId | integer |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "meshId": 0,
    "instanceId": 0,
    "tenantId": "tenantId",
    "vpnCode": "vpnCode",
    "eventCode": "eventCode",
    "environmentId": 0
}
```

**struct example:**

```
type QueueFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    MeshId uint64 `json:"meshId"`
    InstanceId uint64 `json:"instanceId"`
    TenantId string `json:"tenantId"`
    VpnCode string `json:"vpnCode"`
    EventCode string `json:"eventCode"`
    EnvironmentId uint64 `json:"environmentId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| stackId | integer | N |  |
| serviceId | string | N |  |
| packageId | integer | N |  |
| appId | integer | N |  |
| suTypeId | integer | N |  |
| name | string | N |  |
| code | string | N |  |
| serviceType | string | N |  |
| version | string | N |  |
| status | string | N |  |
| serviceInstanceCode | string | N |  |
| environmentId | integer | N |  |
| suId | integer | N |  |
| suInstanceId | integer | N |  |
| Queue | object | N |  |
| Queue.id | integer | N |  |
| Queue.instanceId | integer | N |  |
| Queue.vpnCode | string | N |  |
| Queue.meshId | integer | N |  |
| Queue.meshCode | string | N |  |
| Queue.username | string | N |  |
| Queue.password | string | N |  |
| Queue.ttl | integer | N |  |
| Queue.depth | integer | N |  |
| Queue.maxUnAckMsg | integer | N |  |
| Queue.eventId | integer | N |  |
| Queue.eventCode | string | N |  |
| Queue.queueName | string | N |  |
| Queue.status | string | N |  |
| Queue.subscribeStatus | string | N |  |
| Queue.description | string | N |  |
| Queue.tenantId | string | N |  |
| Queue.creatorId | string | N |  |
| Queue.creator | string | N |  |
| Queue.updaterId | string | N |  |
| Queue.updater | string | N |  |
| Queue.createAt | string | N |  |
| Queue.updatedAt | string | N |  |
| Queue.deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "stackId": 0,
    "serviceId": "serviceId",
    "packageId": 0,
    "appId": 0,
    "suTypeId": 0,
    "name": "name",
    "code": "code",
    "serviceType": "serviceType",
    "version": "version",
    "status": "status",
    "serviceInstanceCode": "serviceInstanceCode",
    "environmentId": 0,
    "suId": 0,
    "suInstanceId": 0,
    "Queue": {
        "id": 0,
        "instanceId": 0,
        "vpnCode": "vpnCode",
        "meshId": 0,
        "meshCode": "meshCode",
        "username": "username",
        "password": "password",
        "ttl": 0,
        "depth": 0,
        "maxUnAckMsg": 0,
        "eventId": 0,
        "eventCode": "eventCode",
        "queueName": "queueName",
        "status": "status",
        "subscribeStatus": "subscribeStatus",
        "description": "description",
        "tenantId": "tenantId",
        "creatorId": "creatorId",
        "creator": "creator",
        "updaterId": "updaterId",
        "updater": "updater",
        "createAt": "createAt",
        "updatedAt": "updatedAt",
        "deletedAt": "deletedAt"
    }
}
```



# **queue - page - API**

## URL: /environment/v1/queue/page ##

## Method : Post ##

## Event ID : EnvQueuePage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | integer |  | N |  |
| stackId | integer |  | N |  |
| meshId | integer |  | N |  |
| instanceId | integer |  | N |  |
| tenantId | string |  | N |  |
| vpnCode | string |  | N |  |
| eventCode | string |  | N |  |
| environmentId | integer |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": 0,
    "stackId": 0,
    "meshId": 0,
    "instanceId": 0,
    "tenantId": "tenantId",
    "vpnCode": "vpnCode",
    "eventCode": "eventCode",
    "environmentId": 0
}
```

**struct example:**

```
type QueueFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id uint64 `json:"id"`
    StackId uint64 `json:"stackId"`
    MeshId uint64 `json:"meshId"`
    InstanceId uint64 `json:"instanceId"`
    TenantId string `json:"tenantId"`
    VpnCode string `json:"vpnCode"`
    EventCode string `json:"eventCode"`
    EnvironmentId uint64 `json:"environmentId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **queue - update - API**

## URL: /environment/v1/queue/update ##

## Method : Post ##

## Event ID : EnvQueueUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |
| ttl | integer |  | N | omitempty,gte=0 |
| depth | integer |  | N | omitempty,gte=0 |
| maxUnAckMsg | integer |  | N | omitempty,gte=0 |
| status | string |  | N | omitempty,max=32 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "id": 0,
    "ttl": 0,
    "depth": 0,
    "maxUnAckMsg": 0,
    "status": "status",
    "description": "description"
}
```

**struct example:**

```
type UpdateQueueRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
    Ttl int64 `json:"ttl" validate:"omitempty,gte=0"`
    Depth int64 `json:"depth" validate:"omitempty,gte=0"`
    MaxUnAckMsg int64 `json:"maxUnAckMsg" validate:"omitempty,gte=0"`
    Status string `json:"status" validate:"omitempty,max=32"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


