
# **obj_relation - delete - API**

## URL: /environment/v1/obj_relation/delete ##

## Method : Post ##

## Event ID : EnvObjRelationDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type DeleteObjRelationRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **obj_relation - detail - API**

## URL: /environment/v1/obj_relation/detail ##

## Method : Post ##

## Event ID : EnvObjRelationDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | Y | required,gt=0 |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type DetailObjRelationRequest struct {
    Id uint64 `json:"id" validate:"required,gt=0"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| srcCiName | string | N |  |
| srcObjId | integer | N |  |
| tarCiName | string | N |  |
| tarObjId | integer | N |  |
| createdAt | string | N |  |
| creatorId | string | N |  |
| updatedAt | string | N |  |
| updaterId | string | N |  |
| data | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "srcCiName": "srcCiName",
    "srcObjId": 0,
    "tarCiName": "tarCiName",
    "tarObjId": 0,
    "createdAt": "createdAt",
    "creatorId": "creatorId",
    "updatedAt": "updatedAt",
    "updaterId": "updaterId",
    "data": "data"
}
```



# **obj_relation - list - API**

## URL: /environment/v1/obj_relation/list ##

## Method : Post ##

## Event ID : EnvObjRelationList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |
| srcCiName | string |  | N |  |
| srcObjId | integer |  | N |  |
| tarCiName | string |  | N |  |
| tarObjId | integer |  | N |  |
| data | string |  | N |  |

**Range of values**

request example:
```
{
    "id": 0,
    "srcCiName": "srcCiName",
    "srcObjId": 0,
    "tarCiName": "tarCiName",
    "tarObjId": 0,
    "data": "data"
}
```

**struct example:**

```
type ListObjRelationRequest struct {
    Id uint64 `json:"id"`
    SrcCiName string `json:"srcCiName"`
    SrcObjId uint64 `json:"srcObjId"`
    TarCiName string `json:"tarCiName"`
    TarObjId uint64 `json:"tarObjId"`
    Data string `json:"data"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | integer | N |  |
| srcCiName | string | N |  |
| srcObjId | integer | N |  |
| tarCiName | string | N |  |
| tarObjId | integer | N |  |
| createdAt | string | N |  |
| creatorId | string | N |  |
| updatedAt | string | N |  |
| updaterId | string | N |  |
| data | string | N |  |

**Range of values**

response example:

```
{
    "id": 0,
    "srcCiName": "srcCiName",
    "srcObjId": 0,
    "tarCiName": "tarCiName",
    "tarObjId": 0,
    "createdAt": "createdAt",
    "creatorId": "creatorId",
    "updatedAt": "updatedAt",
    "updaterId": "updaterId",
    "data": "data"
}
```



# **obj_relation - modify - API**

## URL: /environment/v1/obj_relation/modify ##

## Method : Post ##

## Event ID : EnvObjRelationModify ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| relations | array[ModifyObjRelation] |  | Y | required |
| ModifyObjRelation.operate | integer |  | N |  |
| ModifyObjRelation.id | integer |  | Y | required_without:SrcCiName,SrcObjId,TarCiName,TarObjId |
| ModifyObjRelation.srcCiName | string |  | Y | required_if:Operate,0 |
| ModifyObjRelation.srcObjId | integer |  | Y | required_if:Operate,0 |
| ModifyObjRelation.tarCiName | string |  | Y | required_if:Operate,0 |
| ModifyObjRelation.tarObjId | integer |  | Y | required_if:Operate,0 |
| ModifyObjRelation.relationMod | integer |  | N |  |
| ModifyObjRelation.data | string |  | N |  |

**Range of values**

request example:
```
{
    "relations": [
        {
            "operate": 0,
            "id": 0,
            "srcCiName": "srcCiName",
            "srcObjId": 0,
            "tarCiName": "tarCiName",
            "tarObjId": 0,
            "relationMod": 0,
            "data": "data"
        }
    ]
}
```

**struct example:**

```
type ModifyObjRelationRequest struct {
    Relations []struct {
            Operate int `json:"operate"`
            Id uint64 `json:"id" validate:"required_without:SrcCiName,SrcObjId,TarCiName,TarObjId"`
            SrcCiName string `json:"srcCiName" validate:"required_if:Operate,0"`
            SrcObjId uint64 `json:"srcObjId" validate:"required_if:Operate,0"`
            TarCiName string `json:"tarCiName" validate:"required_if:Operate,0"`
            TarObjId uint64 `json:"tarObjId" validate:"required_if:Operate,0"`
            RelationMod uint64 `json:"relationMod"`
            Data string `json:"data"`
        } `json:"relations" validate:"required"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **obj_relation - page - API**

## URL: /environment/v1/obj_relation/page ##

## Method : Post ##

## Event ID : EnvObjRelationPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| ListObjRelationRequest | object |  | N |  |
| ListObjRelationRequest.id | integer |  | N |  |
| ListObjRelationRequest.srcCiName | string |  | N |  |
| ListObjRelationRequest.srcObjId | integer |  | N |  |
| ListObjRelationRequest.tarCiName | string |  | N |  |
| ListObjRelationRequest.tarObjId | integer |  | N |  |
| ListObjRelationRequest.data | string |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "ListObjRelationRequest": {
        "id": 0,
        "srcCiName": "srcCiName",
        "srcObjId": 0,
        "tarCiName": "tarCiName",
        "tarObjId": 0,
        "data": "data"
    }
}
```

**struct example:**

```
type QueryObjRelationRequest struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    ListObjRelationRequest struct {
        Id uint64 `json:"id"`
        SrcCiName string `json:"srcCiName"`
        SrcObjId uint64 `json:"srcObjId"`
        TarCiName string `json:"tarCiName"`
        TarObjId uint64 `json:"tarObjId"`
        Data string `json:"data"`
    }
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```


