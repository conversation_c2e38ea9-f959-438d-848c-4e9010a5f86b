
# **column - create - API**

## URL: /environment/v1/column/create ##

## Method : Post ##

## Event ID : EnvColumnCreate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| code | string |  | N | omitempty,max=64 |
| domain | string |  | N | omitempty,max=64 |
| group | string |  | N | omitempty,max=64 |
| label | string |  | N | omitempty,max=64 |
| note | string |  | N | omitempty,max=255 |
| necessary | boolean |  | N | omitempty |
| path | string |  | N | min=2,max=64 |
| type | string |  | N | name,max=64 |
| dataType | string |  | N | name,max=64 |
| value | string |  | N | omitempty |
| items | array[Option] |  | N | omitempty |
| Option.value | string |  | Y | required |
| Option.text | string |  | N | name,max=64 |
| validate | string |  | N | omitempty,max=255 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "code": "code",
    "domain": "domain",
    "group": "group",
    "label": "label",
    "note": "note",
    "necessary": false,
    "path": "path",
    "type": "type",
    "dataType": "dataType",
    "value": "value",
    "items": [
        {
            "value": "value",
            "text": "text"
        }
    ],
    "validate": "validate",
    "description": "description"
}
```

**struct example:**

```
type CreateColumnRequest struct {
    Code string `json:"code" validate:"omitempty,max=64"`
    Domain string `json:"domain" validate:"omitempty,max=64"`
    Group string `json:"group" validate:"omitempty,max=64"`
    Label string `json:"label" validate:"omitempty,max=64"`
    Note string `json:"note" validate:"omitempty,max=255"`
    Necessary bool `json:"necessary" validate:"omitempty"`
    Path string `json:"path" validate:"min=2,max=64"`
    Type string `json:"type" validate:"name,max=64"`
    DataType string `json:"dataType" validate:"name,max=64"`
    Value string `json:"value" validate:"omitempty"`
    Items []struct {
            Value string `json:"value" validate:"required"`
            Text string `json:"text" validate:"name,max=64"`
        } `json:"items" validate:"omitempty"`
    Validate string `json:"validate" validate:"omitempty,max=255"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **column - delete - API**

## URL: /environment/v1/column/delete ##

## Method : Post ##

## Event ID : EnvColumnDelete ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type columnIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```



# **column - detail - API**

## URL: /environment/v1/column/detail ##

## Method : Post ##

## Event ID : EnvColumnDetail ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | integer |  | N |  |

**Range of values**

request example:
```
{
    "id": 0
}
```

**struct example:**

```
type columnIDFilter struct {
    Id int64 `json:"id"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | string | N |  |
| domain | string | N |  |
| group | string | N |  |
| label | string | N |  |
| note | string | N |  |
| necessary | boolean | N |  |
| path | string | N |  |
| type | string | N |  |
| dataType | string | N |  |
| value | string | N |  |
| placeholder | string | N |  |
| unit | string | N |  |
| min | number | N |  |
| max | number | N |  |
| readOnly | boolean | N |  |
| hidden | boolean | N |  |
| items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |
| validate | string | N |  |
| description | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": "id",
    "domain": "domain",
    "group": "group",
    "label": "label",
    "note": "note",
    "necessary": false,
    "path": "path",
    "type": "type",
    "dataType": "dataType",
    "value": "value",
    "placeholder": "placeholder",
    "unit": "unit",
    "min": "min",
    "max": "max",
    "readOnly": false,
    "hidden": false,
    "items": [
        {
            "value": "value",
            "text": "text"
        }
    ],
    "validate": "validate",
    "description": "description",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **column - list - API**

## URL: /environment/v1/column/list ##

## Method : Post ##

## Event ID : EnvColumnList ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | string |  | N |  |
| stackId | integer |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": "id",
    "stackId": 0
}
```

**struct example:**

```
type ColumnFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id string `json:"id"`
    StackId uint64 `json:"stackId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| id | string | N |  |
| domain | string | N |  |
| group | string | N |  |
| label | string | N |  |
| note | string | N |  |
| necessary | boolean | N |  |
| path | string | N |  |
| type | string | N |  |
| dataType | string | N |  |
| value | string | N |  |
| placeholder | string | N |  |
| unit | string | N |  |
| min | number | N |  |
| max | number | N |  |
| readOnly | boolean | N |  |
| hidden | boolean | N |  |
| items | array[Option] | N |  |
| Option.value | string | Y | required |
| Option.text | string | N | name,max=64 |
| validate | string | N |  |
| description | string | N |  |
| updaterId | string | N |  |
| updater | string | N |  |
| creatorId | string | N |  |
| creator | string | N |  |
| createAt | string | N |  |
| updatedAt | string | N |  |
| deletedAt | string | N |  |

**Range of values**

response example:

```
{
    "id": "id",
    "domain": "domain",
    "group": "group",
    "label": "label",
    "note": "note",
    "necessary": false,
    "path": "path",
    "type": "type",
    "dataType": "dataType",
    "value": "value",
    "placeholder": "placeholder",
    "unit": "unit",
    "min": "min",
    "max": "max",
    "readOnly": false,
    "hidden": false,
    "items": [
        {
            "value": "value",
            "text": "text"
        }
    ],
    "validate": "validate",
    "description": "description",
    "updaterId": "updaterId",
    "updater": "updater",
    "creatorId": "creatorId",
    "creator": "creator",
    "createAt": "createAt",
    "updatedAt": "updatedAt",
    "deletedAt": "deletedAt"
}
```



# **column - page - API**

## URL: /environment/v1/column/page ##

## Method : Post ##

## Event ID : EnvColumnPage ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| BaseFilter | object |  | N |  |
| BaseFilter.orderBy | string |  | N |  |
| BaseFilter.pageIndex | integer |  | N |  |
| BaseFilter.pageSize | integer |  | N |  |
| BaseFilter.sort | integer |  | N |  |
| id | string |  | N |  |
| stackId | integer |  | N |  |

**Range of values**

request example:
```
{
    "BaseFilter": {
        "orderBy": "orderBy",
        "pageIndex": 0,
        "pageSize": 0,
        "sort": 0
    },
    "id": "id",
    "stackId": 0
}
```

**struct example:**

```
type ColumnFilter struct {
    BaseFilter struct {
        OrderBy string `json:"orderBy"`
        PageIndex int64 `json:"pageIndex"`
        PageSize int64 `json:"pageSize"`
        Sort int64 `json:"sort"`
    }
    Id string `json:"id"`
    StackId uint64 `json:"stackId"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| data | object | N |  |
| page | integer | N |  |
| pageSize | integer | N |  |
| totalCount | integer | N |  |

**Range of values**

response example:

```
{
    "data": {
    },
    "page": 0,
    "pageSize": 0,
    "totalCount": 0
}
```



# **column - update - API**

## URL: /environment/v1/column/update ##

## Method : Post ##

## Event ID : EnvColumnUpdate ##

**Description:**

describe

### **Request:**

| Field | Type | Length | Required | Notes |
| ----- | ---- | ------ | -------- | ----- |
| id | string |  | Y | required,gt=0 |
| label | string |  | N | omitempty,max=64 |
| note | string |  | N | omitempty,max=255 |
| necessary | boolean |  | N | omitempty |
| path | string |  | N | min=2,max=64 |
| type | string |  | N | name,max=64 |
| dataType | string |  | N | name,max=64 |
| value | string |  | N | omitempty |
| validate | string |  | N | omitempty,max=255 |
| description | string |  | N | omitempty,max=255 |

**Range of values**

request example:
```
{
    "id": "id",
    "label": "label",
    "note": "note",
    "necessary": false,
    "path": "path",
    "type": "type",
    "dataType": "dataType",
    "value": "value",
    "validate": "validate",
    "description": "description"
}
```

**struct example:**

```
type UpdateColumnRequest struct {
    Id string `json:"id" validate:"required,gt=0"`
    Label string `json:"label" validate:"omitempty,max=64"`
    Note string `json:"note" validate:"omitempty,max=255"`
    Necessary bool `json:"necessary" validate:"omitempty"`
    Path string `json:"path" validate:"min=2,max=64"`
    Type string `json:"type" validate:"name,max=64"`
    DataType string `json:"dataType" validate:"name,max=64"`
    Value string `json:"value" validate:"omitempty"`
    Validate string `json:"validate" validate:"omitempty,max=255"`
    Description string `json:"description" validate:"omitempty,max=255"`
}
```

### **Response:**

| Field | Type | Required | Notes |
| ----- | ---- | -------- | ----- |
| errorCode | integer | N |  |
| errorMsg | string | N |  |
| response | object | N |  |

**Range of values**

response example:

```
{
    "errorCode": 0,
    "errorMsg": "errorMsg",
    "response": {
    }
}
```


