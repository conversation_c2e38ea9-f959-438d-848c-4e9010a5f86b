package constants

const (
	SUCCESS = 0

	// level 1
	ERROR = 140000

	// level 2
	ErrorDb      = ERROR + 400
	ErrorRequest = ERROR + 500
	ErrorGitlab  = ERROR + 600

	ErrorCommom = ERROR + 100
	// level 3
	ErrorDbRowNotExists = ErrorDb + 1
	ErrorDbRowExisted   = ErrorDb + 2
	ErrorDbQueryFailed  = ErrorDb + 3
	ErrorDbDeleteFailed = ErrorDb + 4
	ErrorDbCreateFailed = ErrorDb + 5
	ErrorDbUpdateFailed = ErrorDb + 6
	// request package erro
	ErrorRequestCiTypeIsNull             = ErrorRequest + 1
	ERROR_INTERNAL_INIT_CONFIG_FAILED    = ErrorGitlab + 8
	ERROR_GITLAB_CONNECT_FAILED          = ErrorGitlab + 2
	ERROR_GITLAB_CREATEPORJECT_FAILED    = ErrorGitlab + 9
	ERROR_GITLAB_CREATEFILE_FAILED       = ErrorGitlab + 3
	ERROR_GITLAB_UPDATEFILE_FAILED       = ErrorGitlab + 4
	ERROR_GITLAB_GETFILE_FAILED          = ErrorGitlab + 5
	ERROR_GITLAB_DELETEFILE_FAILED       = ErrorGitlab + 6
	ERROR_GITLAB_GETPROJECT_FAILED       = ErrorGitlab + 7
	ERROR_GITLAB_GETRWACONTENT_FAILED    = ErrorGitlab + 10
	ERROR_GITLAB_GETMETAFILESINFO_FAILED = ErrorGitlab + 11
	ERROR_ENVIRONMENT_REQUEST_ERROR      = ErrorRequest + 20
	ERROR_GITLAB_QUERY_GROUP_FAILED      = ErrorRequest + 12
	ERROR_GITLAB_QUERY_RAWFILES_FAILED   = ErrorRequest + 14
	ERROR_GITLAB_ISEXISTSFILE_FAILED     = ErrorGitlab + 13
	ERROR_GITLAB_SGISEXISTS              = ErrorGitlab + 16
	ERROR_GITLAB_GETPROJECTID_FAILED     = ErrorGitlab + 15
)
