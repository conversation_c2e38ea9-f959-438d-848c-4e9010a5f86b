package constants

const (
	FunctionServerless = "serverless"

	StatusDisabled = "Disabled"
	StatusEnabled  = "Enabled"

	ServiceInstanceSvcTypeMacro = "MAS"
)

const (
	RedisKeyPrefixServiceTempalteMapping = "serviceTemplateMapping"

	RedisKeyPrefixNewVersion     = "newVersion"
	RedisKeyPrefixCurrentVersion = "currentVersion"
)

const (
	ObjRelationNameSu           = "SU"
	ObjRelationNameSuInstance   = "SUInstance"
	ObjRelationNameK8sCluster   = "K8SCluster"
	ObjRelationNameMesh         = "Mesh"
	ObjRelationNameNetworkZone  = "NetworkZone"
	ObjRelationNameRDBGroup     = "RDBGroup"
	ObjRelationNameRDBCluster   = "RDBCluster"
	ObjRelationNameVPN          = "VPN"
	ObjRelationNameCacheCluster = "CacheCluster"
)

const (
	AutoTemplateProjectNameCommon = "common"
	AutoTemplateProjectNameMas    = "mas"
)

const (
	PlanTypePublish = "publish"
	PlanTypeUpgrade = "upgrade"
)

const (
	ArtifactTypeService = "service"
)
