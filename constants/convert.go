package constants

var (
	ServiceRuntimeConfigKeyMap = map[string]string{
		"ConfigContent":                "configContent",
		"ImagePath":                    "serviceImage",
		"ImageVersion":                 "serviceVersion",
		"MaxIdleConnections":           "mysqlMaxIdleConns",
		"MaxConnectionLife":            "mysqlMaxLifeValue",
		"MaxOpenConnections":           "mysqlMaxOpenConns",
		"LogLevel":                     "logLevel",
		"LogFileNumber":                "LogFileNumber",
		"CPURequestC":                  "appRequestsCpu",
		"MemoryRequestG":               "appRequestsMemory",
		"MsgQueuedQuotaExpectedMB":     "queueDepthInMb",
		"MaxUnackMsgExpectedNum":       "queueMaxUnackMsgNum",
		"MsgTTLS":                      "queueMsgTTLInSec",
		"MinInstanceNumEachSUInstance": "podReplicas",
		"EnablePrediction":             "Enable_Prediction",
		"BrokerMode":                   "BrokerMode",
		"BrokerNumber":                 "BrokerNumber",
		"CPULimitC":                    "appLimitsCpu",
		"MemoryLimitG":                 "appLimitsMemory",
		"serviceImageName":             "serviceImageName",
	}
	ServiceRuntimeConfigValueMap = map[string]string{
		"configContent":       "ConfigContent",
		"serviceImage":        "ImagePath",
		"serviceVersion":      "ImageVersion",
		"mysqlMaxIdleConns":   "MaxIdleConnections",
		"mysqlMaxLifeValue":   "MaxConnectionLife",
		"mysqlMaxOpenConns":   "MaxOpenConnections",
		"logLevel":            "LogLevel",
		"LogFileNumber":       "LogFileNumber",
		"appRequestsCpu":      "CPURequestC",
		"appRequestsMemory":   "MemoryRequestG",
		"queueDepthInMb":      "MsgQueuedQuotaExpectedMB",
		"queueMaxUnackMsgNum": "MaxUnackMsgExpectedNum",
		"queueMsgTTLInSec":    "MsgTTLS",
		"podReplicas":         "MinInstanceNumEachSUInstance",
		"Enable_Prediction":   "EnablePrediction",
		"BrokerMode":          "BrokerMode",
		"BrokerNumber":        "BrokerNumber",
		"appLimitsCpu":        "CPULimitC",
		"appLimitsMemory":     "MemoryLimitG",
		"serviceImageName":    "serviceImageName",
	}
)
