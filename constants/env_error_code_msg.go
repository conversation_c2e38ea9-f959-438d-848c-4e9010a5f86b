package constants

var (
	ErrorCodeMsgMap = map[int]string{
		SUCCESS: "",

		// db
		ErrorDbRowNotExists: "the environment item is not exist. please check again.",
		ErrorDbRowExisted:   "the environment item is existed. please check again.",
		ErrorDbQueryFailed:  "query failed.",
		ErrorDbDeleteFailed: "delete failed.",

		// request package error msg
		ErrorRequestCiTypeIsNull:          "ciType is null.",
		ERROR_INTERNAL_INIT_CONFIG_FAILED: "Failed to init internal http client.",
		ERROR_GITLAB_CONNECT_FAILED:       "Connect gitlab is failed.",
	}
)
