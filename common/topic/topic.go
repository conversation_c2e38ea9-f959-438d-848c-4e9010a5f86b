package topic

const (
	InitDatabaseTopic   = "AutoRdbDatabaseInit"
	UpdateDatabaseTopic = "AutoRdbDatabaseUpdate"

	SpecQueryHarborTopic      = "AutoImageTags"
	ImageProjectInfoTopic     = "AutoImageProjectInfo"
	SpecQueryTopic            = "AutoSpecQuery"
	DeployServicesTopic       = "AutoServiceDeploy"
	UpgradeServicesTopic      = "AutoServiceUpgrade"
	DestroyServicesTopic      = "AutoServiceDestroy"
	GetResourceProviderDetail = "AutoResourceProviderDetail"
	CreateKeyOnAwsKms         = "RES0000024"
	GetKeyDetailOnAwsKms      = "RES0000025"
	CreateCustomJobTopic      = "AutoDriverCreateCustomJob"
	ModifyPodReplicasTopic    = "AutoK8sModifyPodReplicas"
	BindResourceGroupTopic    = "AutoBindResGroup"
	AutoResourceCheckTopic    = "AutoResourceCheck"
	AutoStackCheckPortTopic   = "AutoStackCheckPort"
	AutoGetJobInfoTopic       = "AutoProcessJobInfo"
	AutoSopAutoScaleTopic     = "AutoSopAutoScale"

	// InsertObjRelationTopic = "AddObjRelation"
	// InsertSpecDataTopic    = "AddDataObject"
	// InsertAndLinkSpecDataTopic = "AddDataObjectAndCreateRef"
	// UpdateSpecDataTopic        = "UpdateSpecDataObject"
	// UpsertSpecDataTopic        = "UpsertDataObject"
	// QuerySpecDataTopic         = "QueryDataObject"
	// DeleteSpecDataTopic        = "DeleteDataObject"
	// QueryContextCallChainTopic = "QueryContextCallChain" // 查询服务上下游
	// GetSpecforDeployTopic      = "GetDeploySpec"         // 查询服务部署信息
	// SpecMultiTableQueryTopic   = "MultiTableQuery"
	//  BroadcastRelationEventTopic = "RelationUpdatedEvent" // 广播上下游关系已经更新的消息

	APISpecQueryTopic = "QueryAPIObject"

	QueryGlsPrepareSync = "GlsPrepareSync"
	GlsPrepareSyncTopic = "PrepareGlsSyncAll"
	GlsConfirmSyncTopic = "ConfirmGlsSyncAll"
	GlsExportSyncTopic  = "ExportGlsSyncAll"

	HarborMultiverseRoot = "multiverse"

	ScheduleProcessTopic         = "EnvPlanProcess"
	ScheduleEnvProcessTopic      = "EnvEnvironmentProcess"
	ScheduleFunctionProcessTopic = "EnvFunctionProcess"

	DetailArtifact         = "DetailArtifact"
	AOGGeneralChannelTopic = "AOGGeneralChannel"
)

const (
	GetBrokerClientCountTopic = "EMS_QUERY_CLIENT_COUNT"
	GetDmrClusterInfoTopic    = "RES0000041"
)
