package register

import (
	"sync"
)

var (
	CrudInter *CrudInterRegister
	once      sync.Once
	lock      sync.Mutex
)

type InterRegister interface {
	Register(crudInterName string, srv interface{})
}

func NewInterRegister() *CrudInterRegister {
	once.Do(func() {
		CrudInter = &CrudInterRegister{
			InterHandlers: make(map[string]interface{}),
		}
	})

	return CrudInter
}

type CrudInterRegister struct {
	InterHandlers map[string]interface{}
}

/**
* @Desc Topic Id to Service Register
* @Author: O'Neal
* @Date: 2019-10-14 14:25
* @Param: topicId string topic Id
* @Param: srv interface service Name
* @Return:
 */
func (e CrudInterRegister) Register(crudInterName string, srv interface{}) {
	lock.Lock()
	defer func() {
		lock.Unlock()
	}()

	e.InterHandlers[crudInterName] = srv
}
