/*
 * @Author: Ning <PERSON> <EMAIL>
 * @Date: 2023-05-26 10:32:44
 * @LastEditors: Ning <PERSON>
 * @LastEditTime: 2023-06-06 04:42:22
 * @Description: create by nanili
 */
package errcode

var (
	Success                = 0
	ErrorCodeOffset        = 500000
	ENVErrorCode           = ErrorCodeOffset
	UnCaughtExceptionError = ErrorCodeOffset + 1 // 未捕获异常错误
	InternalError          = ErrorCodeOffset + 2 // 内部异常

	CommonError            = ErrorCodeOffset + 100  // 一般错误起始编码，比如参数错误等
	AuthorityError         = ErrorCodeOffset + 200  // 授权相关错误起始编码
	ThirdModuleError       = ErrorCodeOffset + 300  // 第三方组件相关错误起始编码
	DataBaseError          = ErrorCodeOffset + 500  // 数据库错误起始编码
	ChangePlanError        = ErrorCodeOffset + 600  // 变更计划的错误码
	GitlabError            = ErrorCodeOffset + 700  // 变更计划的错误码
	TemplateError          = ErrorCodeOffset + 800  // 模板管理的错误码
	SolaceError            = ErrorCodeOffset + 1500 // solace相关错误起始编码
	ExternalInterfaceError = ErrorCodeOffset + 1600 // 外部接口错误
	ScheduleError          = ErrorCodeOffset + 2000 // 调度任务错误码
	UPMError               = ErrorCodeOffset + 900  // sync 2 upm errorcode
	ResourceError          = ErrorCodeOffset + 1000 // 资源相关的错误

	// 一般错误起始编码，比如参数错误等
	InvalidParameter      = CommonError + 1 // 无效的参数
	InvalidResponse       = CommonError + 2 // 无效的返回值
	FileOperateFail       = CommonError + 3 // 文件操作错误
	AnsibleExecFail       = CommonError + 4 // ansible 执行失败
	SSHCertFileNotExist   = CommonError + 5 // ssh 证书不存在
	ParameterValidFail    = CommonError + 6
	JsonUnmarshalFail     = CommonError + 7  // json Unmarshal失败
	InvalidK8sTemplate    = CommonError + 8  // 模板数据无效
	InvalidTemplateConfig = CommonError + 9  // 模板数据无效
	AssertFail            = CommonError + 10 // 断言失败
	InvalidOperate        = CommonError + 11 // 无效的操作
	YamlUnmarshalFail     = CommonError + 12 // yaml Unmarshal失败
	JsonMarshalFail       = CommonError + 13 // json Unmarshal失败
	GitOperateFail        = CommonError + 14 // git 操作失败
	UnexpectedValue       = CommonError + 15 // 非期望的值
	TomlUnmarshalFail     = CommonError + 12 // toml Unmarshal失败

	HarborInterfaceError = ThirdModuleError + 1 // Harbor错误

	// 数据库相关错误起始编码
	InitDaoFail         = DataBaseError + 0 //
	RecordNotExist      = DataBaseError + 1 // 记录不存在
	DeleteRecordFail    = DataBaseError + 2 // 删除记录失败
	InsertRecordFail    = DataBaseError + 3 // 新增记录失败
	UpdateRecordFail    = DataBaseError + 4 // 更新记录失败
	QueryRecordFail     = DataBaseError + 5 // 查询记录失败
	GetQueryHandlerFail = DataBaseError + 6 //
	GenerateFilterFail  = DataBaseError + 7 //
	CompareRecordFail   = DataBaseError + 8 // 对比生成ddl语句失败
	DuplicateRecordFail = DataBaseError + 9 // 重复的记录

	// 授权相关错误起始编码
	NoSuchAPI             = AuthorityError + 7
	InvalidToken          = AuthorityError + 8  // 无效的token
	TokenExpired          = AuthorityError + 9  // token过期
	NoTokenParameter      = AuthorityError + 10 // 请求缺少token
	PermissionDenied      = AuthorityError + 11 // 用户权限不够
	UndefinedRing         = AuthorityError + 12 // 未定义的ring
	InvalidTopic          = AuthorityError + 12 // 无效的Topic
	PermissionOutOfDesign = AuthorityError + 13 // 超出设计的赋权操作
	InvalidRequestClaim   = AuthorityError + 14 // 无效的访问申请
	OperatorInvalid       = AuthorityError + 15 // 用户不属于操作的组
	PermissionNotExist    = AuthorityError + 16 // 权限规则不存在

	// 变更计划相关的错误码
	PatchNotMatchError        = ChangePlanError + 1  // 无效的访问申请
	VersionNotExistError      = ChangePlanError + 2  // 版本不存在
	AddRouteFail              = ChangePlanError + 3  // 给gateway添加路由失败
	ServiceNotExistError      = ChangePlanError + 4  // Mesh not exist
	MeshNotExistError         = ChangePlanError + 5  // Mesh not exist
	PropertyNotExistError     = ChangePlanError + 6  // Property Not Exist
	GenerateQueueAndTopicFail = ChangePlanError + 7  // 给服务生成queue，topic失败
	SetErrorCodeForSIFail     = ChangePlanError + 8  // 给服务实例设置错误码
	TargetVersionNotExist     = ChangePlanError + 9  // 目标版本不存在
	MacroServiceConfigError   = ChangePlanError + 10 // 目标版本不存在
	RepeatDeploymentError     = ChangePlanError + 11 // 重复部署

	// Gitlab 相关错误码
	GroupDeleteFail     = GitlabError + 1
	GroupCreateFail     = GitlabError + 2
	ProjectDeleteFail   = GitlabError + 3
	ProjectCreateFail   = GitlabError + 4
	WriteFileFail       = GitlabError + 5
	GetFileMetadataFail = GitlabError + 6
	UpdateFileFail      = GitlabError + 7

	LoadTemplatePackageFail = TemplateError + 1

	SendMessageFailError   = SolaceError + 1 // 发送消息失败
	ParseResponseFailError = SolaceError + 2 // 解析返回消息内容失败

	SpecUpdateCIError         = ExternalInterfaceError + 1 // Spec 更新CI接口失败
	SpecCIInsertError         = ExternalInterfaceError + 2 // Spec 插入CI接口失败
	SpecNotExistError         = ExternalInterfaceError + 3 // Spec 插入CI接口失败
	SpecRelationNotExistError = ExternalInterfaceError + 4 // Spec 插入CI接口失败
	SpecQueryExistError       = ExternalInterfaceError + 5 // Spec 查询CI接口失败

	PrcQueryError = ExternalInterfaceError + 6 // PRC 查询接口失败

	NotCurrentJob = ScheduleError + 1

	/*
		upm errorcode
	*/
	Sync2UPMError = UPMError + 1 // sync to upm error

	ResourceGroupOccupiedError = ResourceError + 1
	RDBNotSpecifiedError       = ResourceError + 2
)

var errorCodeMessageMap = make(map[int]string)

func init() {
	errorCodeMessageMap[ErrorCodeOffset] = "error"
	errorCodeMessageMap[ENVErrorCode] = "error"
	errorCodeMessageMap[UnCaughtExceptionError] = "uncaught exception error"
	errorCodeMessageMap[InternalError] = "internal error"
	errorCodeMessageMap[CommonError] = "common error"
	errorCodeMessageMap[AuthorityError] = "authority error"
	errorCodeMessageMap[ThirdModuleError] = "third module error"
	errorCodeMessageMap[DataBaseError] = "database error"
	errorCodeMessageMap[ChangePlanError] = "change plan error"
	errorCodeMessageMap[GitlabError] = "gitlab error"
	errorCodeMessageMap[TemplateError] = "template error"
	errorCodeMessageMap[SolaceError] = "solace error"
	errorCodeMessageMap[ExternalInterfaceError] = "external interface error"
	errorCodeMessageMap[ScheduleError] = "schedule error"
	errorCodeMessageMap[UPMError] = "UPM error"
	errorCodeMessageMap[InvalidParameter] = "invalid parameter"
	errorCodeMessageMap[InvalidResponse] = "invalid response"
	errorCodeMessageMap[FileOperateFail] = "file operate fail"
	errorCodeMessageMap[AnsibleExecFail] = "ansible exec fail"
	errorCodeMessageMap[SSHCertFileNotExist] = "SSH cert file not exist"
	errorCodeMessageMap[ParameterValidFail] = "parameter valid fail"
	errorCodeMessageMap[JsonUnmarshalFail] = "json unmarshal fail"
	errorCodeMessageMap[InvalidK8sTemplate] = "invalid kubernetes template"
	errorCodeMessageMap[InvalidTemplateConfig] = "invalid template config"
	errorCodeMessageMap[AssertFail] = "assert fail"
	errorCodeMessageMap[InvalidOperate] = "invalid operate"
	errorCodeMessageMap[YamlUnmarshalFail] = "yaml unmarshal fail"
	errorCodeMessageMap[TomlUnmarshalFail] = "toml unmarshal fail"
	errorCodeMessageMap[JsonMarshalFail] = "json marshal fail"
	errorCodeMessageMap[GitOperateFail] = "git operate fail"
	errorCodeMessageMap[UnexpectedValue] = "unexpected value"
	errorCodeMessageMap[HarborInterfaceError] = "harbor interface error"
	errorCodeMessageMap[InitDaoFail] = "init dao fail"
	errorCodeMessageMap[RecordNotExist] = "record not exist"
	errorCodeMessageMap[DeleteRecordFail] = "delete record fail"
	errorCodeMessageMap[InsertRecordFail] = "insert record fail"
	errorCodeMessageMap[UpdateRecordFail] = "update record fail"
	errorCodeMessageMap[QueryRecordFail] = "query record fail"
	errorCodeMessageMap[DuplicateRecordFail] = "Duplicate record"
	errorCodeMessageMap[GetQueryHandlerFail] = "get query handler fail"
	errorCodeMessageMap[GenerateFilterFail] = "generate filter fail"
	errorCodeMessageMap[CompareRecordFail] = "compare record fail"
	errorCodeMessageMap[NoSuchAPI] = "no such API"
	errorCodeMessageMap[InvalidToken] = "invalid token"
	errorCodeMessageMap[TokenExpired] = "token expired"
	errorCodeMessageMap[NoTokenParameter] = "no token parameter"
	errorCodeMessageMap[PermissionDenied] = "permission denied"
	errorCodeMessageMap[UndefinedRing] = "undefined ring"
	errorCodeMessageMap[InvalidTopic] = "invalid topic"
	errorCodeMessageMap[PermissionOutOfDesign] = "permission out of design"
	errorCodeMessageMap[InvalidRequestClaim] = "invalid request claim"
	errorCodeMessageMap[OperatorInvalid] = "operator invalid"
	errorCodeMessageMap[PermissionNotExist] = "permission not exist"
	errorCodeMessageMap[PatchNotMatchError] = "patch not match"
	errorCodeMessageMap[VersionNotExistError] = "version not exist"
	errorCodeMessageMap[AddRouteFail] = "add route fail"
	errorCodeMessageMap[ServiceNotExistError] = "service not exist"
	errorCodeMessageMap[MeshNotExistError] = "mesh not exist"
	errorCodeMessageMap[PropertyNotExistError] = "property not exist"
	errorCodeMessageMap[GenerateQueueAndTopicFail] = "generate queue and topic fail"
	errorCodeMessageMap[SetErrorCodeForSIFail] = "set errorcode for SI fail"
	errorCodeMessageMap[GroupDeleteFail] = "group delete fail"
	errorCodeMessageMap[GroupCreateFail] = "group create fail"
	errorCodeMessageMap[ProjectDeleteFail] = "project delete fail"
	errorCodeMessageMap[ProjectCreateFail] = "project create fail"
	errorCodeMessageMap[WriteFileFail] = "write file fail"
	errorCodeMessageMap[GetFileMetadataFail] = "get file metadata fail"
	errorCodeMessageMap[UpdateFileFail] = "update file fail"
	errorCodeMessageMap[LoadTemplatePackageFail] = "load template package fail"
	errorCodeMessageMap[SendMessageFailError] = "send message fail"
	errorCodeMessageMap[ParseResponseFailError] = "parse response fail"
	errorCodeMessageMap[SpecUpdateCIError] = "spec update CI error"
	errorCodeMessageMap[SpecCIInsertError] = "spec CI insert error"
	errorCodeMessageMap[SpecNotExistError] = "spec not exist"
	errorCodeMessageMap[SpecRelationNotExistError] = "spec relation not exist"
	errorCodeMessageMap[SpecQueryExistError] = "spec query exist error"
	errorCodeMessageMap[NotCurrentJob] = "not current job"
	errorCodeMessageMap[Sync2UPMError] = "sync to UPM error"
	errorCodeMessageMap[ResourceGroupOccupiedError] = "Resource group occupied"
	errorCodeMessageMap[MacroServiceConfigError] = "The macro service failed to collect parameters"
	errorCodeMessageMap[RepeatDeploymentError] = "The current service cannot deploy multiple instances"
	errorCodeMessageMap[RDBNotSpecifiedError] = "No RDB cluster is specified for DAS service"
}

func SetErrorCodeMessage(errorcode int, errormessage string) {
	errorCodeMessageMap[errorcode] = errormessage
}

func GetErrorCodeMessage(errorcode int) string {
	msg, ok := errorCodeMessageMap[errorcode]
	if !ok {
		return "unknown errormessage"
	}
	return msg
}
