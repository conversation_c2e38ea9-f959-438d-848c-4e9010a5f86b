package validate

import (
	"errors"
	"fmt"

	"github.com/go-playground/locales"
	"github.com/go-playground/locales/en"
	"github.com/go-playground/locales/en_001"
	"github.com/go-playground/locales/en_150"
	"github.com/go-playground/locales/en_AG"
	"github.com/go-playground/locales/en_AI"
	"github.com/go-playground/locales/en_AS"
	"github.com/go-playground/locales/en_AT"
	"github.com/go-playground/locales/en_AU"
	"github.com/go-playground/locales/en_BB"
	"github.com/go-playground/locales/en_BE"
	"github.com/go-playground/locales/en_BI"
	"github.com/go-playground/locales/en_BM"
	"github.com/go-playground/locales/en_BS"
	"github.com/go-playground/locales/en_BW"
	"github.com/go-playground/locales/en_BZ"
	"github.com/go-playground/locales/en_CA"
	"github.com/go-playground/locales/en_CC"
	"github.com/go-playground/locales/en_CH"
	"github.com/go-playground/locales/en_CK"
	"github.com/go-playground/locales/en_CM"
	"github.com/go-playground/locales/en_CX"
	"github.com/go-playground/locales/en_CY"
	"github.com/go-playground/locales/en_DE"
	"github.com/go-playground/locales/en_DG"
	"github.com/go-playground/locales/en_DK"
	"github.com/go-playground/locales/en_DM"
	"github.com/go-playground/locales/en_ER"
	"github.com/go-playground/locales/en_FI"
	"github.com/go-playground/locales/en_FJ"
	"github.com/go-playground/locales/en_FK"
	"github.com/go-playground/locales/en_FM"
	"github.com/go-playground/locales/en_GB"
	"github.com/go-playground/locales/en_GD"
	"github.com/go-playground/locales/en_GG"
	"github.com/go-playground/locales/en_GH"
	"github.com/go-playground/locales/en_GI"
	"github.com/go-playground/locales/en_GM"
	"github.com/go-playground/locales/en_GU"
	"github.com/go-playground/locales/en_GY"
	"github.com/go-playground/locales/en_HK"
	"github.com/go-playground/locales/en_IE"
	"github.com/go-playground/locales/en_IL"
	"github.com/go-playground/locales/en_IM"
	"github.com/go-playground/locales/en_IN"
	"github.com/go-playground/locales/en_IO"
	"github.com/go-playground/locales/en_JE"
	"github.com/go-playground/locales/en_JM"
	"github.com/go-playground/locales/en_KE"
	"github.com/go-playground/locales/en_KI"
	"github.com/go-playground/locales/en_KN"
	"github.com/go-playground/locales/en_KY"
	"github.com/go-playground/locales/en_LC"
	"github.com/go-playground/locales/en_LR"
	"github.com/go-playground/locales/en_LS"
	"github.com/go-playground/locales/en_MG"
	"github.com/go-playground/locales/en_MH"
	"github.com/go-playground/locales/en_MO"
	"github.com/go-playground/locales/en_MP"
	"github.com/go-playground/locales/en_MS"
	"github.com/go-playground/locales/en_MT"
	"github.com/go-playground/locales/en_MU"
	"github.com/go-playground/locales/en_MW"
	"github.com/go-playground/locales/en_MY"
	"github.com/go-playground/locales/en_NA"
	"github.com/go-playground/locales/en_NF"
	"github.com/go-playground/locales/en_NG"
	"github.com/go-playground/locales/en_NL"
	"github.com/go-playground/locales/en_NR"
	"github.com/go-playground/locales/en_NU"
	"github.com/go-playground/locales/en_NZ"
	"github.com/go-playground/locales/en_PG"
	"github.com/go-playground/locales/en_PH"
	"github.com/go-playground/locales/en_PK"
	"github.com/go-playground/locales/en_PN"
	"github.com/go-playground/locales/en_PR"
	"github.com/go-playground/locales/en_PW"
	"github.com/go-playground/locales/en_RW"
	"github.com/go-playground/locales/en_SB"
	"github.com/go-playground/locales/en_SC"
	"github.com/go-playground/locales/en_SD"
	"github.com/go-playground/locales/en_SE"
	"github.com/go-playground/locales/en_SG"
	"github.com/go-playground/locales/en_SH"
	"github.com/go-playground/locales/en_SI"
	"github.com/go-playground/locales/en_SL"
	"github.com/go-playground/locales/en_SS"
	"github.com/go-playground/locales/en_SX"
	"github.com/go-playground/locales/en_SZ"
	"github.com/go-playground/locales/en_TC"
	"github.com/go-playground/locales/en_TK"
	"github.com/go-playground/locales/en_TO"
	"github.com/go-playground/locales/en_TT"
	"github.com/go-playground/locales/en_TV"
	"github.com/go-playground/locales/en_TZ"
	"github.com/go-playground/locales/en_UG"
	"github.com/go-playground/locales/en_UM"
	"github.com/go-playground/locales/en_US"
	"github.com/go-playground/locales/en_US_POSIX"
	"github.com/go-playground/locales/en_VC"
	"github.com/go-playground/locales/en_VG"
	"github.com/go-playground/locales/en_VI"
	"github.com/go-playground/locales/en_VU"
	"github.com/go-playground/locales/en_WS"
	"github.com/go-playground/locales/en_ZA"
	"github.com/go-playground/locales/en_ZM"
	"github.com/go-playground/locales/en_ZW"
	"github.com/go-playground/locales/fr"
	"github.com/go-playground/locales/fr_BE"
	"github.com/go-playground/locales/fr_BF"
	"github.com/go-playground/locales/fr_BI"
	"github.com/go-playground/locales/fr_BJ"
	"github.com/go-playground/locales/fr_BL"
	"github.com/go-playground/locales/fr_CA"
	"github.com/go-playground/locales/fr_CD"
	"github.com/go-playground/locales/fr_CF"
	"github.com/go-playground/locales/fr_CG"
	"github.com/go-playground/locales/fr_CH"
	"github.com/go-playground/locales/fr_CI"
	"github.com/go-playground/locales/fr_CM"
	"github.com/go-playground/locales/fr_DJ"
	"github.com/go-playground/locales/fr_DZ"
	"github.com/go-playground/locales/fr_FR"
	"github.com/go-playground/locales/fr_GA"
	"github.com/go-playground/locales/fr_GF"
	"github.com/go-playground/locales/fr_GN"
	"github.com/go-playground/locales/fr_GP"
	"github.com/go-playground/locales/fr_GQ"
	"github.com/go-playground/locales/fr_HT"
	"github.com/go-playground/locales/fr_KM"
	"github.com/go-playground/locales/fr_LU"
	"github.com/go-playground/locales/fr_MA"
	"github.com/go-playground/locales/fr_MC"
	"github.com/go-playground/locales/fr_MF"
	"github.com/go-playground/locales/fr_MG"
	"github.com/go-playground/locales/fr_ML"
	"github.com/go-playground/locales/fr_MQ"
	"github.com/go-playground/locales/fr_MR"
	"github.com/go-playground/locales/fr_MU"
	"github.com/go-playground/locales/fr_NC"
	"github.com/go-playground/locales/fr_NE"
	"github.com/go-playground/locales/fr_PF"
	"github.com/go-playground/locales/fr_PM"
	"github.com/go-playground/locales/fr_RE"
	"github.com/go-playground/locales/fr_RW"
	"github.com/go-playground/locales/fr_SC"
	"github.com/go-playground/locales/fr_SN"
	"github.com/go-playground/locales/fr_SY"
	"github.com/go-playground/locales/fr_TD"
	"github.com/go-playground/locales/fr_TG"
	"github.com/go-playground/locales/fr_TN"
	"github.com/go-playground/locales/fr_VU"
	"github.com/go-playground/locales/fr_WF"
	"github.com/go-playground/locales/fr_YT"
	"github.com/go-playground/locales/id"
	"github.com/go-playground/locales/id_ID"
	"github.com/go-playground/locales/ja"
	"github.com/go-playground/locales/ja_JP"
	"github.com/go-playground/locales/nl"
	"github.com/go-playground/locales/nl_AW"
	"github.com/go-playground/locales/nl_BE"
	"github.com/go-playground/locales/nl_BQ"
	"github.com/go-playground/locales/nl_CW"
	"github.com/go-playground/locales/nl_NL"
	"github.com/go-playground/locales/nl_SR"
	"github.com/go-playground/locales/nl_SX"
	"github.com/go-playground/locales/pt_BR"
	"github.com/go-playground/locales/pt_CH"
	"github.com/go-playground/locales/pt_CV"
	"github.com/go-playground/locales/pt_GQ"
	"github.com/go-playground/locales/pt_GW"
	"github.com/go-playground/locales/pt_LU"
	"github.com/go-playground/locales/pt_MO"
	"github.com/go-playground/locales/pt_MZ"
	"github.com/go-playground/locales/pt_PT"
	"github.com/go-playground/locales/pt_ST"
	"github.com/go-playground/locales/pt_TL"
	"github.com/go-playground/locales/zh"
	"github.com/go-playground/locales/zh_Hans"
	"github.com/go-playground/locales/zh_Hans_CN"
	"github.com/go-playground/locales/zh_Hans_HK"
	"github.com/go-playground/locales/zh_Hans_MO"
	"github.com/go-playground/locales/zh_Hans_SG"
	"github.com/go-playground/locales/zh_Hant"
	"github.com/go-playground/locales/zh_Hant_HK"
	"github.com/go-playground/locales/zh_Hant_MO"
	"github.com/go-playground/locales/zh_Hant_TW"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	translations_en "github.com/go-playground/validator/v10/translations/en"
	translations_fr "github.com/go-playground/validator/v10/translations/fr"
	translations_id "github.com/go-playground/validator/v10/translations/id"
	translations_ja "github.com/go-playground/validator/v10/translations/ja"
	translations_nl "github.com/go-playground/validator/v10/translations/nl"
	translations_pt_BR "github.com/go-playground/validator/v10/translations/pt_BR"
	translations_zh "github.com/go-playground/validator/v10/translations/zh"
	translations_zh_tw "github.com/go-playground/validator/v10/translations/zh_tw"
)

type NewTranslatorFunc func() locales.Translator
type RegistTranslationsFunc func(v *validator.Validate, trans ut.Translator) (err error)

type TranslatorWrapper struct {
	NewTranslator      NewTranslatorFunc
	RegistTranslations RegistTranslationsFunc
}

var translatorModuleMap = make(map[string]*TranslatorWrapper)

func init() {
	// 英语
	translatorModuleMap[LOCALES_EN] = &TranslatorWrapper{NewTranslator: en.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_001] = &TranslatorWrapper{NewTranslator: en_001.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_150] = &TranslatorWrapper{NewTranslator: en_150.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_AG] = &TranslatorWrapper{NewTranslator: en_AG.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_AI] = &TranslatorWrapper{NewTranslator: en_AI.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_AS] = &TranslatorWrapper{NewTranslator: en_AS.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_AT] = &TranslatorWrapper{NewTranslator: en_AT.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_AU] = &TranslatorWrapper{NewTranslator: en_AU.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_BB] = &TranslatorWrapper{NewTranslator: en_BB.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_BE] = &TranslatorWrapper{NewTranslator: en_BE.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_BI] = &TranslatorWrapper{NewTranslator: en_BI.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_BM] = &TranslatorWrapper{NewTranslator: en_BM.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_BS] = &TranslatorWrapper{NewTranslator: en_BS.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_BW] = &TranslatorWrapper{NewTranslator: en_BW.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_BZ] = &TranslatorWrapper{NewTranslator: en_BZ.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_CA] = &TranslatorWrapper{NewTranslator: en_CA.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_CC] = &TranslatorWrapper{NewTranslator: en_CC.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_CH] = &TranslatorWrapper{NewTranslator: en_CH.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_CK] = &TranslatorWrapper{NewTranslator: en_CK.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_CM] = &TranslatorWrapper{NewTranslator: en_CM.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_CX] = &TranslatorWrapper{NewTranslator: en_CX.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_CY] = &TranslatorWrapper{NewTranslator: en_CY.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_DE] = &TranslatorWrapper{NewTranslator: en_DE.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_DG] = &TranslatorWrapper{NewTranslator: en_DG.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_DK] = &TranslatorWrapper{NewTranslator: en_DK.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_DM] = &TranslatorWrapper{NewTranslator: en_DM.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_ER] = &TranslatorWrapper{NewTranslator: en_ER.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_FI] = &TranslatorWrapper{NewTranslator: en_FI.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_FJ] = &TranslatorWrapper{NewTranslator: en_FJ.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_FK] = &TranslatorWrapper{NewTranslator: en_FK.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_FM] = &TranslatorWrapper{NewTranslator: en_FM.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_GB] = &TranslatorWrapper{NewTranslator: en_GB.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_GD] = &TranslatorWrapper{NewTranslator: en_GD.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_GG] = &TranslatorWrapper{NewTranslator: en_GG.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_GH] = &TranslatorWrapper{NewTranslator: en_GH.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_GI] = &TranslatorWrapper{NewTranslator: en_GI.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_GM] = &TranslatorWrapper{NewTranslator: en_GM.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_GU] = &TranslatorWrapper{NewTranslator: en_GU.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_GY] = &TranslatorWrapper{NewTranslator: en_GY.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_HK] = &TranslatorWrapper{NewTranslator: en_HK.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_IE] = &TranslatorWrapper{NewTranslator: en_IE.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_IL] = &TranslatorWrapper{NewTranslator: en_IL.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_IM] = &TranslatorWrapper{NewTranslator: en_IM.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_IN] = &TranslatorWrapper{NewTranslator: en_IN.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_IO] = &TranslatorWrapper{NewTranslator: en_IO.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_JE] = &TranslatorWrapper{NewTranslator: en_JE.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_JM] = &TranslatorWrapper{NewTranslator: en_JM.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_KE] = &TranslatorWrapper{NewTranslator: en_KE.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_KI] = &TranslatorWrapper{NewTranslator: en_KI.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_KN] = &TranslatorWrapper{NewTranslator: en_KN.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_KY] = &TranslatorWrapper{NewTranslator: en_KY.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_LC] = &TranslatorWrapper{NewTranslator: en_LC.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_LR] = &TranslatorWrapper{NewTranslator: en_LR.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_LS] = &TranslatorWrapper{NewTranslator: en_LS.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_MG] = &TranslatorWrapper{NewTranslator: en_MG.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_MH] = &TranslatorWrapper{NewTranslator: en_MH.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_MO] = &TranslatorWrapper{NewTranslator: en_MO.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_MP] = &TranslatorWrapper{NewTranslator: en_MP.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_MS] = &TranslatorWrapper{NewTranslator: en_MS.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_MT] = &TranslatorWrapper{NewTranslator: en_MT.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_MU] = &TranslatorWrapper{NewTranslator: en_MU.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_MW] = &TranslatorWrapper{NewTranslator: en_MW.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_MY] = &TranslatorWrapper{NewTranslator: en_MY.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_NA] = &TranslatorWrapper{NewTranslator: en_NA.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_NF] = &TranslatorWrapper{NewTranslator: en_NF.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_NG] = &TranslatorWrapper{NewTranslator: en_NG.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_NL] = &TranslatorWrapper{NewTranslator: en_NL.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_NR] = &TranslatorWrapper{NewTranslator: en_NR.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_NU] = &TranslatorWrapper{NewTranslator: en_NU.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_NZ] = &TranslatorWrapper{NewTranslator: en_NZ.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_PG] = &TranslatorWrapper{NewTranslator: en_PG.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_PH] = &TranslatorWrapper{NewTranslator: en_PH.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_PK] = &TranslatorWrapper{NewTranslator: en_PK.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_PN] = &TranslatorWrapper{NewTranslator: en_PN.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_PR] = &TranslatorWrapper{NewTranslator: en_PR.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_PW] = &TranslatorWrapper{NewTranslator: en_PW.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_RW] = &TranslatorWrapper{NewTranslator: en_RW.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_SB] = &TranslatorWrapper{NewTranslator: en_SB.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_SC] = &TranslatorWrapper{NewTranslator: en_SC.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_SD] = &TranslatorWrapper{NewTranslator: en_SD.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_SE] = &TranslatorWrapper{NewTranslator: en_SE.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_SG] = &TranslatorWrapper{NewTranslator: en_SG.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_SH] = &TranslatorWrapper{NewTranslator: en_SH.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_SI] = &TranslatorWrapper{NewTranslator: en_SI.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_SL] = &TranslatorWrapper{NewTranslator: en_SL.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_SS] = &TranslatorWrapper{NewTranslator: en_SS.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_SX] = &TranslatorWrapper{NewTranslator: en_SX.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_SZ] = &TranslatorWrapper{NewTranslator: en_SZ.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_TC] = &TranslatorWrapper{NewTranslator: en_TC.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_TK] = &TranslatorWrapper{NewTranslator: en_TK.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_TO] = &TranslatorWrapper{NewTranslator: en_TO.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_TT] = &TranslatorWrapper{NewTranslator: en_TT.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_TV] = &TranslatorWrapper{NewTranslator: en_TV.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_TZ] = &TranslatorWrapper{NewTranslator: en_TZ.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_UG] = &TranslatorWrapper{NewTranslator: en_UG.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_UM] = &TranslatorWrapper{NewTranslator: en_UM.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_US] = &TranslatorWrapper{NewTranslator: en_US.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_US_POSIX] = &TranslatorWrapper{NewTranslator: en_US_POSIX.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_VC] = &TranslatorWrapper{NewTranslator: en_VC.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_VG] = &TranslatorWrapper{NewTranslator: en_VG.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_VI] = &TranslatorWrapper{NewTranslator: en_VI.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_VU] = &TranslatorWrapper{NewTranslator: en_VU.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_WS] = &TranslatorWrapper{NewTranslator: en_WS.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_ZA] = &TranslatorWrapper{NewTranslator: en_ZA.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_ZM] = &TranslatorWrapper{NewTranslator: en_ZM.New, RegistTranslations: translations_en.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_EN_ZW] = &TranslatorWrapper{NewTranslator: en_ZW.New, RegistTranslations: translations_en.RegisterDefaultTranslations}

	// 法语
	translatorModuleMap[LOCALES_FR] = &TranslatorWrapper{NewTranslator: fr.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_BE] = &TranslatorWrapper{NewTranslator: fr_BE.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_BF] = &TranslatorWrapper{NewTranslator: fr_BF.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_BI] = &TranslatorWrapper{NewTranslator: fr_BI.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_BJ] = &TranslatorWrapper{NewTranslator: fr_BJ.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_BL] = &TranslatorWrapper{NewTranslator: fr_BL.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_CA] = &TranslatorWrapper{NewTranslator: fr_CA.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_CD] = &TranslatorWrapper{NewTranslator: fr_CD.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_CF] = &TranslatorWrapper{NewTranslator: fr_CF.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_CG] = &TranslatorWrapper{NewTranslator: fr_CG.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_CH] = &TranslatorWrapper{NewTranslator: fr_CH.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_CI] = &TranslatorWrapper{NewTranslator: fr_CI.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_CM] = &TranslatorWrapper{NewTranslator: fr_CM.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_DJ] = &TranslatorWrapper{NewTranslator: fr_DJ.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_DZ] = &TranslatorWrapper{NewTranslator: fr_DZ.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_FR] = &TranslatorWrapper{NewTranslator: fr_FR.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_GA] = &TranslatorWrapper{NewTranslator: fr_GA.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_GF] = &TranslatorWrapper{NewTranslator: fr_GF.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_GN] = &TranslatorWrapper{NewTranslator: fr_GN.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_GP] = &TranslatorWrapper{NewTranslator: fr_GP.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_GQ] = &TranslatorWrapper{NewTranslator: fr_GQ.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_HT] = &TranslatorWrapper{NewTranslator: fr_HT.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_KM] = &TranslatorWrapper{NewTranslator: fr_KM.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_LU] = &TranslatorWrapper{NewTranslator: fr_LU.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_MA] = &TranslatorWrapper{NewTranslator: fr_MA.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_MC] = &TranslatorWrapper{NewTranslator: fr_MC.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_MF] = &TranslatorWrapper{NewTranslator: fr_MF.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_MG] = &TranslatorWrapper{NewTranslator: fr_MG.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_ML] = &TranslatorWrapper{NewTranslator: fr_ML.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_MQ] = &TranslatorWrapper{NewTranslator: fr_MQ.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_MR] = &TranslatorWrapper{NewTranslator: fr_MR.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_MU] = &TranslatorWrapper{NewTranslator: fr_MU.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_NC] = &TranslatorWrapper{NewTranslator: fr_NC.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_NE] = &TranslatorWrapper{NewTranslator: fr_NE.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_PF] = &TranslatorWrapper{NewTranslator: fr_PF.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_PM] = &TranslatorWrapper{NewTranslator: fr_PM.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_RE] = &TranslatorWrapper{NewTranslator: fr_RE.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_RW] = &TranslatorWrapper{NewTranslator: fr_RW.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_SC] = &TranslatorWrapper{NewTranslator: fr_SC.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_SN] = &TranslatorWrapper{NewTranslator: fr_SN.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_SY] = &TranslatorWrapper{NewTranslator: fr_SY.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_TD] = &TranslatorWrapper{NewTranslator: fr_TD.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_TG] = &TranslatorWrapper{NewTranslator: fr_TG.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_TN] = &TranslatorWrapper{NewTranslator: fr_TN.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_VU] = &TranslatorWrapper{NewTranslator: fr_VU.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_WF] = &TranslatorWrapper{NewTranslator: fr_WF.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_FR_YT] = &TranslatorWrapper{NewTranslator: fr_YT.New, RegistTranslations: translations_fr.RegisterDefaultTranslations}

	// 印尼语
	translatorModuleMap[LOCALES_ID] = &TranslatorWrapper{NewTranslator: id.New, RegistTranslations: translations_id.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_ID_ID] = &TranslatorWrapper{NewTranslator: id_ID.New, RegistTranslations: translations_id.RegisterDefaultTranslations}

	// 日语
	translatorModuleMap[LOCALES_JA] = &TranslatorWrapper{NewTranslator: ja.New, RegistTranslations: translations_ja.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_JA_JP] = &TranslatorWrapper{NewTranslator: ja_JP.New, RegistTranslations: translations_ja.RegisterDefaultTranslations}

	// 荷兰语
	translatorModuleMap[LOCALES_NL] = &TranslatorWrapper{NewTranslator: nl.New, RegistTranslations: translations_nl.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_NL_AW] = &TranslatorWrapper{NewTranslator: nl_AW.New, RegistTranslations: translations_nl.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_NL_BE] = &TranslatorWrapper{NewTranslator: nl_BE.New, RegistTranslations: translations_nl.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_NL_BQ] = &TranslatorWrapper{NewTranslator: nl_BQ.New, RegistTranslations: translations_nl.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_NL_CW] = &TranslatorWrapper{NewTranslator: nl_CW.New, RegistTranslations: translations_nl.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_NL_NL] = &TranslatorWrapper{NewTranslator: nl_NL.New, RegistTranslations: translations_nl.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_NL_SR] = &TranslatorWrapper{NewTranslator: nl_SR.New, RegistTranslations: translations_nl.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_NL_SX] = &TranslatorWrapper{NewTranslator: nl_SX.New, RegistTranslations: translations_nl.RegisterDefaultTranslations}

	// 葡萄牙语
	translatorModuleMap[LOCALES_PT_BR] = &TranslatorWrapper{NewTranslator: pt_BR.New, RegistTranslations: translations_pt_BR.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_PT_CH] = &TranslatorWrapper{NewTranslator: pt_CH.New, RegistTranslations: translations_pt_BR.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_PT_CV] = &TranslatorWrapper{NewTranslator: pt_CV.New, RegistTranslations: translations_pt_BR.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_PT_GQ] = &TranslatorWrapper{NewTranslator: pt_GQ.New, RegistTranslations: translations_pt_BR.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_PT_GW] = &TranslatorWrapper{NewTranslator: pt_GW.New, RegistTranslations: translations_pt_BR.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_PT_LU] = &TranslatorWrapper{NewTranslator: pt_LU.New, RegistTranslations: translations_pt_BR.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_PT_MO] = &TranslatorWrapper{NewTranslator: pt_MO.New, RegistTranslations: translations_pt_BR.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_PT_MZ] = &TranslatorWrapper{NewTranslator: pt_MZ.New, RegistTranslations: translations_pt_BR.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_PT_PT] = &TranslatorWrapper{NewTranslator: pt_PT.New, RegistTranslations: translations_pt_BR.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_PT_ST] = &TranslatorWrapper{NewTranslator: pt_ST.New, RegistTranslations: translations_pt_BR.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_PT_TL] = &TranslatorWrapper{NewTranslator: pt_TL.New, RegistTranslations: translations_pt_BR.RegisterDefaultTranslations}

	// 简体中文，使用zh
	translatorModuleMap[LOCALES_ZH] = &TranslatorWrapper{NewTranslator: zh.New, RegistTranslations: translations_zh.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_ZH_HANS] = &TranslatorWrapper{NewTranslator: zh_Hans.New, RegistTranslations: translations_zh.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_ZH_HANS_CN] = &TranslatorWrapper{NewTranslator: zh_Hans_CN.New, RegistTranslations: translations_zh.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_ZH_HANS_MO] = &TranslatorWrapper{NewTranslator: zh_Hans_MO.New, RegistTranslations: translations_zh.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_ZH_HANS_HK] = &TranslatorWrapper{NewTranslator: zh_Hans_HK.New, RegistTranslations: translations_zh.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_ZH_HANS_SG] = &TranslatorWrapper{NewTranslator: zh_Hans_SG.New, RegistTranslations: translations_zh.RegisterDefaultTranslations}
	// 繁体中文，使用zh_tw
	translatorModuleMap[LOCALES_ZH_HANT] = &TranslatorWrapper{NewTranslator: zh_Hant.New, RegistTranslations: translations_zh_tw.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_ZH_HANT_HK] = &TranslatorWrapper{NewTranslator: zh_Hant_HK.New, RegistTranslations: translations_zh_tw.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_ZH_HANT_MO] = &TranslatorWrapper{NewTranslator: zh_Hant_MO.New, RegistTranslations: translations_zh_tw.RegisterDefaultTranslations}
	translatorModuleMap[LOCALES_ZH_HANT_TW] = &TranslatorWrapper{NewTranslator: zh_Hant_TW.New, RegistTranslations: translations_zh_tw.RegisterDefaultTranslations}
}

// 如果框架中使用的国际化组件里面的tag无法满足业务需求，则可以自定义校验逻辑，自定义方法如下：
// 实现github.com/go-playground/validator.v9/translations文件中的2个函数：func(ut ut.Translator, fe FieldError) string和func(ut ut.Translator) error
// 然后把这两个函数注册到校验器validate中：validate.RegisterTranslation(tag string, trans ut.Translator, registerFn RegisterTranslationsFunc, translationFn TranslationFunc)

// @Param locales 指定语言
// @Param supportedLocales 指定支持的语言
// @Return validate 校验器
// @Return universalTranslator 国际化语言翻译器
// @Return err 错误，获取所有错误的方式如下：
// if err != nil {
//	//先判断error是否为validator.InvalidValidationError指针，如果不是，则说明validator框架本身出错了
//	if _, ok := err.(*validator.InvalidValidationError); ok {
//		fmt.Println(err)
//		return
//	}
//	//获取所有的错误信息
//	for _, err := range err.(validator.ValidationErrors) {
//		fmt.Println(err.Translate(universalTranslator))
//		//如果需要阻断（每次调用只返回一个错误信息，则此处加break或者return即可）
//	}
// }
func NewValidate(locales string, supportedLocales ...locales.Translator) (validate *validator.Validate, universalTranslator ut.Translator, err error) {
	translatorModule, ok := translatorModuleMap[locales]
	if !ok {
		return nil, nil, errors.New(fmt.Sprintf("there is no translator named '%s'", locales))
	}
	// 获取本地语言
	translator := translatorModule.NewTranslator()
	supportedLocales = append(supportedLocales, translator)
	// 将本地语言注册到国际化组件池中
	universalTranslatorPool := ut.New(translator, supportedLocales...)
	// 从国际化组件吃中获取国际化语言组件
	universalTranslator, exists := universalTranslatorPool.GetTranslator(locales)
	if !exists {
		return nil, nil, errors.New(fmt.Sprintf("there is no universal translator named '%s'", locales))
	}
	// 创建一个校验器
	validate = validator.New()
	// 注册国际化组件defaultTranslator到校验器validate
	translatorModule.RegistTranslations(validate, universalTranslator)
	return validate, universalTranslator, nil
}
