package validate

/*
ISO 639-1
aa	阿法尔语			fr	法语				li	林堡语			se	北萨米语
ab	阿布哈兹语		fy	弗里西亚语		ln	林加拉语			sg	桑戈语
ae	阿维斯陀语		ga	爱尔兰语			lo	老挝语			sh	塞尔维亚-克罗地亚语
af	南非语			gd	苏格兰盖尔语		lt	立陶宛语			si	僧加罗语
ak	阿坎语			gl	加利西亚语		lu	卢巴语			sk	斯洛伐克语
am	阿姆哈拉语		gn	瓜拉尼语			lv	拉脱维亚语		sl	斯洛文尼亚语
an	阿拉贡语			gu	古吉拉特语		mg	马达加斯加语		sm	萨摩亚语
ar	阿拉伯语			gv	马恩岛语			mh	马绍尔语			sn	绍纳语
as	阿萨姆语			ha	豪萨语			mi	毛利语			so	索马里语
av	阿瓦尔语			he	希伯来语			mk	马其顿语			sq	阿尔巴尼亚语
ay	艾马拉语			hi	印地语			ml	马拉亚拉姆语		sr	塞尔维亚语
az	阿塞拜疆语		ho	希里莫图语		mn	蒙古语			ss	斯瓦特语
ba	巴什基尔语		hr	克罗地亚语		mo	摩尔达维亚语		st	南索托语
be	白俄罗斯语		ht	海地克里奥尔语	mr	马拉提语			su	巽他语
bg	保加利亚语		hu	匈牙利语			ms	马来语			sv	瑞典语
bh	比哈尔语			hy	亚美尼亚语		mt	马耳他语			sw	斯瓦希里语
bi	比斯拉马语		hz	赫雷罗语			my	缅甸语			ta	泰米尔语
bm	班巴拉语			ia	因特语			na	瑙鲁语			te	泰卢固语
bn	孟加拉语			id	印尼语			nb	书面挪威语		tg	塔吉克斯坦语
bo	藏语			ie	西方国际语		nd	北恩德贝勒语		th	泰语
br	布列塔尼语		ig	伊博语			ne	尼泊尔语			ti	提格里尼亚语
bs	波斯尼亚语		ii	四川彝语（诺苏语）ng	恩敦加语			tk	土库曼语
ca	加泰隆语			ik	依努庇克语		nl	荷兰语			tl	他加禄语
ce	车臣语			io	伊多语			nn	新挪威语			tn	塞茨瓦纳语
ch	查莫罗语			is	冰岛语			no	挪威语			to	汤加语
co	科西嘉语			it	意大利语			nr	南恩德贝勒语		tr	土耳其语
cr	克里语			iu	因纽特语			nv	纳瓦霍语			ts	宗加语
cs	捷克语			ja	日语			ny	尼扬贾语			tt	塔塔尔语
cu	古教会斯拉夫语	jv	爪哇语			oc	奥克语			tw	特威语
cv	楚瓦什语			ka	格鲁吉亚语		oj	奥吉布瓦语		ty	塔希提语
cy	威尔士语			kg	刚果语			om	奥洛莫语			ug	维吾尔语
da	丹麦语			ki	基库尤语			or	奥利亚语			uk	乌克兰语
de	德语			kj	宽亚玛语			os	奥塞梯语			ur	乌尔都语
dv	迪维希语			kk	哈萨克语			pa	旁遮普语			uz	乌兹别克语
dz	不丹语			kl	格陵兰语			pi	巴利语			ve	文达语
ee	埃维语			km	高棉语			pl	波兰语			vi	越南语
el	现代希腊语		kn	卡纳达语			ps	普什图语			vo	沃拉普克语
en	英语			ko	朝鲜语、韩语		pt	葡萄牙语			wa	沃伦语
eo	世界语			kr	卡努里语			qu	凯楚亚语			wo	沃洛夫语
es	西班牙语			ks	克什米尔语		rm	罗曼什语			xh	科萨语
et	爱沙尼亚语		ku	库尔德语			rn	基隆迪语			yi	依地语
eu	巴斯克语			kv	科米语			ro	罗马尼亚语		yo	约鲁巴语
fa	波斯语			kw	康沃尔语			ru	俄语			za	壮语
ff	富拉语			ky	吉尔吉斯语		rw	卢旺达语			zh	中文、汉语
fi	芬兰语			la	拉丁语			sa	梵语			zu	祖鲁语
fj	斐济语			lb	卢森堡语			sc	撒丁语
fo	法罗语			lg	卢干达语			sd	信德语
*/

const (
	//LOCALES_AF             = "af"
	//LOCALES_AF_NA          = "af_NA"
	//LOCALES_AF_ZA          = "af_ZA"
	//LOCALES_AGQ            = "agq"
	//LOCALES_AGQ_CM         = "agq_CM"
	//LOCALES_AK             = "ak"
	//LOCALES_AK_GH          = "ak_GH"
	//LOCALES_AM             = "am"
	//LOCALES_AM_ET          = "am_ET"
	//LOCALES_AR             = "ar"
	//LOCALES_AR_001         = "ar_001"
	//LOCALES_AR_AE          = "ar_AE"
	//LOCALES_AR_BH          = "ar_BH"
	//LOCALES_AR_DJ          = "ar_DJ"
	//LOCALES_AR_DZ          = "ar_DZ"
	//LOCALES_AR_EG          = "ar_EG"
	//LOCALES_AR_EH          = "ar_EH"
	//LOCALES_AR_ER          = "ar_ER"
	//LOCALES_AR_IL          = "ar_IL"
	//LOCALES_AR_IQ          = "ar_IQ"
	//LOCALES_AR_JO          = "ar_JO"
	//LOCALES_AR_KM          = "ar_KM"
	//LOCALES_AR_KW          = "ar_KW"
	//LOCALES_AR_LB          = "ar_LB"
	//LOCALES_AR_LY          = "ar_LY"
	//LOCALES_AR_MA          = "ar_MA"
	//LOCALES_AR_MR          = "ar_MR"
	//LOCALES_AR_OM          = "ar_OM"
	//LOCALES_AR_PS          = "ar_PS"
	//LOCALES_AR_QA          = "ar_QA"
	//LOCALES_AR_SA          = "ar_SA"
	//LOCALES_AR_SD          = "ar_SD"
	//LOCALES_AR_SO          = "ar_SO"
	//LOCALES_AR_SS          = "ar_SS"
	//LOCALES_AR_SY          = "ar_SY"
	//LOCALES_AR_TD          = "ar_TD"
	//LOCALES_AR_TN          = "ar_TN"
	//LOCALES_AR_YE          = "ar_YE"
	//LOCALES_AS             = "as"
	//LOCALES_AS_IN          = "as_IN"
	//LOCALES_ASA            = "asa"
	//LOCALES_ASA_TZ         = "asa_TZ"
	//LOCALES_AST            = "ast"
	//LOCALES_AST_ES         = "ast_ES"
	//LOCALES_AZ             = "az"
	//LOCALES_AZ_CYRL        = "az_Cyrl"
	//LOCALES_AZ_CYRL_AZ     = "az_Cyrl_AZ"
	//LOCALES_AZ_LATN        = "az_Latn"
	//LOCALES_AZ_LATN_AZ     = "az_Latn_AZ"
	//LOCALES_BAS            = "bas"
	//LOCALES_BAS_CM         = "bas_CM"
	//LOCALES_BE             = "be"
	//LOCALES_BE_BY          = "be_BY"
	//LOCALES_BEM            = "bem"
	//LOCALES_BEM_ZM         = "bem_ZM"
	//LOCALES_BEZ            = "bez"
	//LOCALES_BEZ_TZ         = "bez_TZ"
	//LOCALES_BG             = "bg"
	//LOCALES_BG_BG          = "bg_BG"
	//LOCALES_BM             = "bm"
	//LOCALES_BM_ML          = "bm_ML"
	//LOCALES_BN             = "bn"
	//LOCALES_BN_BD          = "bn_BD"
	//LOCALES_BN_IN          = "bn_IN"
	//LOCALES_BO             = "bo"
	//LOCALES_BO_CN          = "bo_CN"
	//LOCALES_BO_IN          = "bo_IN"
	//LOCALES_BR             = "br"
	//LOCALES_BR_FR          = "br_FR"
	//LOCALES_BRX            = "brx"
	//LOCALES_BRX_IN         = "brx_IN"
	//LOCALES_BS             = "bs"
	//LOCALES_BS_CYRL        = "bs_Cyrl"
	//LOCALES_BS_CYRL_BA     = "bs_Cyrl_BA"
	//LOCALES_BS_LATN        = "bs_Latn"
	//LOCALES_BS_LATN_BA     = "bs_Latn_BA"
	//LOCALES_CA             = "ca"
	//LOCALES_CA_AD          = "ca_AD"
	//LOCALES_CA_ES          = "ca_ES"
	//LOCALES_CA_ES_VALENCIA = "ca_ES_VALENCIA"
	//LOCALES_CA_FR          = "ca_FR"
	//LOCALES_CA_IT          = "ca_IT"
	//LOCALES_CCP            = "ccp"
	//LOCALES_CCP_BD         = "ccp_BD"
	//LOCALES_CCP_IN         = "ccp_IN"
	//LOCALES_CE             = "ce"
	//LOCALES_CE_RU          = "ce_RU"
	//LOCALES_CGG            = "cgg"
	//LOCALES_CGG_UG         = "cgg_UG"
	//LOCALES_CHR            = "chr"
	//LOCALES_CHR_US         = "chr_US"
	//LOCALES_CKB            = "ckb"
	//LOCALES_CKB_IQ         = "ckb_IQ"
	//LOCALES_CKB_IR         = "ckb_IR"
	//LOCALES_CMD            = "cmd"
	//LOCALES_CS             = "cs"
	//LOCALES_CS_CZ          = "cs_CZ"
	//LOCALES_CU             = "cu"
	//LOCALES_CU_RU          = "cu_RU"
	//LOCALES_CURRENCY       = "currency"
	//LOCALES_CY             = "cy"
	//LOCALES_CY_GB          = "cy_GB"
	//LOCALES_DA             = "da"
	//LOCALES_DA_DK          = "da_DK"
	//LOCALES_DA_GL          = "da_GL"
	//LOCALES_DAV            = "dav"
	//LOCALES_DAV_KE         = "dav_KE"
	//LOCALES_DE             = "de"
	//LOCALES_DE_AT          = "de_AT"
	//LOCALES_DE_BE          = "de_BE"
	//LOCALES_DE_CH          = "de_CH"
	//LOCALES_DE_DE          = "de_DE"
	//LOCALES_DE_IT          = "de_IT"
	//LOCALES_DE_LI          = "de_LI"
	//LOCALES_DE_LU          = "de_LU"
	//LOCALES_DJE            = "dje"
	//LOCALES_DJE_NE         = "dje_NE"
	//LOCALES_DSB            = "dsb"
	//LOCALES_DSB_DE         = "dsb_DE"
	//LOCALES_DUA            = "dua"
	//LOCALES_DUA_CM         = "dua_CM"
	//LOCALES_DYO            = "dyo"
	//LOCALES_DYO_SN         = "dyo_SN"
	//LOCALES_DZ             = "dz"
	//LOCALES_DZ_BT          = "dz_BT"
	//LOCALES_EBU            = "ebu"
	//LOCALES_EBU_KE         = "ebu_KE"
	//LOCALES_EE             = "ee"
	//LOCALES_EE_GH          = "ee_GH"
	//LOCALES_EE_TG          = "ee_TG"
	//LOCALES_EL             = "el"
	//LOCALES_EL_CY          = "el_CY"
	//LOCALES_EL_GR          = "el_GR"
	LOCALES_EN          = "en"
	LOCALES_EN_001      = "en_001"
	LOCALES_EN_150      = "en_150"
	LOCALES_EN_AG       = "en_AG"
	LOCALES_EN_AI       = "en_AI"
	LOCALES_EN_AS       = "en_AS"
	LOCALES_EN_AT       = "en_AT"
	LOCALES_EN_AU       = "en_AU"
	LOCALES_EN_BB       = "en_BB"
	LOCALES_EN_BE       = "en_BE"
	LOCALES_EN_BI       = "en_BI"
	LOCALES_EN_BM       = "en_BM"
	LOCALES_EN_BS       = "en_BS"
	LOCALES_EN_BW       = "en_BW"
	LOCALES_EN_BZ       = "en_BZ"
	LOCALES_EN_CA       = "en_CA"
	LOCALES_EN_CC       = "en_CC"
	LOCALES_EN_CH       = "en_CH"
	LOCALES_EN_CK       = "en_CK"
	LOCALES_EN_CM       = "en_CM"
	LOCALES_EN_CX       = "en_CX"
	LOCALES_EN_CY       = "en_CY"
	LOCALES_EN_DE       = "en_DE"
	LOCALES_EN_DG       = "en_DG"
	LOCALES_EN_DK       = "en_DK"
	LOCALES_EN_DM       = "en_DM"
	LOCALES_EN_ER       = "en_ER"
	LOCALES_EN_FI       = "en_FI"
	LOCALES_EN_FJ       = "en_FJ"
	LOCALES_EN_FK       = "en_FK"
	LOCALES_EN_FM       = "en_FM"
	LOCALES_EN_GB       = "en_GB"
	LOCALES_EN_GD       = "en_GD"
	LOCALES_EN_GG       = "en_GG"
	LOCALES_EN_GH       = "en_GH"
	LOCALES_EN_GI       = "en_GI"
	LOCALES_EN_GM       = "en_GM"
	LOCALES_EN_GU       = "en_GU"
	LOCALES_EN_GY       = "en_GY"
	LOCALES_EN_HK       = "en_HK"
	LOCALES_EN_IE       = "en_IE"
	LOCALES_EN_IL       = "en_IL"
	LOCALES_EN_IM       = "en_IM"
	LOCALES_EN_IN       = "en_IN"
	LOCALES_EN_IO       = "en_IO"
	LOCALES_EN_JE       = "en_JE"
	LOCALES_EN_JM       = "en_JM"
	LOCALES_EN_KE       = "en_KE"
	LOCALES_EN_KI       = "en_KI"
	LOCALES_EN_KN       = "en_KN"
	LOCALES_EN_KY       = "en_KY"
	LOCALES_EN_LC       = "en_LC"
	LOCALES_EN_LR       = "en_LR"
	LOCALES_EN_LS       = "en_LS"
	LOCALES_EN_MG       = "en_MG"
	LOCALES_EN_MH       = "en_MH"
	LOCALES_EN_MO       = "en_MO"
	LOCALES_EN_MP       = "en_MP"
	LOCALES_EN_MS       = "en_MS"
	LOCALES_EN_MT       = "en_MT"
	LOCALES_EN_MU       = "en_MU"
	LOCALES_EN_MW       = "en_MW"
	LOCALES_EN_MY       = "en_MY"
	LOCALES_EN_NA       = "en_NA"
	LOCALES_EN_NF       = "en_NF"
	LOCALES_EN_NG       = "en_NG"
	LOCALES_EN_NL       = "en_NL"
	LOCALES_EN_NR       = "en_NR"
	LOCALES_EN_NU       = "en_NU"
	LOCALES_EN_NZ       = "en_NZ"
	LOCALES_EN_PG       = "en_PG"
	LOCALES_EN_PH       = "en_PH"
	LOCALES_EN_PK       = "en_PK"
	LOCALES_EN_PN       = "en_PN"
	LOCALES_EN_PR       = "en_PR"
	LOCALES_EN_PW       = "en_PW"
	LOCALES_EN_RW       = "en_RW"
	LOCALES_EN_SB       = "en_SB"
	LOCALES_EN_SC       = "en_SC"
	LOCALES_EN_SD       = "en_SD"
	LOCALES_EN_SE       = "en_SE"
	LOCALES_EN_SG       = "en_SG"
	LOCALES_EN_SH       = "en_SH"
	LOCALES_EN_SI       = "en_SI"
	LOCALES_EN_SL       = "en_SL"
	LOCALES_EN_SS       = "en_SS"
	LOCALES_EN_SX       = "en_SX"
	LOCALES_EN_SZ       = "en_SZ"
	LOCALES_EN_TC       = "en_TC"
	LOCALES_EN_TK       = "en_TK"
	LOCALES_EN_TO       = "en_TO"
	LOCALES_EN_TT       = "en_TT"
	LOCALES_EN_TV       = "en_TV"
	LOCALES_EN_TZ       = "en_TZ"
	LOCALES_EN_UG       = "en_UG"
	LOCALES_EN_UM       = "en_UM"
	LOCALES_EN_US       = "en_US"
	LOCALES_EN_US_POSIX = "en_US_POSIX"
	LOCALES_EN_VC       = "en_VC"
	LOCALES_EN_VG       = "en_VG"
	LOCALES_EN_VI       = "en_VI"
	LOCALES_EN_VU       = "en_VU"
	LOCALES_EN_WS       = "en_WS"
	LOCALES_EN_ZA       = "en_ZA"
	LOCALES_EN_ZM       = "en_ZM"
	LOCALES_EN_ZW       = "en_ZW"
	//EO 世界语（暂时不支持）
	//LOCALES_EO             = "eo"
	//LOCALES_EO_001         = "eo_001"
	//ES 西班牙语（暂时不支持）
	//LOCALES_ES          = "es"
	//LOCALES_ES_419      = "es_419"
	//LOCALES_ES_AR       = "es_AR"
	//LOCALES_ES_BO       = "es_BO"
	//LOCALES_ES_BR       = "es_BR"
	//LOCALES_ES_BZ       = "es_BZ"
	//LOCALES_ES_CL       = "es_CL"
	//LOCALES_ES_CO       = "es_CO"
	//LOCALES_ES_CR       = "es_CR"
	//LOCALES_ES_CU       = "es_CU"
	//LOCALES_ES_DO       = "es_DO"
	//LOCALES_ES_EA       = "es_EA"
	//LOCALES_ES_EC       = "es_EC"
	//LOCALES_ES_ES       = "es_ES"
	//LOCALES_ES_GQ       = "es_GQ"
	//LOCALES_ES_GT       = "es_GT"
	//LOCALES_ES_HN       = "es_HN"
	//LOCALES_ES_IC       = "es_IC"
	//LOCALES_ES_MX       = "es_MX"
	//LOCALES_ES_NI       = "es_NI"
	//LOCALES_ES_PA       = "es_PA"
	//LOCALES_ES_PE       = "es_PE"
	//LOCALES_ES_PH       = "es_PH"
	//LOCALES_ES_PR       = "es_PR"
	//LOCALES_ES_PY       = "es_PY"
	//LOCALES_ES_SV       = "es_SV"
	//LOCALES_ES_US       = "es_US"
	//LOCALES_ES_UY       = "es_UY"
	//LOCALES_ES_VE       = "es_VE"
	//ET 爱沙尼亚语（暂时不支持）
	//LOCALES_ET          = "et"
	//LOCALES_ET_EE       = "et_EE"
	//EU 巴斯克语（暂时不支持）
	//LOCALES_EU    = "eu"
	//LOCALES_EU_ES = "eu_ES"
	//EWO 埃翁多语（暂时不支持）
	//LOCALES_EWO    = "ewo"
	//LOCALES_EWO_CM = "ewo_CM"
	//FA 波斯语（暂时不支持）
	//LOCALES_FA    = "fa"
	//LOCALES_FA_AF = "fa_AF"
	//LOCALES_FA_IR = "fa_IR"
	//FF 富拉赫语（暂时不支持）
	//LOCALES_FF          = "ff"
	//LOCALES_FF_CM       = "ff_CM"
	//LOCALES_FF_GN       = "ff_GN"
	//LOCALES_FF_MR       = "ff_MR"
	//LOCALES_FF_SN       = "ff_SN"
	//FI 芬兰语（暂时不支持）
	//LOCALES_FI     = "fi"
	//LOCALES_FI_FI  = "fi_FI"
	//LOCALES_FIL    = "fil"
	//LOCALES_FIL_PH = "fil_PH"
	//FO 法罗语（暂时不支持）
	//LOCALES_FO          = "fo"
	//LOCALES_FO_DK       = "fo_DK"
	//LOCALES_FO_FO       = "fo_FO"
	//FR 法语
	LOCALES_FR    = "fr"
	LOCALES_FR_BE = "fr_BE"
	LOCALES_FR_BF = "fr_BF"
	LOCALES_FR_BI = "fr_BI"
	LOCALES_FR_BJ = "fr_BJ"
	LOCALES_FR_BL = "fr_BL"
	LOCALES_FR_CA = "fr_CA"
	LOCALES_FR_CD = "fr_CD"
	LOCALES_FR_CF = "fr_CF"
	LOCALES_FR_CG = "fr_CG"
	LOCALES_FR_CH = "fr_CH"
	LOCALES_FR_CI = "fr_CI"
	LOCALES_FR_CM = "fr_CM"
	LOCALES_FR_DJ = "fr_DJ"
	LOCALES_FR_DZ = "fr_DZ"
	LOCALES_FR_FR = "fr_FR"
	LOCALES_FR_GA = "fr_GA"
	LOCALES_FR_GF = "fr_GF"
	LOCALES_FR_GN = "fr_GN"
	LOCALES_FR_GP = "fr_GP"
	LOCALES_FR_GQ = "fr_GQ"
	LOCALES_FR_HT = "fr_HT"
	LOCALES_FR_KM = "fr_KM"
	LOCALES_FR_LU = "fr_LU"
	LOCALES_FR_MA = "fr_MA"
	LOCALES_FR_MC = "fr_MC"
	LOCALES_FR_MF = "fr_MF"
	LOCALES_FR_MG = "fr_MG"
	LOCALES_FR_ML = "fr_ML"
	LOCALES_FR_MQ = "fr_MQ"
	LOCALES_FR_MR = "fr_MR"
	LOCALES_FR_MU = "fr_MU"
	LOCALES_FR_NC = "fr_NC"
	LOCALES_FR_NE = "fr_NE"
	LOCALES_FR_PF = "fr_PF"
	LOCALES_FR_PM = "fr_PM"
	LOCALES_FR_RE = "fr_RE"
	LOCALES_FR_RW = "fr_RW"
	LOCALES_FR_SC = "fr_SC"
	LOCALES_FR_SN = "fr_SN"
	LOCALES_FR_SY = "fr_SY"
	LOCALES_FR_TD = "fr_TD"
	LOCALES_FR_TG = "fr_TG"
	LOCALES_FR_TN = "fr_TN"
	LOCALES_FR_VU = "fr_VU"
	LOCALES_FR_WF = "fr_WF"
	LOCALES_FR_YT = "fr_YT"
	//FUR 未知语言（暂时不支持）
	//LOCALES_FUR         = "fur"
	//LOCALES_FUR_IT      = "fur_IT"
	//FY 未知语言（暂时不支持）
	//LOCALES_FY          = "fy"
	//LOCALES_FY_NL       = "fy_NL"
	//GA 爱尔兰语（暂时不支持）
	//LOCALES_GA    = "ga"
	//LOCALES_GA_IE = "ga_IE"
	//GD 未知语言（暂时不支持）
	//LOCALES_GD    = "gd"
	//LOCALES_GD_GB = "gd_GB"
	//GL 加利西亚语（暂时不支持）
	//LOCALES_GL    = "gl"
	//LOCALES_GL_ES = "gl_ES"
	//GSW 瑞士德语（暂时不支持）
	//LOCALES_GSW    = "gsw"
	//LOCALES_GSW_CH = "gsw_CH"
	//LOCALES_GSW_FR = "gsw_FR"
	//LOCALES_GSW_LI = "gsw_LI"
	//GU 古吉拉特语（暂时不支持）
	//LOCALES_GU     = "gu"
	//LOCALES_GU_IN  = "gu_IN"
	//LOCALES_GUZ    = "guz"
	//LOCALES_GUZ_KE = "guz_KE"
	//GV 曼岛语（暂时不支持）
	//LOCALES_GV    = "gv"
	//LOCALES_GV_IM = "gv_IM"
	//
	//LOCALES_HA          = "ha"
	//LOCALES_HA_GH       = "ha_GH"
	//LOCALES_HA_NE       = "ha_NE"
	//LOCALES_HA_NG       = "ha_NG"
	//LOCALES_HAW         = "haw"
	//LOCALES_HAW_US      = "haw_US"
	//LOCALES_HE          = "he"
	//LOCALES_HE_IL       = "he_IL"
	//LOCALES_HI          = "hi"
	//LOCALES_HI_IN       = "hi_IN"
	//LOCALES_HR          = "hr"
	//LOCALES_HR_BA       = "hr_BA"
	//LOCALES_HR_HR       = "hr_HR"
	//LOCALES_HSB         = "hsb"
	//LOCALES_HSB_DE      = "hsb_DE"
	//LOCALES_HU          = "hu"
	//LOCALES_HU_HU       = "hu_HU"
	//LOCALES_HY          = "hy"
	//LOCALES_HY_AM       = "hy_AM"
	//
	LOCALES_ID    = "id"
	LOCALES_ID_ID = "id_ID"
	//LOCALES_IG          = "ig"
	//LOCALES_IG_NG       = "ig_NG"
	//LOCALES_II          = "ii"
	//LOCALES_II_CN       = "ii_CN"
	//LOCALES_IS          = "is"
	//LOCALES_IS_IS       = "is_IS"
	//LOCALES_IT          = "it"
	//LOCALES_IT_CH       = "it_CH"
	//LOCALES_IT_IT       = "it_IT"
	//LOCALES_IT_SM       = "it_SM"
	//LOCALES_IT_VA       = "it_VA"
	LOCALES_JA    = "ja"
	LOCALES_JA_JP = "ja_JP"
	//LOCALES_JGO         = "jgo"
	//LOCALES_JGO_CM      = "jgo_CM"
	//LOCALES_JMC         = "jmc"
	//LOCALES_JMC_TZ      = "jmc_TZ"
	//LOCALES_KA          = "ka"
	//LOCALES_KA_GE       = "ka_GE"
	//LOCALES_KAB         = "kab"
	//LOCALES_KAB_DZ      = "kab_DZ"
	//LOCALES_KAM         = "kam"
	//LOCALES_KAM_KE      = "kam_KE"
	//LOCALES_KDE         = "kde"
	//LOCALES_KDE_TZ      = "kde_TZ"
	//LOCALES_KEA         = "kea"
	//LOCALES_KEA_CV      = "kea_CV"
	//LOCALES_KHQ         = "khq"
	//LOCALES_KHQ_ML      = "khq_ML"
	//LOCALES_KI          = "ki"
	//LOCALES_KI_KE       = "ki_KE"
	//LOCALES_KK          = "kk"
	//LOCALES_KK_KZ       = "kk_KZ"
	//LOCALES_KKJ         = "kkj"
	//LOCALES_KKJ_CM      = "kkj_CM"
	//LOCALES_KL          = "kl"
	//LOCALES_KL_GL       = "kl_GL"
	//LOCALES_KLN         = "kln"
	//LOCALES_KLN_KE      = "kln_KE"
	//LOCALES_KM          = "km"
	//LOCALES_KM_KH       = "km_KH"
	//LOCALES_KN          = "kn"
	//LOCALES_KN_IN       = "kn_IN"
	//LOCALES_KO          = "ko"
	//LOCALES_KO_KP       = "ko_KP"
	//LOCALES_KO_KR       = "ko_KR"
	//LOCALES_KOK         = "kok"
	//LOCALES_KOK_IN      = "kok_IN"
	//LOCALES_KS          = "ks"
	//LOCALES_KS_IN       = "ks_IN"
	//LOCALES_KSB         = "ksb"
	//LOCALES_KSB_TZ      = "ksb_TZ"
	//LOCALES_KSF         = "ksf"
	//LOCALES_KSF_CM      = "ksf_CM"
	//LOCALES_KSH         = "ksh"
	//LOCALES_KSH_DE      = "ksh_DE"
	//LOCALES_KW          = "kw"
	//LOCALES_KW_GB       = "kw_GB"
	//LOCALES_KY          = "ky"
	//LOCALES_KY_KG       = "ky_KG"
	//LOCALES_LAG         = "lag"
	//LOCALES_LAG_TZ      = "lag_TZ"
	//LOCALES_LB          = "lb"
	//LOCALES_LB_LU       = "lb_LU"
	//LOCALES_LG          = "lg"
	//LOCALES_LG_UG       = "lg_UG"
	//LOCALES_LKT         = "lkt"
	//LOCALES_LKT_US      = "lkt_US"
	//LOCALES_LN          = "ln"
	//LOCALES_LN_AO       = "ln_AO"
	//LOCALES_LN_CD       = "ln_CD"
	//LOCALES_LN_CF       = "ln_CF"
	//LOCALES_LN_CG       = "ln_CG"
	//LOCALES_LO          = "lo"
	//LOCALES_LO_LA       = "lo_LA"
	//LOCALES_LRC         = "lrc"
	//LOCALES_LRC_IQ      = "lrc_IQ"
	//LOCALES_LRC_IR      = "lrc_IR"
	//LOCALES_LT          = "lt"
	//LOCALES_LT_LT       = "lt_LT"
	//LOCALES_LU          = "lu"
	//LOCALES_LU_CD       = "lu_CD"
	//LOCALES_LUO         = "luo"
	//LOCALES_LUO_KE      = "luo_KE"
	//LOCALES_LUY         = "luy"
	//LOCALES_LUY_KE      = "luy_KE"
	//LOCALES_LV          = "lv"
	//LOCALES_LV_LV       = "lv_LV"
	//LOCALES_MAS         = "mas"
	//LOCALES_MAS_KE      = "mas_KE"
	//LOCALES_MAS_TZ      = "mas_TZ"
	//LOCALES_MER         = "mer"
	//LOCALES_MER_KE      = "mer_KE"
	//LOCALES_MFE         = "mfe"
	//LOCALES_MFE_MU      = "mfe_MU"
	//LOCALES_MG          = "mg"
	//LOCALES_MG_MG       = "mg_MG"
	//LOCALES_MGH         = "mgh"
	//LOCALES_MGH_MZ      = "mgh_MZ"
	//LOCALES_MGO         = "mgo"
	//LOCALES_MGO_CM      = "mgo_CM"
	//LOCALES_MK          = "mk"
	//LOCALES_MK_MK       = "mk_MK"
	//LOCALES_ML          = "ml"
	//LOCALES_ML_IN       = "ml_IN"
	//LOCALES_MN          = "mn"
	//LOCALES_MN_MN       = "mn_MN"
	//LOCALES_MR          = "mr"
	//LOCALES_MR_IN       = "mr_IN"
	//LOCALES_MS          = "ms"
	//LOCALES_MS_BN       = "ms_BN"
	//LOCALES_MS_MY       = "ms_MY"
	//LOCALES_MS_SG       = "ms_SG"
	//LOCALES_MT          = "mt"
	//LOCALES_MT_MT       = "mt_MT"
	//LOCALES_MUA         = "mua"
	//LOCALES_MUA_CM      = "mua_CM"
	//LOCALES_MY          = "my"
	//LOCALES_MY_MM       = "my_MM"
	//LOCALES_MZN         = "mzn"
	//LOCALES_MZN_IR      = "mzn_IR"
	//LOCALES_NAQ         = "naq"
	//LOCALES_NAQ_NA      = "naq_NA"
	//LOCALES_NB          = "nb"
	//LOCALES_NB_NO       = "nb_NO"
	//LOCALES_NB_SJ       = "nb_SJ"
	//LOCALES_ND          = "nd"
	//LOCALES_ND_ZW       = "nd_ZW"
	//LOCALES_NDS         = "nds"
	//LOCALES_NDS_DE      = "nds_DE"
	//LOCALES_NDS_NL      = "nds_NL"
	//LOCALES_NE          = "ne"
	//LOCALES_NE_IN       = "ne_IN"
	//LOCALES_NE_NP       = "ne_NP"
	LOCALES_NL    = "nl"
	LOCALES_NL_AW = "nl_AW"
	LOCALES_NL_BE = "nl_BE"
	LOCALES_NL_BQ = "nl_BQ"
	LOCALES_NL_CW = "nl_CW"
	LOCALES_NL_NL = "nl_NL"
	LOCALES_NL_SR = "nl_SR"
	LOCALES_NL_SX = "nl_SX"
	//LOCALES_NMG         = "nmg"
	//LOCALES_NMG_CM      = "nmg_CM"
	//LOCALES_NN          = "nn"
	//LOCALES_NN_NO       = "nn_NO"
	//LOCALES_NNH         = "nnh"
	//LOCALES_NNH_CM      = "nnh_CM"
	//LOCALES_NUS         = "nus"
	//LOCALES_NUS_SS      = "nus_SS"
	//LOCALES_NYN         = "nyn"
	//LOCALES_NYN_UG      = "nyn_UG"
	//LOCALES_OM          = "om"
	//LOCALES_OM_ET       = "om_ET"
	//LOCALES_OM_KE       = "om_KE"
	//LOCALES_OR          = "or"
	//LOCALES_OR_IN       = "or_IN"
	//LOCALES_OS          = "os"
	//LOCALES_OS_GE       = "os_GE"
	//LOCALES_OS_RU       = "os_RU"
	//LOCALES_PA          = "pa"
	//LOCALES_PA_ARAB     = "pa_Arab"
	//LOCALES_PA_ARAB_PK  = "pa_Arab_PK"
	//LOCALES_PA_GURU     = "pa_Guru"
	//LOCALES_PA_GURU_IN  = "pa_Guru_IN"
	//LOCALES_PL          = "pl"
	//LOCALES_PL_PL       = "pl_PL"
	//LOCALES_PRG         = "prg"
	//LOCALES_PRG_001     = "prg_001"
	//LOCALES_PS          = "ps"
	//LOCALES_PS_AF       = "ps_AF"
	//LOCALES_PT          = "pt"
	//LOCALES_PT_AO       = "pt_AO"
	LOCALES_PT_BR = "pt_BR"
	LOCALES_PT_CH = "pt_CH"
	LOCALES_PT_CV = "pt_CV"
	LOCALES_PT_GQ = "pt_GQ"
	LOCALES_PT_GW = "pt_GW"
	LOCALES_PT_LU = "pt_LU"
	LOCALES_PT_MO = "pt_MO"
	LOCALES_PT_MZ = "pt_MZ"
	LOCALES_PT_PT = "pt_PT"
	LOCALES_PT_ST = "pt_ST"
	LOCALES_PT_TL = "pt_TL"
	//LOCALES_QU          = "qu"
	//LOCALES_QU_BO       = "qu_BO"
	//LOCALES_QU_EC       = "qu_EC"
	//LOCALES_QU_PE       = "qu_PE"
	//LOCALES_RM          = "rm"
	//LOCALES_RM_CH       = "rm_CH"
	//LOCALES_RN          = "rn"
	//LOCALES_RN_BI       = "rn_BI"
	//LOCALES_RO          = "ro"
	//LOCALES_RO_MD       = "ro_MD"
	//LOCALES_RO_RO       = "ro_RO"
	//LOCALES_ROF         = "rof"
	//LOCALES_ROF_TZ      = "rof_TZ"
	//LOCALES_ROOT        = "root"
	//LOCALES_RU          = "ru"
	//LOCALES_RU_BY       = "ru_BY"
	//LOCALES_RU_KG       = "ru_KG"
	//LOCALES_RU_KZ       = "ru_KZ"
	//LOCALES_RU_MD       = "ru_MD"
	//LOCALES_RU_RU       = "ru_RU"
	//LOCALES_RU_UA       = "ru_UA"
	//LOCALES_RW          = "rw"
	//LOCALES_RW_RW       = "rw_RW"
	//LOCALES_RWK         = "rwk"
	//LOCALES_RWK_TZ      = "rwk_TZ"
	//LOCALES_SAH         = "sah"
	//LOCALES_SAH_RU      = "sah_RU"
	//LOCALES_SAQ         = "saq"
	//LOCALES_SAQ_KE      = "saq_KE"
	//LOCALES_SBP         = "sbp"
	//LOCALES_SBP_TZ      = "sbp_TZ"
	//LOCALES_SD          = "sd"
	//LOCALES_SD_PK       = "sd_PK"
	//LOCALES_SE          = "se"
	//LOCALES_SE_FI       = "se_FI"
	//LOCALES_SE_NO       = "se_NO"
	//LOCALES_SE_SE       = "se_SE"
	//LOCALES_SEH         = "seh"
	//LOCALES_SEH_MZ      = "seh_MZ"
	//LOCALES_SES         = "ses"
	//LOCALES_SES_ML      = "ses_ML"
	//LOCALES_SG          = "sg"
	//LOCALES_SG_CF       = "sg_CF"
	//LOCALES_SHI         = "shi"
	//LOCALES_SHI_LATN    = "shi_Latn"
	//LOCALES_SHI_LATN_MA = "shi_Latn_MA"
	//LOCALES_SHI_TFNG    = "shi_Tfng"
	//LOCALES_SHI_TFNG_MA = "shi_Tfng_MA"
	//LOCALES_SI          = "si"
	//LOCALES_SI_LK       = "si_LK"
	//LOCALES_SK          = "sk"
	//LOCALES_SK_SK       = "sk_SK"
	//LOCALES_SL          = "sl"
	//LOCALES_SL_SI       = "sl_SI"
	//LOCALES_SMN         = "smn"
	//LOCALES_SMN_FI      = "smn_FI"
	//LOCALES_SN          = "sn"
	//LOCALES_SN_ZW       = "sn_ZW"
	//LOCALES_SO          = "so"
	//LOCALES_SO_DJ       = "so_DJ"
	//LOCALES_SO_ET       = "so_ET"
	//LOCALES_SO_KE       = "so_KE"
	//LOCALES_SO_SO       = "so_SO"
	//LOCALES_SQ          = "sq"
	//LOCALES_SQ_AL       = "sq_AL"
	//LOCALES_SQ_MK       = "sq_MK"
	//LOCALES_SQ_XK       = "sq_XK"
	//LOCALES_SR          = "sr"
	//LOCALES_SR_CYRL     = "sr_Cyrl"
	//LOCALES_SR_CYRL_BA  = "sr_Cyrl_BA"
	//LOCALES_SR_CYRL_ME  = "sr_Cyrl_ME"
	//LOCALES_SR_CYRL_RS  = "sr_Cyrl_RS"
	//LOCALES_SR_CYRL_XK  = "sr_Cyrl_XK"
	//LOCALES_SR_LATN     = "sr_Latn"
	//LOCALES_SR_LATN_BA  = "sr_Latn_BA"
	//LOCALES_SR_LATN_ME  = "sr_Latn_ME"
	//LOCALES_SR_LATN_RS  = "sr_Latn_RS"
	//LOCALES_SR_LATN_XK  = "sr_Latn_XK"
	//LOCALES_SV          = "sv"
	//LOCALES_SV_AX       = "sv_AX"
	//LOCALES_SV_FI       = "sv_FI"
	//LOCALES_SV_SE       = "sv_SE"
	//LOCALES_SW          = "sw"
	//LOCALES_SW_CD       = "sw_CD"
	//LOCALES_SW_KE       = "sw_KE"
	//LOCALES_SW_TZ       = "sw_TZ"
	//LOCALES_SW_UG       = "sw_UG"
	//LOCALES_TA          = "ta"
	//LOCALES_TA_IN       = "ta_IN"
	//LOCALES_TA_LK       = "ta_LK"
	//LOCALES_TA_MY       = "ta_MY"
	//LOCALES_TA_SG       = "ta_SG"
	//LOCALES_TE          = "te"
	//LOCALES_TE_IN       = "te_IN"
	//LOCALES_TEO         = "teo"
	//LOCALES_TEO_KE      = "teo_KE"
	//LOCALES_TEO_UG      = "teo_UG"
	//LOCALES_TG          = "tg"
	//LOCALES_TG_TJ       = "tg_TJ"
	//LOCALES_TH          = "th"
	//LOCALES_TH_TH       = "th_TH"
	//LOCALES_TI          = "ti"
	//LOCALES_TI_ER       = "ti_ER"
	//LOCALES_TI_ET       = "ti_ET"
	//LOCALES_TK          = "tk"
	//LOCALES_TK_TM       = "tk_TM"
	//LOCALES_TO          = "to"
	//LOCALES_TO_TO       = "to_TO"
	//LOCALES_TR          = "tr"
	//LOCALES_TR_CY       = "tr_CY"
	//LOCALES_TR_TR       = "tr_TR"
	//LOCALES_TT          = "tt"
	//LOCALES_TT_RU       = "tt_RU"
	//LOCALES_TWQ         = "twq"
	//LOCALES_TWQ_NE      = "twq_NE"
	//LOCALES_TZM         = "tzm"
	//LOCALES_TZM_MA      = "tzm_MA"
	//LOCALES_UG          = "ug"
	//LOCALES_UG_CN       = "ug_CN"
	//LOCALES_UK          = "uk"
	//LOCALES_UK_UA       = "uk_UA"
	//LOCALES_UR          = "ur"
	//LOCALES_UR_IN       = "ur_IN"
	//LOCALES_UR_PK       = "ur_PK"
	//LOCALES_UZ          = "uz"
	//LOCALES_UZ_ARAB     = "uz_Arab"
	//LOCALES_UZ_ARAB_AF  = "uz_Arab_AF"
	//LOCALES_UZ_CYRL     = "uz_Cyrl"
	//LOCALES_UZ_CYRL_UZ  = "uz_Cyrl_UZ"
	//LOCALES_UZ_LATN     = "uz_Latn"
	//LOCALES_UZ_LATN_UZ  = "uz_Latn_UZ"
	//LOCALES_VAI         = "vai"
	//LOCALES_VAI_LATN    = "vai_Latn"
	//LOCALES_VAI_LATN_LR = "vai_Latn_LR"
	//LOCALES_VAI_VAII    = "vai_Vaii"
	//LOCALES_VAI_VAII_LR = "vai_Vaii_LR"
	//LOCALES_VI          = "vi"
	//LOCALES_VI_VN       = "vi_VN"
	//LOCALES_VO          = "vo"
	//LOCALES_VO_001      = "vo_001"
	//LOCALES_VUN         = "vun"
	//LOCALES_VUN_TZ      = "vun_TZ"
	//LOCALES_WAE         = "wae"
	//LOCALES_WAE_CH      = "wae_CH"
	//LOCALES_WO          = "wo"
	//LOCALES_WO_SN       = "wo_SN"
	//LOCALES_XOG         = "xog"
	//LOCALES_XOG_UG      = "xog_UG"
	//LOCALES_YAV         = "yav"
	//LOCALES_YAV_CM      = "yav_CM"
	//LOCALES_YI          = "yi"
	//LOCALES_YI_001      = "yi_001"
	//LOCALES_YO          = "yo"
	//LOCALES_YO_BJ       = "yo_BJ"
	//LOCALES_YO_NG       = "yo_NG"
	//LOCALES_YUE         = "yue"
	//LOCALES_YUE_HK      = "yue_HK"
	//LOCALES_YUE_HANS    = "yue_Hans"
	//LOCALES_YUE_HANS_CN = "yue_Hans_CN"
	//LOCALES_YUE_HANT    = "yue_Hant"
	//LOCALES_YUE_HANT_HK = "yue_Hant_HK"
	//LOCALES_ZGH         = "zgh"
	//LOCALES_ZGH_MA      = "zgh_MA"
	LOCALES_ZH         = "zh"
	LOCALES_ZH_HANS    = "zh_Hans"
	LOCALES_ZH_HANS_CN = "zh_Hans_CN"
	LOCALES_ZH_HANS_HK = "zh_Hans_HK"
	LOCALES_ZH_HANS_MO = "zh_Hans_MO"
	LOCALES_ZH_HANS_SG = "zh_Hans_SG"
	LOCALES_ZH_HANT    = "zh_Hant"
	LOCALES_ZH_HANT_HK = "zh_Hant_HK"
	LOCALES_ZH_HANT_MO = "zh_Hant_MO"
	LOCALES_ZH_HANT_TW = "zh_Hant_TW"
	//LOCALES_ZU         = "zu"
	//LOCALES_ZU_ZA      = "zu_ZA"
)
