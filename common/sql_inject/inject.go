package SQLInject

import (
	"fmt"
	"regexp"

	"github.com/go-playground/validator/v10"
)

// FilteredSQLInject .
// 正则过滤sql注入的方法
// 参数 : 要匹配的语句
func RegexpMatchString(toMatchStr string) bool {
	// 过滤 '
	// ORACLE 注解 --  /**/
	// 关键字过滤 update ,delete
	// 正则的字符串, 不能用 " " 因为" "里面的内容会转义
	// str := `=|;|'|--|(/\\*(.|[\\n\\r])*?\\*/)` // OK!!!
	// str := `=|;|'|--` // OK!!!
	str := `=|;|--` // OK!!!

	// Test regexp string
	// str := `(?:')|(?:--)|(/\\*(?:.|[\\n\\r])*?\\*/)|(\b(select|update|and|or|delete|insert|trancate|char|chr|into|substr|ascii|declare|exec|count|master|into|drop|execute)\b)`
	// str := `(?:=)|(?:')|(?:--)|(/\\*(?:.|[\\n\\r])*?\\*/)`
	// str := `((?!\+))-{2}((?!\+))` // test
	re, err := regexp.Compile(str)
	if err != nil {
		// panic(err.Error())
		return false
	}
	return re.MatchString(toMatchStr)
}

func FilteredSQLInject(v interface{}) error {
	var sqlstr string
	switch v.(type) {
	case string:
		sqlstr = fmt.Sprintf("%s", v)
		if RegexpMatchString(sqlstr) {
			return fmt.Errorf("Injection illustrates suspect\n")
		}
		return nil
	default:
		return nil
	}
}

func ValidateSQL(fl validator.FieldLevel) bool {
	// 过滤 '
	// ORACLE 注解 --  /**/
	// 关键字过滤 update ,delete
	// 正则的字符串, 不能用 " " 因为" "里面的内容会转义
	str := `(?:')|(?:--)|(/\\*(?:.|[\\n\\r])*?\\*/)|(\b(select|update|and|or|delete|insert|trancate|char|chr|into|substr|ascii|declare|exec|count|master|into|drop|execute)\b)`
	re, err := regexp.Compile(str)
	if err != nil {
		return false
	}
	return re.MatchString(fl.Field().String())
}
