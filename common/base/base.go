package base

// Description: In User Settings Edit
// Author: ch<PERSON><PERSON> (<EMAIL>)
// Date: 2019-08-19 11:07:19
// LastEditors: Jeff
// LastEditTime: 2019-11-05 16:11:26

import (
	"git.platform.io/environment/environment/common/validate"
	"os"
	"strconv"
	"unsafe"

)

// Str2Bytes : string transfer to bytes
// Param : string
// Return : []byte
func Str2Bytes(s string) []byte {
	x := (*[2]uintptr)(unsafe.Pointer(&s))
	h := [3]uintptr{x[0], x[1], x[1]}
	return *(*[]byte)(unsafe.Pointer(&h))
}

// Bytes2Str : bytes transfer to string
// Param : []byte
// Return : string
func Bytes2Str(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

// BuildPagingDataSet : Build Paging DataSet
// Param :
// Return :
func BuildPagingDataSet(total int, data interface{}) *DataSet {
	dataSet := DataSet{
		Total: total,
		Data:  data,
	}
	return &dataSet
}

// StringInList : Whether there is a specified string in the string array
// Param : Param1:key string; Param2:src array
// Return : true or false
func StringInList(key string, strList []string) bool {
	for _, k := range strList {
		if k == key {
			return true
		}
	}
	return false
}

func DuplicateEleInStrList(strList []string) ([]string, bool) {
	var duplicateList []string
	for i := range strList {
		for j := range strList[i+1:] {
			if strList[i] == strList[i+1+j] {
				duplicateList = append(duplicateList, strList[i])
				// return &strList[i], true
			}
		}
	}

	if len(duplicateList) > 0 {
		return duplicateList, true
	}

	return nil, false
}

// Uint64InList : Whether there is a specified uint64 in the number array
// Param : Param1:key number; Param2:src number array
// Return : true or false
func Uint64InList(key uint64, list []uint64) bool {
	for _, v := range list {
		if key == v {
			return true
		}
	}
	return false
}

// EnvSetString : set env
// Param :
// Return :
func EnvSetString(key, defaultValue string) error {
	err := os.Setenv(key, defaultValue)
	if err != nil {
		return err
	}
	return nil
}

// EnvDefaultString : get env (string)
// Param :
// Return :
func EnvDefaultString(key, defaultValue string) string {
	v := os.Getenv(key)
	if v == "" {
		return defaultValue
	}
	return v
}

// EnvDefaultInt : get env (int)
// Param :
// Return :
func EnvDefaultInt(key string, defaultValue int) int {
	v := os.Getenv(key)
	if v == "" {
		return defaultValue
	}
	n, err := strconv.Atoi(v)
	if err != nil {
		return defaultValue
	}
	return n
}

func LanguageConvert(l string) string {
	switch l {
	case "zh-CN":
		return validate.LOCALES_ZH
	default:
		return validate.LOCALES_EN
	}
}

// BuildCustomerPagingDataSet : Build Paging DataSet
// Param :
// Return :
func BuildPagingCustomerDataSet(total int, data interface{}, attr map[string]interface{}) *CustomerDataSet {
	dataSet := CustomerDataSet{
		Total: total,
		Attr:  attr,
		Data:  data,
	}
	return &dataSet
}
