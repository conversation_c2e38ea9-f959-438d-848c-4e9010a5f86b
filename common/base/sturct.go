package base

import (
	"time"
)

type CommData struct {
	GroupID uint64 `json:"groupId" validate:"omitempty,gte=1,required"`
}

// swagger:response QueryDataObjectResponse
type QueryDataObjectResponse struct {
	// in: body
	Body struct {
		ErrorCode string `json:"errorCode"`
		Message   string `json:"errorMsg"`

		// in: body
		// The body of response
		Data DataSet `json:"response"`
	}
}

//
// // swagger:response QueryImportInfoResponse
// type QueryImportInfoResponse struct {
// 	// in: body
// 	Body struct {
// 		ErrorCode string `json:"errorCode"`
// 		Message   string `json:"errorMsg"`
//
// 		// in: body
// The body of response
// 		Data DataSet `json:"response"`
// 	}
// }
//
// // swagger:response QueryCheckInfoResponse
// type QueryCheckInfoResponse struct {
// 	// in: body
// 	Body struct {
// 		ErrorCode string `json:"errorCode"`
// 		Message   string `json:"errorMsg"`
//
// 		// in: body
// The body of response
// 		Data DataSet `json:"response"`
// 	}
// }

type CustomerDataSet struct {
	Total int                    `json:"total"`
	Attr  map[string]interface{} `json:"attr,omitempty"`
	Data  interface{}            `json:"data"`
}

type DataSet struct {
	Total int         `json:"total"`
	Data  interface{} `json:"data"`
}

type GovDataSet struct {
	Total int         `json:"total"`
	Data  interface{} `json:"data"`
}

type Result struct {
	ErrorCode string      `json:"errorCode"`
	Message   string      `json:"errorMsg"`
	Data      interface{} `json:"response"`
}

type BaseFilter struct {
	PageIndex int    `json:"pageIndex"`
	PageSize  int    `json:"pageSize"`
	OrderBy   string `json:"orderBy"`
	Sort      int    `json:"sort"`
}

type TimeRange struct {
	From time.Time `json:"from"`
	To   time.Time `json:"to"`
}

type PermissionFilter struct {
	BaseFilter
	ID        uint64    `json:"id"`
	Role      uint64    `json:"role"`
	Method    string    `json:"api"`
	Describe  string    `json:"describe"`
	CreatedAt TimeRange `json:"createdAt"`
}
